<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>食材溯源信息</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .trace-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .trace-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .trace-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .trace-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .trace-content {
            padding: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-start: 4px solid #007bff;
        }
        
        .info-card.supplier {
            border-start-color: #28a745;
        }
        
        .info-card.school {
            border-start-color: #ffc107;
        }
        
        .info-card.usage {
            border-start-color: #dc3545;
        }
        
        .info-card h5 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .info-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .usage-record {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        
        .usage-record:last-child {
            margin-bottom: 0;
        }
        
        .error-container {
            text-align: center;
            padding: 50px;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-good {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .qr-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .qr-footer small {
            color: #6c757d;
        }
        
        @d-flex (max-width: 768px) {
            .trace-container {
                margin: 0 10px;
            }
            
            .trace-header {
                padding: 20px;
            }
            
            .trace-header h1 {
                font-size: 1.5rem;
            }
            
            .trace-content {
                padding: 20px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                margin-top: 5px;
            }
        }
    
        body {
            font-family: "Microsoft YaHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px 0;
        }
        
        .trace-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            /* box-shadow: 0 10px 30px rgba(0,0,0,0.2); */ /* 移除阴影效果 */
            overflow: hidden;
        }
        
        .trace-header {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .trace-header h1 {
            margin: 0;
            font-size: 2rem;
            font-weight: 600;
        }
        
        .trace-header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
        }
        
        .trace-content {
            padding: 30px;
        }
        
        .info-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            border-start: 4px solid #007bff;
        }
        
        .info-card.supplier {
            border-start-color: #28a745;
        }
        
        .info-card.school {
            border-start-color: #ffc107;
        }
        
        .info-card.usage {
            border-start-color: #dc3545;
        }
        
        .info-card h5 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .info-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        
        .info-row:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 500;
            color: #6c757d;
        }
        
        .info-value {
            font-weight: 600;
            color: #2c3e50;
        }
        
        .usage-record {
            background: white;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border: 1px solid #e9ecef;
        }
        
        .usage-record:last-child {
            margin-bottom: 0;
        }
        
        .error-container {
            text-align: center;
            padding: 50px;
        }
        
        .error-icon {
            font-size: 4rem;
            color: #dc3545;
            margin-bottom: 20px;
        }
        
        .status-badge {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .status-good {
            background: #d4edda;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-danger {
            background: #f8d7da;
            color: #721c24;
        }
        
        .qr-footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #e9ecef;
        }
        
        .qr-footer small {
            color: #6c757d;
        }
        
        @d-flex (max-width: 768px) {
            .trace-container {
                margin: 0 10px;
            }
            
            .trace-header {
                padding: 20px;
            }
            
            .trace-header h1 {
                font-size: 1.5rem;
            }
            
            .trace-content {
                padding: 20px;
            }
            
            .info-row {
                flex-direction: column;
                align-items: flex-start;
            }
            
            .info-value {
                margin-top: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="trace-container">
        <div class="trace-header">
            <h1><i class="fas fa-leaf"></i> 食材溯源信息</h1>
            <p>扫描二维码查看完整的食材供应链信息</p>
        </div>
        
        <div class="trace-content">
            {% if error %}
                <div class="error-container">
                    <div class="error-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <h3>溯源信息获取失败</h3>
                    <p class="text-muted">{{ error }}</p>
                </div>
            {% else %}
                <!-- 食材基本信息 -->
                {% if ingredient_info %}
                <div class="info-card">
                    <h5><i class="fas fa-seedling"></i> 食材基本信息</h5>
                    <div class="info-row">
                        <span class="info-label">食材名称</span>
                        <span class="info-value">{{ ingredient_info.name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">批次号</span>
                        <span class="info-value">{{ ingredient_info.batch_number }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">入库数量</span>
                        <span class="info-value">{{ ingredient_info.quantity }}{{ ingredient_info.unit }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">入库日期</span>
                        <span class="info-value">{{ ingredient_info.stock_in_date or '未知' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">生产日期</span>
                        <span class="info-value">{{ ingredient_info.production_date or '未知' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">过期日期</span>
                        <span class="info-value">{{ ingredient_info.expiry_date or '未知' }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">质量状态</span>
                        <span class="info-value">
                            <span class="status-badge status-good">{{ ingredient_info.quality_status }}</span>
                        </span>
                    </div>
                </div>
                {% endif %}

                <!-- 供应商信息 -->
                {% if supplier_info %}
                <div class="info-card supplier">
                    <h5><i class="fas fa-truck"></i> 供应商信息</h5>
                    <div class="info-row">
                        <span class="info-label">供应商名称</span>
                        <span class="info-value">{{ supplier_info.name }}</span>
                    </div>
                    {% if supplier_info.contact_person %}
                    <div class="info-row">
                        <span class="info-label">联系人</span>
                        <span class="info-value">{{ supplier_info.contact_person }}</span>
                    </div>
                    {% endif %}
                    {% if supplier_info.phone %}
                    <div class="info-row">
                        <span class="info-label">联系电话</span>
                        <span class="info-value">{{ supplier_info.phone }}</span>
                    </div>
                    {% endif %}
                    {% if supplier_info.address %}
                    <div class="info-row">
                        <span class="info-label">地址</span>
                        <span class="info-value">{{ supplier_info.address }}</span>
                    </div>
                    {% endif %}
                </div>
                {% endif %}

                <!-- 学校信息 -->
                {% if school_info %}
                <div class="info-card school">
                    <h5><i class="fas fa-school"></i> 学校信息</h5>
                    <div class="info-row">
                        <span class="info-label">学校名称</span>
                        <span class="info-value">{{ school_info.name }}</span>
                    </div>
                    <div class="info-row">
                        <span class="info-label">存储仓库</span>
                        <span class="info-value">{{ school_info.warehouse }}</span>
                    </div>
                </div>
                {% endif %}

                <!-- 使用记录 -->
                {% if usage_info and usage_info|length > 0 %}
                <div class="info-card usage">
                    <h5><i class="fas fa-utensils"></i> 使用记录</h5>
                    {% for usage in usage_info %}
                    <div class="usage-record">
                        <div class="info-row">
                            <span class="info-label">使用日期</span>
                            <span class="info-value">{{ usage.consumption_date or '未知' }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">餐次</span>
                            <span class="info-value">{{ usage.meal_type or '未知' }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">使用数量</span>
                            <span class="info-value">{{ usage.quantity }}{{ usage.unit }}</span>
                        </div>
                        <div class="info-row">
                            <span class="info-label">使用学校</span>
                            <span class="info-value">{{ usage.school or '未知' }}</span>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="info-card usage">
                    <h5><i class="fas fa-utensils"></i> 使用记录</h5>
                    <p class="text-muted text-center">暂无使用记录</p>
                </div>
                {% endif %}
            {% endif %}
        </div>
        
        <div class="qr-footer">
            <small>
                <i class="fas fa-shield-alt"></i>
                此信息由校园餐智慧食堂平台提供，确保食材安全可追溯
            </small>
        </div>
    </div>
</body>
</html>
