#!/usr/bin/env python3
"""
批量移除CSS文件中的阴影效果脚本
移除所有text-shadow、box-shadow、backdrop-filter、filter: blur等模糊阴影效果
"""

import os
import re
from pathlib import Path

def remove_shadows_from_file(file_path):
    """移除单个文件中的阴影效果"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 1. 移除 box-shadow 属性
        box_shadow_pattern = r'(\s*)(box-shadow:\s*[^;]+;)'
        matches = re.findall(box_shadow_pattern, content)
        for indent, shadow_rule in matches:
            # 注释掉box-shadow
            commented_rule = f"{indent}/* {shadow_rule} */ /* 移除阴影效果 */"
            content = content.replace(f"{indent}{shadow_rule}", commented_rule)
            changes_made += 1
        
        # 2. 移除 text-shadow 属性
        text_shadow_pattern = r'(\s*)(text-shadow:\s*[^;]+;)'
        matches = re.findall(text_shadow_pattern, content)
        for indent, shadow_rule in matches:
            # 注释掉text-shadow
            commented_rule = f"{indent}/* {shadow_rule} */ /* 移除文字阴影效果 */"
            content = content.replace(f"{indent}{shadow_rule}", commented_rule)
            changes_made += 1
        
        # 3. 移除 backdrop-filter 属性
        backdrop_filter_pattern = r'(\s*)(backdrop-filter:\s*[^;]+;)'
        matches = re.findall(backdrop_filter_pattern, content)
        for indent, filter_rule in matches:
            # 注释掉backdrop-filter
            commented_rule = f"{indent}/* {filter_rule} */ /* 移除背景模糊效果 */"
            content = content.replace(f"{indent}{filter_rule}", commented_rule)
            changes_made += 1
        
        # 4. 移除 filter: blur() 属性
        filter_blur_pattern = r'(\s*)(filter:\s*[^;]*blur[^;]*;)'
        matches = re.findall(filter_blur_pattern, content)
        for indent, filter_rule in matches:
            # 注释掉filter: blur
            commented_rule = f"{indent}/* {filter_rule} */ /* 移除滤镜模糊效果 */"
            content = content.replace(f"{indent}{filter_rule}", commented_rule)
            changes_made += 1
        
        # 5. 移除 drop-shadow 属性
        drop_shadow_pattern = r'(\s*)(filter:\s*[^;]*drop-shadow[^;]*;)'
        matches = re.findall(drop_shadow_pattern, content)
        for indent, filter_rule in matches:
            # 注释掉drop-shadow
            commented_rule = f"{indent}/* {filter_rule} */ /* 移除投影效果 */"
            content = content.replace(f"{indent}{filter_rule}", commented_rule)
            changes_made += 1
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return 0
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return 0

def main():
    """主函数"""
    print("🔄 批量移除CSS文件中的阴影效果")
    print("=" * 50)
    
    # CSS文件目录
    css_dir = Path("app/static/css")
    
    if not css_dir.exists():
        print("❌ CSS目录不存在")
        return
    
    total_files = 0
    total_changes = 0
    
    # 处理所有CSS文件
    for css_file in css_dir.glob("*.css"):
        changes = remove_shadows_from_file(css_file)
        if changes > 0:
            total_files += 1
            total_changes += changes
            print(f"✅ 处理文件: {css_file.name} - 移除了 {changes} 个阴影效果")
    
    print("\n" + "=" * 50)
    print(f"📊 处理完成:")
    print(f"   修改文件: {total_files} 个")
    print(f"   移除阴影: {total_changes} 个")
    
    if total_changes > 0:
        print("\n✨ 所有文字模糊阴影效果已成功移除！")
    else:
        print("\n✅ 没有发现需要移除的阴影效果")

if __name__ == "__main__":
    main()
