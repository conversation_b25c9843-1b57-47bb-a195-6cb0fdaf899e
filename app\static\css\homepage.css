/**
 * 首页专用样式文件
 * 替代外部CDN资源，提升加载速度
 */

/* 基础重置和字体 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
  line-height: 1.6;
  color: #fff;
  background-color: #0B0E2F;
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 颜色变量 */
:root {
  --primary: #165DFF;
  --secondary: #36CBCB;
  --accent: #722ED1;
  --dark: #1D2129;
  --light: #F7F8FA;
  --primary-light: #E8F3FF;
  --primary-dark: #0D47A1;
  --neon-blue: #00BFFF;
  --neon-purple: #9D4EDD;
  --neon-green: #00FF9D;
  --dark-blue: #0B0E2F;
}

/* 容器和布局 */
.container {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-padding {
  padding: 4rem 0 6rem;
}

/* 网格系统 */
.grid {
  display: grid;
  gap: 1.5rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

/* Flexbox */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

/* 尺寸 */
.w-full { width: 100%; }
.w-1\/2 { width: 50%; }
.w-10 { width: 2.5rem; }
.w-14 { width: 3.5rem; }
.h-10 { height: 2.5rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-48 { height: 12rem; }

/* 间距 */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.px-8 { padding-left: 2rem; padding-right: 2rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.py-12 { padding-top: 3rem; padding-bottom: 3rem; }
.py-16 { padding-top: 4rem; padding-bottom: 4rem; }
.pt-24 { padding-top: 6rem; }
.pt-32 { padding-top: 8rem; }
.pb-16 { padding-bottom: 4rem; }
.pb-24 { padding-bottom: 6rem; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.mb-8 { margin-bottom: 2rem; }
.mb-10 { margin-bottom: 2.5rem; }
.mb-12 { margin-bottom: 3rem; }
.mb-16 { margin-bottom: 4rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-12 { margin-top: 3rem; }
.ms-2 { margin-left: 0.5rem; }

/* 文字 */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-4xl { font-size: 2.25rem; }
.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }
.text-center { text-align: center; }
.text-white { color: #fff; }
.text-gray-300 { color: #D1D5DB; }
.text-gray-400 { color: #9CA3AF; }
.text-gray-500 { color: #6B7280; }
.leading-tight { line-height: 1.25; }

/* 背景 */
.bg-transparent { background-color: transparent; }
.bg-dark-blue { background-color: var(--dark-blue); }
.bg-primary { background-color: var(--primary); }

/* 边框 */
.border { border-width: 1px; }
.border-primary\/20 { border-color: rgba(22, 93, 255, 0.2); }
.border-neon-blue\/50 { border-color: rgba(0, 191, 255, 0.5); }
.border-neon-green\/50 { border-color: rgba(0, 255, 157, 0.5); }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 - 已移除所有阴影效果 */
/* .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); } */
/* .shadow-2xl { box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); } */

/* 过渡动画 */
.transition-all { transition: all 0.3s ease; }
.transition-colors { transition: color 0.3s ease, background-color 0.3s ease; }
.transition-transform { transition: transform 0.3s ease; }

/* 特殊效果 - 已移除所有模糊效果 */
/* .backdrop-blur-sm { backdrop-filter: blur(4px); } */
/* .backdrop-blur-md { backdrop-filter: blur(12px); } */

/* .neon-glow {
  box-shadow: 0 0 10px rgba(22, 93, 255, 0.5),
              0 0 20px rgba(22, 93, 255, 0.3),
              0 0 30px rgba(22, 93, 255, 0.1);
} */ /* 移除霓虹发光效果 */

.neon-text {
  color: #3B82F6;
  /* text-shadow: 0 0 10px rgba(59, 130, 246, 0.5); */ /* 注释掉文字阴影，避免模糊效果 */
}

.text-neon-blue { color: var(--neon-blue); }
.text-neon-green { color: var(--neon-green); }
.text-neon-purple { color: var(--neon-purple); }

/* 动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* 背景网格 */
.bg-grid {
  position: fixed;
  inset: 0;
  background-image: linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  background-size: 20px 20px;
  z-index: 0;
  opacity: 0.5;
}

/* 渐变背景 */
.bg-gradient {
  background: linear-gradient(135deg, #0B0E2F 0%, #1A1B3A 50%, #2D1B69 100%);
  position: relative;
  overflow: hidden;
}

/* 卡片悬停效果 */
.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 10px 30px rgba(22, 93, 255, 0.1); */ /* 移除阴影效果 */
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #3B82F6, #8B5CF6);
  color: #fff;
  /* box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3); */ /* 移除阴影效果 */
}

.btn-primary:hover {
  transform: translateY(-2px);
  /* box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4); */ /* 移除阴影效果 */
}

.btn-outline {
  background: rgba(255, 255, 255, 0.1);
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-outline:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* 导航栏 */
.navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 50;
  background: rgba(11, 14, 47, 0.95);
  /* backdrop-filter: blur(12px); */ /* 移除背景模糊效果 */
  border-bottom: 1px solid rgba(22, 93, 255, 0.2);
}

.navbar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #fff;
}

.navbar-nav {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  color: #fff;
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-link:hover {
  color: #3B82F6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .container {
    padding: 0 1.5rem;
  }
  
  .grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
  
  .flex-col {
    flex-direction: column;
  }
  
  .text-4xl {
    font-size: 2rem;
  }
  
  .pt-32 {
    padding-top: 6rem;
  }
  
  .section-padding {
    padding: 3rem 0 4rem;
  }

  .btn {
    padding: 0.5rem 1rem;
  }

  .card {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .px-8 {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}

/* 轮播图样式 */
.carousel-container {
  position: relative;
  width: 100%;
  height: 280px;
  border-radius: 1rem;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.1);
}

.carousel-inner {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.6s ease-in-out;
}

.carousel-item.active {
  opacity: 1;
}

.carousel-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 1rem;
}

.carousel-caption {
  position: absolute;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 1rem;
  border-radius: 0.5rem;
  /* backdrop-filter: blur(4px); */ /* 移除背景模糊效果 */
}

.carousel-caption h5 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.carousel-caption p {
  margin: 0;
  font-size: 0.875rem;
  opacity: 0.9;
}

/* 轮播指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
  z-index: 10;
}

.carousel-indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.8);
  background: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.7;
}

.carousel-indicator.active {
  background: var(--neon-blue);
  border-color: var(--neon-blue);
  transform: scale(1.2);
  opacity: 1;
}

/* 轮播控制按钮 */
.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 45px;
  height: 45px;
  background: rgba(0, 0, 0, 0.4);
  border: none;
  border-radius: 50%;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  opacity: 0.8;
  z-index: 10;
}

.carousel-control:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.1);
  opacity: 1;
}

.carousel-control-prev {
  left: 15px;
}

.carousel-control-next {
  right: 15px;
}

.carousel-control-icon {
  width: 18px;
  height: 18px;
  display: inline-block;
}

.carousel-control-prev-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m11.354 1.646a.5.5 0 0 1 0 .708L5.707 8l5.647 5.646a.5.5 0 0 1-.708.708l-6-6a.5.5 0 0 1 0-.708l6-6a.5.5 0 0 1 .708 0z'/%3e%3c/svg%3e");
}

.carousel-control-next-icon {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='m4.646 1.646a.5.5 0 0 1 .708 0l6 6a.5.5 0 0 1 0 .708l-6 6a.5.5 0 0 1-.708-.708L10.293 8 4.646 2.354a.5.5 0 0 1 0-.708z'/%3e%3c/svg%3e");
}

/* 加载和空状态 */
.carousel-loading,
.carousel-empty {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 1rem;
  color: white;
}

.spinner {
  width: 3rem;
  height: 3rem;
  border: 0.3em solid rgba(255, 255, 255, 0.3);
  border-top: 0.3em solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* 隐藏类 */
.hidden {
  display: none;
}

.md\:hidden {
  display: block;
}

@media (min-width: 768px) {
  .md\:hidden {
    display: none;
  }

  .md\:flex {
    display: flex;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  /* 轮播图响应式 */
  .carousel-container {
    height: 280px;
  }
}

@media (max-width: 768px) {
  .carousel-container {
    height: 220px;
    border-radius: 1rem;
  }

  .carousel-control {
    width: 35px;
    height: 35px;
  }

  .carousel-control-prev {
    left: 10px;
  }

  .carousel-control-next {
    right: 10px;
  }

  .carousel-indicator {
    width: 10px;
    height: 10px;
  }

  .carousel-caption {
    bottom: 10px;
    left: 10px;
    right: 10px;
    padding: 0.75rem;
  }

  .carousel-caption h5 {
    font-size: 1rem;
  }

  .carousel-caption p {
    font-size: 0.75rem;
  }
}

@media (min-width: 1024px) {
  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }
}

/* 卡片样式 */
.card {
  background: rgba(29, 33, 41, 0.3);
  /* backdrop-filter: blur(12px); */ /* 移除背景模糊效果 */
  border: 1px solid rgba(22, 93, 255, 0.2);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
}

/* 表单样式 */
.form-input {
  width: 100%;
  padding: 1rem 1.5rem;
  background: rgba(0, 0, 0, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 0.75rem;
  color: #fff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.form-input:focus {
  outline: none;
  border-color: #3B82F6;
  /* box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2); */ /* 移除阴影效果 */
}

.form-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

/* 图标样式 */
.icon {
  width: 3rem;
  height: 3rem;
  background: rgba(22, 93, 255, 0.2);
  border-radius: 0.75rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 1.5rem;
  transition: all 0.3s ease;
}

.icon:hover {
  transform: scale(1.05);
  background: rgba(22, 93, 255, 0.3);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(29, 33, 41, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.5);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.7);
}

/* 主横幅 */
.hero {
  padding: 120px 0 60px;
  background: linear-gradient(135deg, #165DFF, #0D47A1);
  color: #fff;
  text-align: center;
}

.hero-content {
  max-width: 800px;
  margin: 0 auto;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.hero p {
  font-size: 1.2rem;
  margin-bottom: 30px;
  opacity: 0.9;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
}

/* 功能特点 */
.features {
  padding: 80px 0;
  background: #fff;
}

.features h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 50px;
}

.feature-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.feature-card {
  padding: 30px;
  text-align: center;
  background: #fff;
  border-radius: 10px;
  /* box-shadow: 0 4px 6px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
  transition: transform 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}

.feature-card i {
  font-size: 2.5rem;
  color: #165DFF;
  margin-bottom: 20px;
}

.feature-card h3 {
  margin-bottom: 15px;
  font-size: 1.5rem;
}

/* 联系我们 */
.contact {
  padding: 80px 0;
  background: #f5f5f5;
}

.contact h2 {
  text-align: center;
  font-size: 2rem;
  margin-bottom: 50px;
}

.contact-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 50px;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 15px;
}

.info-item i {
  font-size: 1.5rem;
  color: #165DFF;
}

.contact-form {
  margin-top: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: all 0.3s;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #165DFF;
  /* box-shadow: 0 0 0 3px rgba(22, 93, 255, 0.1); */ /* 移除阴影效果 */
}

.form-group textarea {
  height: 150px;
  resize: vertical;
}

/* 消息提示样式 */
.message {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 8px;
  background: white;
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
  transform: translateX(120%);
  transition: transform 0.3s ease-out;
  z-index: 1000;
}

.message.show {
  transform: translateX(0);
}

.message-success {
  background: #e6f7e6;
  border: 1px solid #00c853;
  color: #00c853;
}

.message-error {
  background: #ffebee;
  border: 1px solid #f44336;
  color: #f44336;
}

.message-info {
  background: #e3f2fd;
  border: 1px solid #2196f3;
  color: #2196f3;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .contact-form {
    margin-top: 1.5rem;
  }
  
  .form-group {
    margin-bottom: 1rem;
  }
  
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 10px;
    font-size: 0.95rem;
  }
  
  .message {
    left: 20px;
    right: 20px;
    text-align: center;
  }
}

/* 页脚 */
.footer {
  background: #333;
  color: #fff;
  padding: 40px 0;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info p {
  margin-bottom: 10px;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  color: #fff;
  font-size: 1.5rem;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #165DFF;
}

/* 视频教程区域 */
.tutorials {
    padding: 80px 0;
    background: #f8f9fa;
}

.tutorials h2 {
    text-align: center;
    font-size: 2.5rem;
    margin-bottom: 1rem;
    color: #1a1a1a;
}

.section-desc {
    text-align: center;
    color: #666;
    font-size: 1.1rem;
    margin-bottom: 3rem;
}

.tutorial-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.tutorial-card {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    /* box-shadow: 0 4px 6px rgba(0,0,0,0.05); */ /* 移除阴影效果 */
    transition: all 0.3s ease;
    cursor: pointer;
}

.tutorial-card:hover {
    transform: translateY(-5px);
    /* box-shadow: 0 8px 15px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
}

.tutorial-thumbnail {
    position: relative;
    padding-top: 56.25%; /* 16:9 宽高比 */
    background: #000;
}

.tutorial-thumbnail img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60px;
    height: 60px;
    background: rgba(22, 93, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.play-button i {
    color: white;
    font-size: 24px;
    margin-left: 4px;
}

.tutorial-card:hover .play-button {
    background: #165DFF;
    transform: translate(-50%, -50%) scale(1.1);
}

.tutorial-info {
    padding: 20px;
}

.tutorial-info h3 {
    font-size: 1.25rem;
    margin-bottom: 10px;
    color: #1a1a1a;
}

.tutorial-info p {
    color: #666;
    font-size: 0.95rem;
    margin-bottom: 15px;
}

.duration {
    display: inline-block;
    padding: 4px 8px;
    background: #f0f0f0;
    border-radius: 4px;
    font-size: 0.85rem;
    color: #666;
}

/* 视频模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
}

.modal-content {
    position: relative;
    width: 90%;
    max-width: 1000px;
    margin: 40px auto;
    background: white;
    border-radius: 12px;
    overflow: hidden;
}

.close-button {
    position: absolute;
    top: 15px;
    right: 15px;
    width: 30px;
    height: 30px;
    background: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    z-index: 1;
    font-size: 20px;
    transition: all 0.3s ease;
}

.close-button:hover {
    background: rgba(0, 0, 0, 0.8);
}

.video-container {
    position: relative;
    padding-top: 56.25%; /* 16:9 宽高比 */
    background: #000;
}

.video-container video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.video-info {
    padding: 20px;
}

.video-info h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #1a1a1a;
}

.video-info p {
    color: #666;
    font-size: 1rem;
    line-height: 1.6;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .tutorials {
        padding: 60px 0;
    }

    .tutorials h2 {
        font-size: 2rem;
    }

    .section-desc {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .tutorial-grid {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 20px auto;
    }

    .video-info h3 {
        font-size: 1.25rem;
    }
}
