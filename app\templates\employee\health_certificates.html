{% extends 'base.html' %}

{% block title %}健康证管理 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <h2>健康证管理</h2>
    </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回员工列表
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">员工健康证状态一览</h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>员工姓名</th>
                        <th>部门</th>
                        <th>职位</th>
                        <th>健康证编号</th>
                        <th>发证日期</th>
                        <th>到期日期</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in employee_certs %}
                    <tr>
                        <td>{{ item.employee.name }}</td>
                        <td>{{ item.employee.department }}</td>
                        <td>{{ item.employee.position }}</td>
                        <td>{{ item.certificate.certificate_no if item.certificate else '-' }}</td>
                        <td>{{  item.certificate.issue_date|format_datetime('%Y-%m-%d') if item.certificate else '-'  }}</td>
                        <td>{{  item.certificate.expire_date|format_datetime('%Y-%m-%d') if item.certificate else '-'  }}</td>
                        <td>
                            {% if item.status == "有效" %}
                            <span class="badge bg-success">{{ item.status }}</span>
                            {% elif item.status == "未办理" %}
                            <span class="badge bg-secondary">{{ item.status }}</span>
                            {% elif item.status == "已过期" %}
                            <span class="badge bg-danger">{{ item.status }}</span>
                            {% elif "即将过期" in item.status %}
                            <span class="badge bg-warning">{{ item.status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('employee.view_employee', id=item.employee.id) }}" class="btn btn-info">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=item.employee.id) }}" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> 添加健康证
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="8" class="text-center">暂无员工数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<div class="row mt-4">
    <div class="col-md-4">
        <div class="card bg-danger text-white">
            <div class="card-body">
                <h5 class="card-title">已过期</h5>
                <p class="card-text">
                    <span class="h2">{{  employee_certs|selectattr('status', 'eq', '已过期')|list|length  }}</span> 人
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <h5 class="card-title">即将过期</h5>
                <p class="card-text">
                    <span class="h2">{{  employee_certs|selectattr('status', 'contains', '即将过期')|list|length  }}</span> 人
                </p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card bg-secondary text-white">
            <div class="card-body">
                <h5 class="card-title">未办理</h5>
                <p class="card-text">
                    <span class="h2">{{  employee_certs|selectattr('status', 'eq', '未办理')|list|length  }}</span> 人
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}
