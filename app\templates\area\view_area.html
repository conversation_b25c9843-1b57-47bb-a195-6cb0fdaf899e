{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<!-- 页面标题和面包屑导航 -->
<div class="row mb-4">
    <div class="col-lg-8">
        <div class="d-flex align-items-center mb-2">
            <h2 class="me-3 mb-0">
                <i class="fas fa-map-marker-alt text-primary"></i> {{ area.name }}
            </h2>
            <span class="badge rounded-pill bg-primary">{{ area.get_level_name() }}</span>
            {% if area.status == 1 %}
            <span class="badge rounded-pill bg-success ms-2">启用</span>
            {% else %}
            <span class="badge rounded-pill bg-danger ms-2">禁用</span>
            {% endif %}
        </div>
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb bg-light shadow-sm">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('area.index') }}">
                        <i class="fas fa-home"></i> 区域管理
                    </a>
                </li>
                {% for a in area_path %}
                <li class="breadcrumb-item {% if loop.last %}active{% endif %}">
                    {% if not loop.last %}
                    <a href="{{ url_for('area.view_area', id=a.id) }}">{{ a.name }}</a>
                    {% else %}
                    {{ a.name }}
                    {% endif %}
                </li>
                {% endfor %}
            </ol>
        </nav>
    </div>
    <div class="col-lg-4 text-end">
        <div class="btn-group">
            <a href="{{ url_for('area.switch_area', id=area.id) }}" class="btn btn-success">
                <i class="fas fa-exchange-alt"></i> 切换到此区域
            </a>
            {% if current_user.is_admin() %}
            <a href="{{ url_for('area.edit_area', id=area.id) }}" class="btn btn-primary">
                <i class="fas fa-edit"></i> 编辑
            </a>
            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteAreaModal">
                <i class="fas fa-trash"></i> 删除
            </button>
            {% endif %}
            <a href="{{ url_for('area.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>
</div>

<!-- 统计信息卡片 -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card bg-primary text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">下级区域</h6>
                        <h2 class="mt-2 mb-0">{{  children|length  }}</h2>
                    </div>
                    <i class="fas fa-sitemap fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-success text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">区域用户</h6>
                        <h2 class="mt-2 mb-0">{{  users|length  }}</h2>
                    </div>
                    <i class="fas fa-users fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-info text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">区域代码</h6>
                        <h4 class="mt-2 mb-0">{{ area.code }}</h4>
                    </div>
                    <i class="fas fa-barcode fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card bg-warning text-dark">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title mb-0">变更记录</h6>
                        <h2 class="mt-2 mb-0">{{  history|length  }}</h2>
                    </div>
                    <i class="fas fa-history fa-3x opacity-50"></i>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-primary text-white">
                <div class="d-flex align-items-center">
                    <i class="fas fa-info-circle me-2"></i>
                    <h5 class="mb-0">基本信息</h5>
                </div>
            </div>
            <div class="card-body">
                <div class="area-info-container">
                    <!-- 区域名称 -->
                    <div class="info-item mb-3">
                        <label class="info-label">区域名称</label>
                        <div class="info-value">
                            <h5 class="mb-0 text-dark">{{ area.name }}</h5>
                        </div>
                    </div>

                    <!-- 区域代码 -->
                    <div class="info-item mb-3">
                        <label class="info-label">区域代码</label>
                        <div class="info-value">
                            <code class="bg-light px-3 py-2 rounded border">{{ area.code }}</code>
                        </div>
                    </div>

                    <!-- 区域级别 -->
                    <div class="info-item mb-3">
                        <label class="info-label">区域级别</label>
                        <div class="info-value">
                            <span class="badge bg-primary bg-lg px-3 py-2">{{ area.get_level_name() }}</span>
                        </div>
                    </div>

                    <!-- 上级区域 -->
                    <div class="info-item mb-3">
                        <label class="info-label">上级区域</label>
                        <div class="info-value">
                            {% if area.parent %}
                            <a href="{{ url_for('area.view_area', id=area.parent.id) }}" class="text-primary text-decoration-none">
                                <i class="fas fa-link me-2"></i>{{ area.parent.name }}
                            </a>
                            {% else %}
                            <span class="text-muted">
                                <i class="fas fa-times me-2"></i>无
                            </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 状态 -->
                    <div class="info-item mb-3">
                        <label class="info-label">状态</label>
                        <div class="info-value">
                            {% if area.status == 1 %}
                            <span class="badge bg-success bg-lg px-3 py-2">
                                <i class="fas fa-check me-1"></i>启用
                            </span>
                            {% else %}
                            <span class="badge bg-danger bg-lg px-3 py-2">
                                <i class="fas fa-ban me-1"></i>禁用
                            </span>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 创建时间 -->
                    <div class="info-item mb-3">
                        <label class="info-label">创建时间</label>
                        <div class="info-value">
                            <i class="fas fa-calendar-alt text-muted me-2"></i>
                            <span class="text-dark">{{ area.created_at.strftime('%Y-%m-%d %H:%M:%S') if area.created_at else '未知' }}</span>
                        </div>
                    </div>

                    <!-- 描述 -->
                    <div class="info-item mb-3">
                        <label class="info-label">描述</label>
                        <div class="info-value">
                            <p class="mb-0 text-muted">{{ area.description or '无描述信息' }}</p>
                        </div>
                    </div>

                    {% if area.level == 3 %}
                    <!-- 乡镇级别学校 -->
                    <div class="info-item mb-3">
                        <label class="info-label">乡镇级别学校</label>
                        <div class="info-value">
                            {% if area.is_township_school %}
                            <span class="badge bg-primary bg-lg px-3 py-2">
                                <i class="fas fa-check me-1"></i>是
                            </span>
                            {% else %}
                            <span class="badge bg-secondary bg-lg px-3 py-2">
                                <i class="fas fa-times me-1"></i>否
                            </span>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-8">
        <!-- 下级区域卡片 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-sitemap me-2"></i>
                    <h5 class="mb-0">下级区域</h5>
                </div>
                {% if current_user.is_admin() %}
                <a href="{{ url_for('area.add_area') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> 添加下级区域
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if children %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>名称</th>
                                <th>代码</th>
                                <th>级别</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for child in children %}
                            <tr>
                                <td><strong>{{ child.name }}</strong></td>
                                <td><code>{{ child.code }}</code></td>
                                <td>{{ child.get_level_name() }}</td>
                                <td>
                                    {% if child.status == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check-circle"></i> 启用</span>
                                    {% else %}
                                    <span class="badge bg-danger"><i class="fas fa-ban"></i> 禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ url_for('area.view_area', id=child.id) }}" class="btn btn-info" title="查看详情">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('area.switch_area', id=child.id) }}" class="btn btn-success" title="切换到此区域">
                                            <i class="fas fa-exchange-alt"></i>
                                        </a>
                                        {% if current_user.is_admin() %}
                                        <a href="{{ url_for('area.edit_area', id=child.id) }}" class="btn btn-primary" title="编辑">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteChildModal{{ child.id }}" title="删除">
                                            <i class="fas fa-trash"></i>
                                        </button>

                                        <!-- 删除子区域确认模态框 -->
                                        <div class="modal fade" id="deleteChildModal{{ child.id }}" tabindex="-1" role="dialog" aria-labelledby="deleteChildModalLabel{{ child.id }}" aria-hidden="true">
                                            <div class="modal-dialog" role="document">
                                                <div class="modal-content">
                                                    <div class="modal-header bg-danger text-white">
                                                        <h5 class="modal-title" id="deleteChildModalLabel{{ child.id }}">确认删除区域</h5>
                                                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                    </div>
                                                    <div class="modal-body">
                                                        <p>您确定要删除区域 <strong>{{ child.name }}</strong> 吗？</p>
                                                        <div class="alert alert-warning">
                                                            <i class="fas fa-exclamation-triangle"></i> 警告：
                                                            <ul>
                                                                <li>此操作不可逆，删除后无法恢复</li>
                                                                <li>如果区域包含子区域，将无法删除</li>
                                                                <li>如果区域包含用户，将无法删除</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                    <div class="modal-footer">
                                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                                        <form action="{{ url_for('area.delete_area', id=child.id) }}" method="POST"><button type="submit" class="btn btn-danger">确认删除</button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 该区域暂无下级区域
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 区域用户卡片 -->
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-users me-2"></i>
                    <h5 class="mb-0">区域用户</h5>
                </div>
                {% if current_user.is_admin() %}
                <a href="{{ url_for('system.add_user') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-user-plus"></i> 添加用户
                </a>
                {% elif area.level == 3 and current_user.area_id == area.id and current_user.has_permission('user', 'view') %}
                <a href="{{ url_for('school_admin.users') }}" class="btn btn-sm btn-light">
                    <i class="fas fa-users-cog"></i> 管理学校用户
                </a>
                {% endif %}
            </div>
            <div class="card-body">
                {% if users %}
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>用户名</th>
                                <th>真实姓名</th>
                                <th>角色</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users %}
                            <tr>
                                <td>{{ user.username }}</td>
                                <td><strong>{{ user.real_name or '-' }}</strong></td>
                                <td>
                                    {% for role in user.roles %}
                                    <span class="badge bg-info">{{ role.name }}</span>
                                    {% else %}
                                    <span class="text-muted">无角色</span>
                                    {% endfor %}
                                </td>
                                <td>
                                    {% if user.status == 1 %}
                                    <span class="badge bg-success"><i class="fas fa-check-circle"></i> 启用</span>
                                    {% else %}
                                    <span class="badge bg-danger"><i class="fas fa-ban"></i> 禁用</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        {% if current_user.is_admin() %}
                                        <a href="{{ url_for('system.view_user', id=user.id) }}" class="btn btn-info" title="查看用户">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('system.edit_user', id=user.id) }}" class="btn btn-primary" title="编辑用户">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 该区域暂无用户
                </div>
                {% endif %}
            </div>
        </div>

        <!-- 变更历史卡片 -->
        {% if history %}
        <div class="card mb-4 shadow-sm">
            <div class="card-header bg-warning text-dark d-flex align-items-center">
                <i class="fas fa-history me-2"></i>
                <h5 class="mb-0">变更历史</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped">
                        <thead class="thead-light">
                            <tr>
                                <th>变更类型</th>
                                <th>变更人</th>
                                <th>变更时间</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for record in history %}
                            <tr>
                                <td>
                                    {% if record.change_type == 'create' %}
                                    <span class="badge bg-success"><i class="fas fa-plus-circle"></i> 创建</span>
                                    {% elif record.change_type == 'update' %}
                                    <span class="badge bg-primary"><i class="fas fa-edit"></i> 更新</span>
                                    {% elif record.change_type == 'move' %}
                                    <span class="badge bg-warning"><i class="fas fa-arrows-alt"></i> 移动</span>
                                    {% elif record.change_type == 'delete' %}
                                    <span class="badge bg-danger"><i class="fas fa-trash-alt"></i> 删除</span>
                                    {% endif %}
                                </td>
                                <td>{{ record.user.username }}</td>
                                <td>{{ record.created_at }}</td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#historyModal{{ record.id }}">
                                        <i class="fas fa-info-circle"></i> 查看
                                    </button>

                                    <!-- 变更详情模态框 -->
                                    <div class="modal fade" id="historyModal{{ record.id }}" tabindex="-1" role="dialog" aria-labelledby="historyModalLabel{{ record.id }}" aria-hidden="true">
                                        <div class="modal-dialog modal-lg" role="document">
                                            <div class="modal-content">
                                                <div class="modal-header bg-info text-white">
                                                    <h5 class="modal-title" id="historyModalLabel{{ record.id }}">
                                                        <i class="fas fa-history"></i> 变更详情
                                                    </h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    {% if record.change_type == 'create' %}
                                                    <h6 class="text-success"><i class="fas fa-plus-circle"></i> 新建区域</h6>
                                                    <pre class="bg-light p-3 border rounded">{{ record.new_data }}</pre>
                                                    {% elif record.change_type == 'update' %}
                                                    <div class="row">
                                                        <div class="col-md-6">
                                                            <h6 class="text-danger"><i class="fas fa-minus-circle"></i> 变更前</h6>
                                                            <pre class="bg-light p-3 border rounded">{{ record.old_data }}</pre>
                                                        </div>
                                                        <div class="col-md-6">
                                                            <h6 class="text-success"><i class="fas fa-plus-circle"></i> 变更后</h6>
                                                            <pre class="bg-light p-3 border rounded">{{ record.new_data }}</pre>
                                                        </div>
                                                    </div>
                                                    {% elif record.change_type == 'move' %}
                                                    <h6 class="text-warning"><i class="fas fa-arrows-alt"></i> 区域移动</h6>
                                                    <div class="alert alert-info">
                                                        <p class="mb-0">
                                                            从
                                                            <strong>
                                                            {% if record.old_parent_id %}
                                                            {{ AdministrativeArea.query.get(record.old_parent_id).name }}
                                                            {% else %}
                                                            顶级区域
                                                            {% endif %}
                                                            </strong>
                                                            移动到
                                                            <strong>
                                                            {% if record.new_parent_id %}
                                                            {{ AdministrativeArea.query.get(record.new_parent_id).name }}
                                                            {% else %}
                                                            顶级区域
                                                            {% endif %}
                                                            </strong>
                                                        </p>
                                                    </div>
                                                    {% endif %}
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
<!-- 删除区域确认模态框 -->
{% if current_user.is_admin() %}
<div class="modal fade" id="deleteAreaModal" tabindex="-1" role="dialog" aria-labelledby="deleteAreaModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteAreaModalLabel">
                    <i class="fas fa-trash-alt"></i> 确认删除区域
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                    <h5>您确定要删除区域 <strong>{{ area.name }}</strong> 吗？</h5>
                </div>
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle"></i> 警告：
                    <ul class="mb-0 mt-2">
                        <li>此操作不可逆，删除后无法恢复</li>
                        <li>如果区域包含子区域，将无法删除</li>
                        <li>如果区域包含用户，将无法删除</li>
                    </ul>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times"></i> 取消
                </button>
                <form action="{{ url_for('area.delete_area', id=area.id) }}" method="POST"><button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash-alt"></i> 确认删除
                    </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- 添加自定义CSS -->
{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    /* 透明度效果 */
    .opacity-50 {
        opacity: 0.5;
    }

    /* 表格行悬停效果 */
    .table-hover tbody tr:hover {
        background-color: rgba(0, 123, 255, 0.05);
        transition: background-color 0.2s;
    }

    /* 卡片阴影效果 */
    .shadow-sm {
        box-shadow: 0 .125rem .25rem rgba(0,0,0,.075)!important;
        transition: box-shadow 0.3s;
    }

    .shadow-sm:hover {
        box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
    }

    /* 徽章样式优化 */
    .badge {
        font-weight: 500;
        padding: 0.4em 0.6em;
    }

    /* 按钮组样式优化 */
    .btn-group .btn {
        margin-right: 2px;
    }

    /* 区域信息容器样式 */
    .area-info-container {
        padding: 0.5rem 0;
    }

    /* 信息项样式 */
    .info-item {
        border-bottom: 1px solid #f1f3f4;
        padding: 0.75rem 0;
    }

    .info-item:last-child {
        border-bottom: none;
    }

    /* 信息标签样式 */
    .info-label {
        display: block;
        font-size: 0.875rem;
        font-weight: 600;
        color: #6c757d;
        margin-bottom: 0.5rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* 信息值样式 */
    .info-value {
        font-size: 1rem;
        line-height: 1.5;
    }

    /* 徽章大号样式 */
    .bg-lg {
        font-size: 0.875rem;
        padding: 0.5rem 0.75rem;
        border-radius: 0.375rem;
    }

    /* 代码块样式优化 */
    code.bg-light {
        font-size: 0.9rem;
        font-weight: 500;
        color: #495057;
    }

    /* 链接样式优化 */
    .text-decoration-none:hover {
        text-decoration: underline !important;
    }
</style>
{% endblock %}
{% endblock %}