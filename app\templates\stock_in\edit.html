{% extends 'base.html' %}

{% block title %}编辑入库单
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .context-guidance {
        border-start: 4px solid #17a2b8;
        background-color: #f8f9fa;
    }
    .workflow-context {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }
    .previous-step, .current-step, .next-step {
        flex: 1;
        padding: 0 10px;
    }
    .current-step {
        border-start: 1px solid #dee2e6;
        border-end: 1px solid #dee2e6;
    }
    .guidance-tips li {
        margin-bottom: 5px;
    }
    .step-guide-card {
        margin-bottom: 20px;
    }
    .alert-icon {
        font-size: 1.2rem;
        margin-right: 10px;
    }
    .data-entry-tips {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .highlight-box {
        border-start: 4px solid #28a745;
        padding-left: 15px;
        margin-bottom: 15px;
    }

    .context-guidance {
        border-start: 4px solid #17a2b8;
        background-color: #f8f9fa;
    }
    .workflow-context {
        display: flex;
        justify-content: space-between;
        margin-top: 10px;
    }
    .previous-step, .current-step, .next-step {
        flex: 1;
        padding: 0 10px;
    }
    .current-step {
        border-start: 1px solid #dee2e6;
        border-end: 1px solid #dee2e6;
    }
    .guidance-tips li {
        margin-bottom: 5px;
    }
    .step-guide-card {
        margin-bottom: 20px;
    }
    .alert-icon {
        font-size: 1.2rem;
        margin-right: 10px;
    }
    .data-entry-tips {
        background-color: #f8f9fa;
        border-radius: 5px;
        padding: 15px;
        margin-bottom: 20px;
    }
    .highlight-box {
        border-start: 4px solid #28a745;
        padding-left: 15px;
        margin-bottom: 15px;
    }
</style>

{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 入库明细录入指引 -->
    <div class="context-guidance card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-dolly-flatbed"></i> 入库明细录入指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle alert-icon"></i> <strong>重要提示：</strong> 请确保所有入库食材的信息准确无误，特别是批次号、数量、生产日期和过期日期等关键信息。
            </div>

            <div class="data-entry-tips">
                <h6 class="fw-bold"><i class="fas fa-lightbulb text-warning"></i> 入库明细录入技巧</h6>
                <div class="row">
                    <div class="col-md-6">
                        <div class="highlight-box">
                            <p class="mb-2"><strong>批次管理</strong></p>
                            <ul class="small mb-0">
                                <li>每批次食材应有唯一的批次号</li>
                                <li>点击"生成"按钮可自动生成批次号</li>
                                <li>批次号格式：B + 日期 + 随机字符</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="highlight-box">
                            <p class="mb-2"><strong>日期管理</strong></p>
                            <ul class="small mb-0">
                                <li>生产日期默认为今天，可根据实际情况修改</li>
                                <li>过期日期会根据生产日期自动计算</li>
                                <li>不同食材的保质期不同，请核对后再确认</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="highlight-box">
                            <p class="mb-2"><strong>数量与单价</strong></p>
                            <ul class="small mb-0">
                                <li>数量必须与实际入库数量一致</li>
                                <li>单价应为实际采购单价</li>
                                <li>系统会自动计算总价</li>
                            </ul>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="highlight-box">
                            <p class="mb-2"><strong>存储位置</strong></p>
                            <ul class="small mb-0">
                                <li>根据食材类型选择合适的存储位置</li>
                                <li>冷藏食材应放置在冷藏区</li>
                                <li>干货应放置在干燥区域</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <div class="alert alert-info mt-3">
                <i class="fas fa-info-circle"></i> <strong>操作流程：</strong>
                <ol class="mb-0">
                    <li>填写入库单基本信息并保存</li>
                    <li>添加入库明细（可添加多条）</li>
                    <li>核对所有信息无误后，确认入库</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑入库单</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 入库单基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">入库单号</th>
                                    <td>{{ stock_in.stock_in_number }}</td>
                                </tr>
                                <tr>
                                    <th>仓库</th>
                                    <td>{{ stock_in.warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        <span class="badge bg-warning">{{ stock_in.status }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <form method="post" action="{{ url_for('stock_in.edit', id=stock_in.id) }}" novalidate novalidate>
        {{ csrf_token() }}
                <div class="mb-3">
                                    <label for="stock_in_date">入库日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="stock_in_date" name="stock_in_date" value="{{  stock_in.stock_in_date|format_datetime('%Y-%m-%d')  }}" required>
                                </div>

                                <div class="mb-3">
                                    <label for="stock_in_type">入库类型 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stock_in_type" name="stock_in_type" required>
                                        <option value="">-- 请选择入库类型 --</option>
                                        <option value="采购入库" {% if stock_in.stock_in_type == '采购入库' %}selected{% endif %}>采购入库</option>
                                        <option value="调拨入库" {% if stock_in.stock_in_type == '调拨入库' %}selected{% endif %}>调拨入库</option>
                                        <option value="退货入库" {% if stock_in.stock_in_type == '退货入库' %}selected{% endif %}>退货入库</option>
                                        <option value="其他入库" {% if stock_in.stock_in_type == '其他入库' %}selected{% endif %}>其他入库</option>
                                    </select>
                                </div>

                                <!-- 供应商信息 -->
                                <div class="mb-3">
                                    <label>供应商信息</label>
                                    <p class="form-control-static">
                                        {% if stock_in.supplier %}
                                        <strong>{{ stock_in.supplier.name }}</strong>
                                        {% else %}
                                        <span class="text-muted">未指定供应商</span>
                                        {% endif %}
                                    </p>
                                    <small class="text-muted">供应商信息将在导入食材时自动关联</small>
                                </div>

                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ stock_in.notes }}</textarea>
                                </div>

                                <div class="mb-3 text-end">
                                    <button type="submit" class="btn btn-primary">保存基本信息</button>
                                </div>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                        </div>
                    </div>

                    <!-- 入库明细 -->
                    <div class="card mt-4">
                        <div class="card-header bg-info text-white">
                            <h4 class="card-title"><i class="fas fa-list-alt"></i> 入库明细</h4>
                        </div>
                        <div class="card-body">
                            <!-- 入库明细操作指引 -->
                            <div class="alert alert-light border mb-4">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-info-circle text-info fa-2x"></i>
                                    </div>
                                    <div>
                                        <h5 class="alert-heading">入库明细操作指引</h5>
                                        <p>请按照以下步骤添加入库明细：</p>
                                        <ol>
                                            <li>选择<strong>食材</strong>和<strong>存储位置</strong></li>
                                            <li>填写或生成<strong>批次号</strong>（批次号用于食品追溯）</li>
                                            <li>输入准确的<strong>数量</strong>和<strong>单位</strong></li>
                                            <li>确认<strong>生产日期</strong>和<strong>过期日期</strong></li>
                                            <li>填写<strong>单价</strong>（用于成本核算）</li>
                                            <li>点击<strong>添加明细</strong>按钮</li>
                                        </ol>
                                        <p class="mb-0 text-danger"><i class="fas fa-exclamation-triangle"></i> 注意：所有添加的明细将在确认入库后一并保存到库存中。</p>
                                    </div>
                                </div>
                            </div>
                            <!-- 快速操作按钮 -->
                            <div class="row mb-4">
                                <div class="col-md-12">
                                    <div class="card border-primary">
                                        <div class="card-header bg-primary text-white">
                                            <h5 class="mb-0"><i class="fas fa-bolt"></i> 快速操作</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <button type="button" class="btn btn-success w-100" id="importFromPurchaseBtn">
                                                        <i class="fas fa-file-import"></i> 从采购计划导入食材
                                                    </button>
                                                    <small class="text-muted">快速导入采购计划中的食材列表，自动填充基础信息</small>
                                                </div>
                                                <div class="col-md-6">
                                                    <button type="button" class="btn btn-info w-100" id="documentUploadBtn">
                                                        <i class="fas fa-file-upload"></i> 上传入库单据
                                                    </button>
                                                    <small class="text-muted">上传送货单、检验检疫证明等单据，并关联到食材</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 添加明细表单 -->
                            <form method="post" action="{{ url_for('stock_in.add_item', id=stock_in.id) }}" class="mb-4" novalidate novalidate>
        {{ csrf_token() }}
                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="ingredient_id">食材 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="ingredient_id" name="ingredient_id" required>
                                                <option value="">-- 请选择食材 --</option>
                                                {% for ingredient in ingredients %}
                                                <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="storage_location_id">存储位置 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="storage_location_id" name="storage_location_id" required>
                                                <option value="">-- 请选择存储位置 --</option>
                                                {% for location in storage_locations %}
                                                <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="batch_number">批次号 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="text" class="form-control" id="batch_number" name="batch_number" required>
                                                <div >
                                                    <button type="button" class="btn btn-outline-secondary" id="generate_batch_number">生成</button>
                                                </div>
                                            </div>
                                            <small class="form-text text-muted">点击"生成"按钮自动生成批次号</small>
                                            <script nonce="{{ csp_nonce }}">
                                                document.getElementById('generate_batch_number').addEventListener('click', function() {
                                                    const now = new Date();
                                                    const dateStr = now.getFullYear() +
                                                                  ('0' + (now.getMonth() + 1)).slice(-2) +
                                                                  ('0' + now.getDate()).slice(-2);
                                                    const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
                                                    document.getElementById('batch_number').value = 'B' + dateStr + randomStr;
                                                });

                                                // 点击输入框也可以生成批次号（如果为空）
                                                document.getElementById('batch_number').addEventListener('click', function() {
                                                    if (!this.value) {
                                                        const now = new Date();
                                                        const dateStr = now.getFullYear() +
                                                                      ('0' + (now.getMonth() + 1)).slice(-2) +
                                                                      ('0' + now.getDate()).slice(-2);
                                                        const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
                                                        this.value = 'B' + dateStr + randomStr;
                                                    }
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="quantity">数量 <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control form-control-lg fw-bold bg-light" id="quantity" name="quantity" step="0.01" required>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="unit">单位 <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="unit" name="unit" required>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="production_date">生产日期 <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="production_date" name="production_date" required>
                                            <script nonce="{{ csp_nonce }}">
                                                // 设置默认生产日期为今天
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const today = new Date();
                                                    const formattedDate = today.toISOString().substr(0, 10);
                                                    document.getElementById('production_date').value = formattedDate;
                                                });

                                                // 当生产日期变化时，自动更新过期日期
                                                document.getElementById('production_date').addEventListener('change', function() {
                                                    const prodDate = this.value;
                                                    if (prodDate) {
                                                        const prodDateObj = new Date(prodDate);
                                                        const expiryDate = new Date(prodDateObj);
                                                        expiryDate.setDate(prodDateObj.getDate() + 30); // 默认30天后过期
                                                        document.getElementById('expiry_date').value = expiryDate.toISOString().substr(0, 10);
                                                    }
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="expiry_date">过期日期 <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="expiry_date" name="expiry_date" required>
                                            <script nonce="{{ csp_nonce }}">
                                                // 设置默认过期日期为30天后
                                                document.addEventListener('DOMContentLoaded', function() {
                                                    const today = new Date();
                                                    const futureDate = new Date(today);
                                                    futureDate.setDate(today.getDate() + 30); // 默认30天后过期
                                                    const formattedDate = futureDate.toISOString().substr(0, 10);
                                                    document.getElementById('expiry_date').value = formattedDate;
                                                });

                                                // 当选择食材时，根据食材的保质期自动计算过期日期
                                                document.getElementById('ingredient_id').addEventListener('change', function() {
                                                    const ingredientId = this.value;
                                                    if (ingredientId) {
                                                        // 这里可以添加AJAX请求获取食材的保质期信息
                                                        // 简化起见，我们使用默认30天
                                                        const prodDate = document.getElementById('production_date').value;
                                                        if (prodDate) {
                                                            const prodDateObj = new Date(prodDate);
                                                            const expiryDate = new Date(prodDateObj);
                                                            expiryDate.setDate(prodDateObj.getDate() + 30);
                                                            document.getElementById('expiry_date').value = expiryDate.toISOString().substr(0, 10);
                                                        }
                                                    }
                                                });
                                            </script>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label for="unit_price">单价 <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="number" class="form-control form-control-lg fw-bold bg-light" id="unit_price" name="unit_price" step="0.01" placeholder="请输入单价">
                                                <div >
                                                    <span class="input-group-text">元/单位</span>
                                                </div>
                                            </div>
                                            <small id="total_price_display" class="form-text text-success fw-bold"></small>
                                            <script nonce="{{ csp_nonce }}">
                                                // 计算总价
                                                function calculateTotalPrice() {
                                                    const quantity = parseFloat(document.getElementById('quantity').value) || 0;
                                                    const unitPrice = parseFloat(document.getElementById('unit_price').value) || 0;
                                                    const totalPrice = quantity * unitPrice;

                                                    if (totalPrice > 0) {
                                                        document.getElementById('total_price_display').innerHTML =
                                                            `总价: <span class="text-danger">${totalPrice.toFixed(2)}</span> 元`;
                                                    } else {
                                                        document.getElementById('total_price_display').innerHTML = '';
                                                    }
                                                }

                                                // 监听数量和单价变化
                                                document.getElementById('quantity').addEventListener('input', calculateTotalPrice);
                                                document.getElementById('unit_price').addEventListener('input', calculateTotalPrice);
                                            </script>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-9">
                                        <div class="mb-3">
                                            <label for="notes">备注</label>
                                            <input type="text" class="form-control" id="notes" name="notes">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="mb-3">
                                            <label>&nbsp;</label>
                                            <button type="submit" class="btn btn-primary w-100">添加明细</button>
                                        </div>
                                    </div>
                                </div>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>

                            <!-- 明细列表 -->
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材</th>
                                            <th>存储位置</th>
                                            <th>批次号</th>
                                            <th class="bg-light text-primary">数量</th>
                                            <th>单位</th>
                                            <th>生产日期</th>
                                            <th>过期日期</th>
                                            <th class="bg-light text-danger">单价(元)</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_in_items %}
                                        <tr>
                                            <td>
                                                {{ item.ingredient.name }}
                                                {% if item.ingredient.category_rel and item.ingredient.category_rel.needs_inspection %}
                                                <span class="badge bg-warning">需检验</span>
                                                <button type="button" class="btn btn-xs btn-outline-warning ms-1" data-onclick="showInspectionModal({{ item.id }}, "{{ item.ingredient.name }}', '{{ item.ingredient.category_rel.inspection_type }}')">
                                                    <i class="fas fa-microscope"></i> 添加检验记录
                                                </button>
                                                {% endif %}
                                            </td>
                                            <td>{{ item.storage_location.name }} ({{ item.storage_location.location_code }})</td>
                                            <td>{{ item.batch_number }}</td>
                                            <td class="fw-bold text-primary">{{ item.quantity }}</td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{  item.production_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td>{{  item.expiry_date|format_datetime('%Y-%m-%d')  }}</td>
                                            <td class="fw-bold text-danger">{{ item.unit_price or '-' }}</td>
                                            <td>
                                                <form action="{{ url_for('stock_in.remove_item', id=stock_in.id, item_id=item.id) }}" method="post" style="display: inline;" novalidate novalidate><button type="submit" class="btn btn-danger btn-sm" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                                                        <i class="fas fa-trash"></i> 删除
                                                    </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="10" class="text-center">暂无入库明细，请添加</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <!-- 操作按钮指引 -->
                            <div class="card bg-light border mt-4 mb-4">
                                <div class="card-body">
                                    <h5 class="card-title"><i class="fas fa-check-circle text-success"></i> 完成入库操作</h5>
                                    <div class="row">
                                        <div class="col-md-8">
                                            <p>请在完成以下操作后，确认入库：</p>
                                            <ol>
                                                <li>确认所有食材明细已添加完成</li>
                                                <li>核对每项食材的数量、单价和批次号</li>
                                                <li>确认所有食材的生产日期和过期日期正确</li>
                                                <li>确认存储位置分配合理</li>
                                            </ol>
                                            <div class="alert alert-warning">
                                                <i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 确认入库后，入库单将无法再进行编辑。如需修改，请联系管理员。
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="card border-success">
                                                <div class="card-header bg-success text-white">
                                                    <h6 class="mb-0">入库流程</h6>
                                                </div>
                                                <div class="card-body">
                                                    <p class="small">确认入库后，系统将：</p>
                                                    <ol class="small">
                                                        <li>自动更新库存数量</li>
                                                        <li>生成入库记录，用于食品追溯</li>
                                                        <li>完成入库流程</li>
                                                    </ol>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 操作按钮 -->
                            <div class="row mt-4">
                                <div class="col-12 text-center">
                                    <a href="{{ url_for('stock_in.view', id=stock_in.id) }}" class="btn btn-info btn-lg">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                    <a href="{{ url_for('stock_in.batch_editor', id=stock_in.id) }}" class="btn btn-primary btn-lg">
                                        <i class="fas fa-th-list"></i> 批次编辑器
                                    </a>
                                    <button type="button" class="btn btn-success btn-lg" id="stockInBtn" {% if not stock_in_items %}disabled{% endif %}>
                                        <i class="fas fa-warehouse"></i> 确认入库
                                    </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                    <form action="{{ url_for('stock_in.cancel', id=stock_in.id) }}" method="post" style="display: inline;" novalidate novalidate><button type="submit" class="btn btn-danger btn-lg" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                                            <i class="fas fa-times"></i> 取消入库单
                                        </button>

    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                                </div>
                            </div>

                            <!-- 入库后注意事项 -->
                            <div class="alert alert-info mt-4">
                                <h6 class="alert-heading"><i class="fas fa-info-circle"></i> 入库后注意事项</h6>
                                <ul class="mb-0">
                                    <li>入库完成后，请确保食材按照要求存放在指定位置</li>
                                    <li>冷藏/冷冻食材应立即放入冷藏/冷冻设备</li>
                                    <li>所有食材应贴上标签，标明名称、批次号和过期日期</li>
                                    <li>入库单据应妥善保存，以备查验</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 采购计划选择模态框 -->
<div class="modal fade" id="purchaseOrderModal" tabindex="-1" role="dialog" aria-labelledby="purchaseOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="purchaseOrderModalLabel"><i class="fas fa-file-import"></i> 从采购计划导入</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 选择一个采购计划，系统将自动导入其中的食材明细。
                </div>

                <div class="table-responsive">
                    <table class="table table-hover table-sm">
                        <thead class="thead-light">
                            <tr>
                                <th>采购单号</th>
                                <th>创建日期</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for order in purchase_orders %}
                            <tr>
                                <td>{{ order.order_number }}</td>
                                <td>{{ order.created_at|format_datetime('%Y-%m-%d') }}</td>
                                <td>
                                    <button type="button" class="btn btn-primary btn-sm import-btn"
                                            data-id="{{ order.id }}" data-bs-dismiss="modal">
                                        <i class="fas fa-file-import"></i> 导入
                                    </button>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="3" class="text-center">暂无可用的采购计划</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
            </div>
        </div>
    </div>
</div>

<!-- 食材批量导入区域 -->
<div id="importedIngredientsContainer" style="display: none;" class="mb-4">
    <div class="card border-success">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0"><i class="fas fa-list-alt"></i> 从采购计划导入的食材</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i> 请检查并补充以下信息：批次号、生产日期、过期日期、存储位置等。
            </div>

            <!-- 关联信息 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">关联信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label>选择供应商</label>
                                <select class="form-control" id="globalSupplier">
                                    <option value="">-- 请选择供应商 --</option>
                                    {% for supplier in suppliers %}
                                    <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                    {% endfor %}
                                </select>
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applySupplier">
                                    应用到所有选中项
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label>关联入库单据</label>
                                <select class="form-control" id="relatedDocument">
                                    <option value="">-- 请选择单据 --</option>
                                    {% for doc in documents %}
                                    <option value="{{ doc.id }}">{{ doc.document_type }} - {{ doc.file_path.split('/')[-1] }}</option>
                                    {% endfor %}
                                </select>
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applyDocument">
                                    关联到所有选中项
                                </button>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label>批次号生成</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="batchNumberPrefix" placeholder="批次号前缀">
                                    <div >
                                        <button class="btn btn-outline-secondary" type="button" id="generateBatchNumber">生成</button>
                                    </div>
                                </div>
                                <small class="form-text text-muted">将自动添加日期后缀</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 批量操作 -->
            <div class="card mb-4">
                <div class="card-header bg-light">
                    <h5 class="mb-0">批量操作</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_storage_location">批量设置存储位置</label>
                                <select class="form-control" id="batch_storage_location">
                                    <option value="">-- 请选择存储位置 --</option>
                                    {% for location in storage_locations %}
                                    <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                    {% endfor %}
                                </select>
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applyStorageLocation">
                                    应用到选中项
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_production_date">批量设置生产日期</label>
                                <input type="date" class="form-control" id="batch_production_date">
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applyProductionDate">
                                    应用到选中项
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_expiry_date">批量设置过期日期</label>
                                <input type="date" class="form-control" id="batch_expiry_date">
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applyExpiryDate">
                                    应用到选中项
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-3">
                                <label for="batch_unit_price">批量设置单价</label>
                                <input type="number" class="form-control" id="batch_unit_price" step="0.01" min="0">
                                <button type="button" class="btn btn-sm btn-primary mt-2" id="applyUnitPrice">
                                    应用到选中项
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <button type="button" class="btn btn-primary" id="selectAllItems">
                                <i class="fas fa-check-square"></i> 全选
                            </button>
                            <button type="button" class="btn btn-secondary" id="deselectAllItems">
                                <i class="fas fa-square"></i> 取消全选
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导入的食材表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-hover">
                    <thead class="thead-light">
                        <tr>
                            <th width="5%">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="selectAll" checked>
                                    <label class="form-check-label" for="selectAll"></label>
                                </div>
                            </th>
                            <th width="15%">食材</th>
                            <th width="15%">存储位置</th>
                            <th width="10%">批次号</th>
                            <th width="8%" class="bg-light text-primary">数量</th>
                            <th width="7%">单位</th>
                            <th width="10%">生产日期</th>
                            <th width="10%">过期日期</th>
                            <th width="10%" class="bg-light text-danger">单价(元)</th>
                            <th width="10%">供应商</th>
                        </tr>
                    </thead>
                    <tbody id="importedItemsTable">
                        <!-- 导入的食材将在这里显示 -->
                    </tbody>
                </table>
            </div>

            <!-- 批量保存按钮 -->
            <div class="text-center mt-4">
                <button type="button" class="btn btn-success btn-lg" id="batchSaveBtn">
                    <i class="fas fa-save"></i> 批量保存入库明细
                </button>
                <button type="button" class="btn btn-secondary btn-lg" id="cancelImport">
                    <i class="fas fa-times"></i> 取消导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 文档上传模态框 -->
<div class="modal fade" id="documentUploadModal" tabindex="-1" role="dialog" aria-labelledby="documentUploadModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="documentUploadModalLabel"><i class="fas fa-file-upload"></i> 上传入库单据</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="uploadDocumentForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="document_type">单据类型 <span class="text-danger">*</span></label>
                        <select class="form-control" id="document_type" name="document_type" required>
                            <option value="送货单">送货单</option>
                            <option value="检验检疫证明">检验检疫证明</option>
                            <option value="质量检测报告">质量检测报告</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="document_supplier_id">关联供应商</label>
                        <select class="form-control" id="document_supplier_id" name="supplier_id">
                            <option value="">-- 请选择供应商 --</option>
                            {% for supplier in suppliers %}
                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="document">选择文件 <span class="text-danger">*</span></label>
                        <div class="form-control">
                            <input type="file" class="form-control-input" id="document" name="document" required>
                            <label class="form-control-label" for="document">选择文件...</label>
                        </div>
                        <small class="form-text text-muted">支持的文件格式：PDF, PNG, JPG, JPEG, DOC, DOCX, XLS, XLSX</small>
                    </div>

                    <div class="mb-3">
                        <label for="document_notes">备注</label>
                        <textarea class="form-control" id="document_notes" name="notes" rows="3"></textarea>
                    </div>

                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-upload"></i> 上传文档
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- 文档与食材关联模态框 -->
<div class="modal fade" id="associateItemsModal" tabindex="-1" role="dialog" aria-labelledby="associateItemsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="associateItemsModalLabel"><i class="fas fa-link"></i> 关联食材</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 请选择要与该文档关联的食材。
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                        <thead class="thead-light">
                            <tr>
                                <th>选择</th>
                                <th>食材</th>
                                <th>批次号</th>
                                <th>数量</th>
                                <th>单位</th>
                            </tr>
                        </thead>
                        <tbody id="associateItemsList">
                            {% for item in stock_in_items %}
                            <tr>
                                <td>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input select-item-for-document" id="associate_item_{{ item.id }}" data-id="{{ item.id }}">
                                        <label class="form-check-label" for="associate_item_{{ item.id }}"></label>
                                    </div>
                                </td>
                                <td>{{ item.ingredient.name }}</td>
                                <td>{{ item.batch_number }}</td>
                                <td>{{ item.quantity }} {{ item.unit }}</td>
                                <td>{{ item.unit }}</td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="5" class="text-center">暂无入库明细可关联</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>

                <input type="hidden" id="selectedDocumentId" value="">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveAssociationBtn">保存关联</button>
            </div>
        </div>
    </div>
</div>

<!-- 已上传文档列表 -->
<div class="modal fade" id="documentListModal" tabindex="-1" role="dialog" aria-labelledby="documentListModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="documentListModalLabel"><i class="fas fa-file-alt"></i> 已上传文档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="documentList">
                    <!-- 文档列表将通过AJAX加载 -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 食材检验检疫模态框 -->
<div class="modal fade" id="inspectionModal" tabindex="-1" role="dialog" aria-labelledby="inspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-warning text-white">
                <h5 class="modal-title" id="inspectionModalLabel"><i class="fas fa-microscope"></i> 食材检验检疫</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> 请填写食材 <strong id="inspectionItemName"></strong> 的检验检疫信息。
                </div>

                <!-- 检验记录表单 -->
                <form id="inspectionForm">
                    <input type="hidden" id="inspectionItemId" name="item_id">

                    <div class="mb-3">
                        <label for="inspection_type">检验类型 <span class="text-danger">*</span></label>
                        <select class="form-control" id="inspection_type" name="inspection_type" required>
                            <option value="">-- 请选择检验类型 --</option>
                            <option value="感官检验">感官检验</option>
                            <option value="理化检验">理化检验</option>
                            <option value="微生物检验">微生物检验</option>
                            <option value="农药残留检验">农药残留检验</option>
                            <option value="兽药残留检验">兽药残留检验</option>
                            <option value="重金属检验">重金属检验</option>
                            <option value="其他检验">其他检验</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label>检验结果 <span class="text-danger">*</span></label>
                        <div class="form-check">
                            <input type="radio" id="result_pass" name="result" value="合格" class="form-check-input" required>
                            <label class="form-check-label text-success" for="result_pass"><i class="fas fa-check-circle"></i> 合格</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" id="result_fail" name="result" value="不合格" class="form-check-input">
                            <label class="form-check-label text-danger" for="result_fail"><i class="fas fa-times-circle"></i> 不合格</label>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="inspection_document">关联检验证明文档</label>
                        <select class="form-control" id="inspection_document" name="document_id">
                            <option value="">-- 请选择关联文档 --</option>
                            {% for doc in documents %}
                            {% if doc.document_type in ['检验检疫证明', '质量检测报告'] %}
                            <option value="{{ doc.id }}">{{ doc.document_type }} - {{ doc.file_path.split('/')[-1] }}</option>
                            {% endif %}
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">如果没有相关文档，请先上传检验检疫证明或质量检测报告</small>
                    </div>

                    <div class="mb-3">
                        <label for="inspection_notes">检验备注</label>
                        <textarea class="form-control" id="inspection_notes" name="notes" rows="3"></textarea>
                    </div>
                </form>

                <!-- 已有检验记录列表 -->
                <div class="mt-4">
                    <h5>已有检验记录</h5>
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover">
                            <thead class="thead-light">
                                <tr>
                                    <th>检验类型</th>
                                    <th>检验结果</th>
                                    <th>检验人员</th>
                                    <th>检验日期</th>
                                    <th>关联文档</th>
                                </tr>
                            </thead>
                            <tbody id="inspectionRecordsList">
                                <!-- 检验记录将通过AJAX加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveInspectionBtn">保存检验记录</button>
            </div>
        </div>
    </div>
</div>

<!-- JavaScript代码 -->
<script nonce="{{ csp_nonce }}">
    // 页面加载完成后绑定事件
    $(document).ready(function() {
        console.log('Document ready');

        // 从采购计划导入食材 - 绑定点击事件
        $('#importFromPurchaseBtn').on('click', function() {
            console.log('importFromPurchaseBtn clicked');
            // 显示采购计划选择模态框
            $('#purchaseOrderModal').modal('show');
            console.log('Modal should be shown now');
        });
    });

    // 直接点击导入按钮加载食材（使用事件委托）
    $(document).on('click', '.import-btn', function() {
        console.log('import-btn clicked');
        const purchaseId = $(this).data('id');
        console.log('Purchase ID:', purchaseId);

        // 显示加载提示
        alert('正在导入食材，请稍候...');

        // 加载采购计划中的食材
        console.log('Sending AJAX request to:', `/stock-in/import-from-purchase/${purchaseId}`);

        // 使用完整的URL路径
        const fullUrl = window.location.origin + `/stock-in/import-from-purchase/${purchaseId}`;
        console.log('Full URL:', fullUrl);

        $.ajax({
            url: `/stock-in/import-from-purchase/${purchaseId}`,
            type: 'GET',
            dataType: 'json',
            success: function(response) {
                console.log('AJAX response received:', response);
                if (response.success) {
                    const data = response.data;

                    // 清空现有食材表格
                    $('#importedItemsTable').empty();

                    // 生成默认批次号
                    const defaultBatchNumber = `B${new Date().getTime().toString().slice(-8)}`;

                    // 设置默认日期
                    const today = new Date();
                    const defaultProductionDate = today.toISOString().split('T')[0];

                    // 默认过期日期为7天后
                    const defaultExpiryDate = new Date(today);
                    defaultExpiryDate.setDate(today.getDate() + 7);
                    const defaultExpiryDateStr = defaultExpiryDate.toISOString().split('T')[0];

                    // 设置默认日期到批量设置字段
                    $('#batch_production_date').val(defaultProductionDate);
                    $('#batch_expiry_date').val(defaultExpiryDateStr);

                    // 渲染食材列表
                    data.forEach((item, index) => {
                        // 设置默认值
                        item.batch_number = item.batch_number || defaultBatchNumber;
                        item.production_date = item.production_date || defaultProductionDate;
                        item.expiry_date = item.expiry_date || defaultExpiryDateStr;
                        item.estimated_price = item.estimated_price || 0;

                        const rowHtml = `
                            <tr class="${item.needs_inspection ? 'table-warning' : ''}">
                                <td>
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input select-item" id="item_${index}" data-id="${item.ingredient_id}" checked>
                                        <label class="form-check-label" for="item_${index}"></label>
                                    </div>
                                </td>
                                <td>${item.ingredient_name} ${item.needs_inspection ? '<span class="badge bg-warning">需检验</span>' : ''}</td>
                                <td>
                                    <select class="form-control form-control-sm item-storage-location">
                                        <option value="">-- 请选择 --</option>
                                        {% for location in storage_locations %}
                                        <option value="{{ location.id }}">{{ location.name }} ({{ location.location_code }})</option>
                                        {% endfor %}
                                    </select>
                                </td>
                                <td><input type="text" class="form-control form-control-sm item-batch" value="${item.batch_number}"></td>
                                <td><input type="number" class="form-control form-control-sm item-quantity" value="${item.quantity}" min="0" step="0.01"></td>
                                <td>${item.unit}</td>
                                <td><input type="date" class="form-control form-control-sm item-production-date" value="${item.production_date}"></td>
                                <td><input type="date" class="form-control form-control-sm item-expiry-date" value="${item.expiry_date}"></td>
                                <td><input type="number" class="form-control form-control-sm item-price" value="${item.estimated_price}" min="0" step="0.01"></td>
                                <td>
                                    <select class="form-control form-control-sm item-supplier">
                                        <option value="${item.supplier_id}">${item.supplier_name || '-- 请选择 --'}</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                        {% endfor %}
                                    </select>
                                </td>
                            </tr>
                        `;
                        $('#importedItemsTable').append(rowHtml);
                    });

                    // 显示导入的食材区域
                    $('#importedIngredientsContainer').show();

                    // 隐藏模态框
                    $('#purchaseOrderModal').modal('hide');
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('AJAX error:', status, error);
                console.log('Response:', xhr.responseText);
                alert('请求失败，请检查控制台获取详细信息');
            }
        });
    });

    // 全选/取消全选
    $('#selectAll').change(function() {
        $('.select-item').prop('checked', $(this).prop('checked'));
    });

    // 单个选择项改变时，检查是否需要更新全选状态
    $(document).on('change', '.select-item', function() {
        const allChecked = $('.select-item:not(:checked)').length === 0;
        $('#selectAll').prop('checked', allChecked);
    });

    // 全选按钮
    $('#selectAllItems').click(function() {
        $('.select-item, #selectAll').prop('checked', true);
    });

    // 取消全选按钮
    $('#deselectAllItems').click(function() {
        $('.select-item, #selectAll').prop('checked', false);
    });

    // 生成批次号
    $('#generateBatchNumber').click(function() {
        const prefix = $('#batchNumberPrefix').val() || 'B';
        const datePart = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const batchNumber = `${prefix}-${datePart}`;

        // 应用到所有选中项
        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-batch').val(batchNumber);
        });
    });

    // 应用供应商
    $('#applySupplier').click(function() {
        const supplierId = $('#globalSupplier').val();
        if (!supplierId) {
            alert('请先选择供应商');
            return;
        }

        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-supplier').val(supplierId);
        });
    });

    // 应用存储位置
    $('#applyStorageLocation').click(function() {
        const locationId = $('#batch_storage_location').val();
        if (!locationId) {
            alert('请先选择存储位置');
            return;
        }

        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-storage-location').val(locationId);
        });
    });

    // 应用生产日期
    $('#applyProductionDate').click(function() {
        const date = $('#batch_production_date').val();
        if (!date) {
            alert('请先选择生产日期');
            return;
        }

        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-production-date').val(date);
        });
    });

    // 应用过期日期
    $('#applyExpiryDate').click(function() {
        const date = $('#batch_expiry_date').val();
        if (!date) {
            alert('请先选择过期日期');
            return;
        }

        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-expiry-date').val(date);
        });
    });

    // 应用单价
    $('#applyUnitPrice').click(function() {
        const price = $('#batch_unit_price').val();
        if (!price) {
            alert('请先输入单价');
            return;
        }

        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            row.find('.item-price').val(price);
        });
    });

    // 关联文档
    $('#applyDocument').click(function() {
        const documentId = $('#relatedDocument').val();
        if (!documentId) {
            alert('请先选择文档');
            return;
        }

        // 这里可以添加文档关联逻辑
        alert('文档关联功能将在后续版本中实现');
    });

    // 取消导入
    $('#cancelImport').click(function() {
        if (confirm('确定要取消导入吗？所有已导入的食材信息将被清空。')) {
            $('#importedIngredientsContainer').hide();
            $('#importedItemsTable').empty();
        }
    });

    // 批量保存入库明细
    $('#batchSaveBtn').click(function() {
        const selectedItems = [];
        let hasError = false;

        // 收集所有选中的食材信息
        $('.select-item:checked').each(function() {
            const row = $(this).closest('tr');
            const itemId = $(this).data('id');

            console.log('Processing item with ID:', itemId);

            const storageLocationId = row.find('.item-storage-location').val();
            if (!storageLocationId) {
                alert('请为所有食材选择存储位置');
                hasError = true;
                return false;
            }

            const quantity = row.find('.item-quantity').val();
            if (!quantity || parseFloat(quantity) <= 0) {
                alert('请为所有食材输入有效的数量');
                hasError = true;
                return false;
            }

            const batchNumber = row.find('.item-batch').val();
            if (!batchNumber) {
                alert('请为所有食材输入批次号');
                hasError = true;
                return false;
            }

            const productionDate = row.find('.item-production-date').val();
            if (!productionDate) {
                alert('请为所有食材选择生产日期');
                hasError = true;
                return false;
            }

            const expiryDate = row.find('.item-expiry-date').val();
            if (!expiryDate) {
                alert('请为所有食材选择过期日期');
                hasError = true;
                return false;
            }

            const unit = row.find('td:eq(5)').text().trim();
            const supplierId = row.find('.item-supplier').val();
            const unitPrice = row.find('.item-price').val();

            console.log('Item details:', {
                ingredient_id: itemId,
                storage_location_id: storageLocationId,
                quantity: quantity,
                unit: unit,
                batch_number: batchNumber,
                production_date: productionDate,
                expiry_date: expiryDate,
                supplier_id: supplierId,
                unit_price: unitPrice
            });

            selectedItems.push({
                ingredient_id: itemId,
                quantity: quantity,
                unit: unit,
                supplier_id: supplierId,
                unit_price: unitPrice,
                batch_number: batchNumber,
                production_date: productionDate,
                expiry_date: expiryDate,
                storage_location_id: storageLocationId,
                quality_status: '良好',
                notes: ''
            });
        });

        if (hasError) {
            return;
        }

        if (selectedItems.length === 0) {
            alert('请至少选择一项食材');
            return;
        }

        console.log('Sending batch save request with items:', selectedItems);

        // 发送批量保存请求
        $.ajax({
            url: `/stock-in/{{ stock_in.id }}/add-items-batch`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ items: selectedItems }),
            success: function(response) {
                console.log('Batch save response:', response);
                if (response.success) {
                    alert('批量添加入库明细成功');
                    // 跳转到简化版批次编辑器
                    if (response.redirect_url) {
                        window.location.href = response.redirect_url;
                    } else {
                        location.reload();
                    }
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function(xhr, status, error) {
                console.error('Batch save error:', status, error);
                console.log('Response:', xhr.responseText);
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 票据上传
    $('#documentUploadBtn').click(function() {
        $('#documentUploadModal').modal('show');
    });

    // 上传票据文件
    $('#uploadDocumentForm').submit(function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: `/stock-in/{{ stock_in.id }}/upload-document`,
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    // 添加票据到列表
                    alert('文档上传成功');
                    $('#documentUploadModal').modal('hide');

                    // 显示关联食材模态框
                    $('#selectedDocumentId').val(response.document.id);
                    $('#associateItemsModal').modal('show');
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 保存票据与食材的关联
    $('#saveAssociationBtn').click(function() {
        const documentId = $('#selectedDocumentId').val();
        const selectedItems = [];

        $('.select-item-for-document:checked').each(function() {
            selectedItems.push($(this).data('id'));
        });

        if (selectedItems.length === 0) {
            alert('请至少选择一项食材');
            return;
        }

        $.ajax({
            url: `/stock-in/associate-document/${documentId}`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({ item_ids: selectedItems }),
            success: function(response) {
                if (response.success) {
                    alert('关联成功');
                    $('#associateItemsModal').modal('hide');
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 自定义文件输入框显示文件名
    $('.form-control-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).next('.form-control-label').html(fileName);
    });

    // 显示检验检疫模态框
    function showInspectionModal(itemId, itemName, inspectionType) {
        // 设置模态框标题和表单数据
        $('#inspectionItemName').text(itemName);
        $('#inspectionItemId').val(itemId);

        // 如果有默认的检验类型，则设置
        if (inspectionType) {
            $('#inspection_type option').each(function() {
                if ($(this).text().includes(inspectionType)) {
                    $(this).prop('selected', true);
                }
            });
        }

        // 加载已有的检验记录
        loadInspectionRecords(itemId);

        // 显示模态框
        $('#inspectionModal').modal('show');
    }

    // 加载检验记录
    function loadInspectionRecords(itemId) {
        $.get(`/stock-in/get-inspections/${itemId}`, function(response) {
            if (response.success) {
                const records = response.data;
                let html = '';

                if (records.length === 0) {
                    html = '<tr><td colspan="5" class="text-center">暂无检验记录</td></tr>';
                } else {
                    records.forEach(record => {
                        html += `
                            <tr>
                                <td>${record.inspection_type}</td>
                                <td>
                                    <span class="badge badge-${record.result === '合格' ? 'success' : 'danger'}">
                                        ${record.result}
                                    </span>
                                </td>
                                <td>${record.inspector_name}</td>
                                <td>${record.inspection_date}</td>
                                <td>
                                    ${record.document_path ?
                                        `<a href="/static/${record.document_path}" target="_blank" class="btn btn-sm btn-info">
                                            <i class="fas fa-file-alt"></i> ${record.document_type}
                                        </a>` :
                                        '无关联文档'}
                                </td>
                            </tr>
                        `;
                    });
                }

                $('#inspectionRecordsList').html(html);
            } else {
                alert('加载检验记录失败: ' + response.message);
            }
        });
    }

    // 保存检验记录
    $('#saveInspectionBtn').click(function() {
        const itemId = $('#inspectionItemId').val();
        const inspectionType = $('#inspection_type').val();
        const result = $('input[name="result"]:checked').val();
        const documentId = $('#inspection_document').val();
        const notes = $('#inspection_notes').val();

        if (!inspectionType || !result) {
            alert('请填写检验类型和检验结果');
            return;
        }

        // 发送请求保存检验记录
        $.ajax({
            url: `/stock-in/add-inspection/${itemId}`,
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify({
                inspection_type: inspectionType,
                result: result,
                document_id: documentId || null,
                notes: notes
            }),
            success: function(response) {
                if (response.success) {
                    alert('检验记录添加成功');

                    // 重新加载检验记录
                    loadInspectionRecords(itemId);

                    // 清空表单
                    $('#inspection_type').val('');
                    $('input[name="result"]').prop('checked', false);
                    $('#inspection_document').val('');
                    $('#inspection_notes').val('');
                } else {
                    alert('错误: ' + response.message);
                }
            },
            error: function() {
                alert('服务器错误，请稍后重试');
            }
        });
    });

    // 确认入库按钮点击事件
    $('#stockInBtn').click(function() {
        if (confirm('确认入库将更新学校库存系统，此操作不可撤销，确定要继续吗？')) {
            $.ajax({
                url: "{{ url_for('stock_in.stock_in_operation', id=stock_in.id) }}",
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({}),
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        // 跳转到详情页面
                        window.location.href = "{{ url_for('stock_in.view_details', id=stock_in.id) }}";
                    } else {
                        alert('错误: ' + response.message);
                    }
                },
                error: function() {
                    alert('服务器错误，请稍后重试');
                }
            });
        }
    });
</script>


{% endblock %}
