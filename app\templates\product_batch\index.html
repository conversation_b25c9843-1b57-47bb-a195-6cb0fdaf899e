{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">产品批量上架管理</h3>
                            <small class="text-muted">管理产品批次的创建、审核和上架流程</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_product.index') }}" class="btn btn-info btn-sm">
                                <i class="fas fa-box"></i> 产品上架审核
                            </a>
                            <a href="{{ url_for('product_batch.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 创建批次
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <form method="get" action="{{ url_for('product_batch.index') }}">
                                <div class="input-group">
                                    <select name="status" class="form-control">
                                        <option value="" {% if not status %}selected{% endif %}>全部状态</option>
                                        <option value="pending" {% if status == 'pending' %}selected{% endif %}>待审核</option>
                                        <option value="approved" {% if status == 'approved' %}selected{% endif %}>已审核</option>
                                        <option value="shelved" {% if status == 'shelved' %}selected{% endif %}>已上架</option>
                                        <option value="rejected" {% if status == 'rejected' %}selected{% endif %}>已拒绝</option>
                                    </select>
                                    <div >
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-search"></i> 筛选
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>批次号</th>
                                    <th>分类</th>
                                    <th>供应商</th>
                                    <th>产品数量</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr>
                                    <td>
                                        <code class="text-primary">{{ batch.name }}</code>
                                    </td>
                                    <td>{{ batch.category.name if batch.category else '' }}</td>
                                    <td>{{ batch.supplier.name if batch.supplier else '' }}</td>
                                    <td>{{ batch.products.count() }}</td>
                                    <td>
                                        {% if batch.status == 'pending' %}
                                        <span class="badge bg-warning">待审核</span>
                                        {% elif batch.status == 'approved' %}
                                        <span class="badge bg-success">已审核</span>
                                        {% elif batch.status == 'shelved' %}
                                        <span class="badge bg-info">已上架</span>
                                        {% elif batch.status == 'rejected' %}
                                        <span class="badge bg-danger">已拒绝</span>
                                        {% endif %}
                                    </td>
                                    <td>{% if batch.created_at is defined and batch.created_at is not string and batch.created_at is not none %}{{ batch.created_at.strftime('%Y-%m-%d %H:%M:%S') }}{% else %}{{ batch.created_at }}{% endif %}</td>
                                    <td>
                                        <a href="{{ url_for('product_batch.view', id=batch.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>

                                        {% if batch.status == 'pending' and current_user.is_admin() %}
                                        <a href="{{ url_for('product_batch.approve', id=batch.id) }}" class="btn btn-success btn-sm">
                                            <i class="fas fa-check"></i> 审核
                                        </a>
                                        {% endif %}

                                        {% if batch.status == 'approved' %}
                                        <button type="button" class="btn btn-primary btn-sm shelf-batch" data-id="{{ batch.id }}">
                                            <i class="fas fa-upload"></i> 上架
                                        </button>
                                        {% endif %}

                                        {% if batch.status == 'shelved' %}
                                        <button type="button" class="btn btn-warning btn-sm unshelf-batch" data-id="{{ batch.id }}">
                                            <i class="fas fa-download"></i> 下架
                                        </button>
                                        {% endif %}

                                        {% if batch.status != 'shelved' %}
                                        <button type="button" class="btn btn-danger btn-sm delete-batch" data-id="{{ batch.id }}">
                                            <i class="fas fa-trash"></i> 删除
                                        </button>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <div class="row">
                        <div class="col-md-12">
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center">
                                    {% if pagination.has_prev %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('product_batch.index', page=pagination.prev_num, status=status) }}">上一页</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">上一页</a>
                                    </li>
                                    {% endif %}

                                    {% for p in pagination.iter_pages() %}
                                        {% if p %}
                                            {% if p == pagination.page %}
                                            <li class="page-item active">
                                                <a class="page-link" href="{{ url_for('product_batch.index', page=p, status=status) }}">{{ p }}</a>
                                            </li>
                                            {% else %}
                                            <li class="page-item">
                                                <a class="page-link" href="{{ url_for('product_batch.index', page=p, status=status) }}">{{ p }}</a>
                                            </li>
                                            {% endif %}
                                        {% else %}
                                        <li class="page-item disabled">
                                            <a class="page-link" href="#">...</a>
                                        </li>
                                        {% endif %}
                                    {% endfor %}

                                    {% if pagination.has_next %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('product_batch.index', page=pagination.next_num, status=status) }}">下一页</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#">下一页</a>
                                    </li>
                                    {% endif %}
                                </ul>
                            </nav>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 上架批次
    $('.shelf-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要上架此批次吗？')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/shelf',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });

    // 下架批次
    $('.unshelf-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要下架此批次吗？')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/unshelf',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });

    // 删除批次
    $('.delete-batch').click(function() {
        var batchId = $(this).data('id');
        if (confirm('确定要删除此批次吗？此操作将删除批次及其所有产品，且不可恢复！')) {
            $.ajax({
                url: '/product-batch/' + batchId + '/delete',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        alert(response.message);
                        location.reload();
                    } else {
                        alert(response.message);
                    }
                },
                error: function() {
                    alert('操作失败，请重试');
                }
            });
        }
    });
});
</script>
{% endblock %}
