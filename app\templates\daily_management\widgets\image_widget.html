{% extends 'base_widget.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    .image-gallery {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 15px;
    }
    .image-container {
        width: 200px;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px;
        position: relative;
    }
    .inspection-image {
        width: 100%;
        height: 150px;
        object-fit: cover;
        border-radius: 3px;
        cursor: pointer;
    }
    .rating {
        text-align: center;
        margin: 5px 0;
        font-size: 20px;
    }
    .star {
        color: #ccc;
        cursor: pointer;
    }
    .star.active {
        color: #ffc107;
    }
    .image-actions {
        display: flex;
        justify-content: space-between;
        margin-top: 5px;
    }
    .add-image-container {
        width: 200px;
        height: 200px;
        border: 2px dashed #ddd;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .add-image-btn {
        background: none;
        border: none;
        color: #6c757d;
        display: flex;
        flex-direction: column;
        align-items: center;
        cursor: pointer;
    }
    .add-image-btn i {
        font-size: 24px;
        margin-bottom: 5px;
    }
    .loading-indicator {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        border-radius: 4px;
    }
    .upload-progress {
        width: 80%;
        margin-top: 10px;
    }
    .widget-container {
        padding: 15px;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        background-color: #fff;
    }
    .widget-header {
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 1px solid #e3e6f0;
    }
    .widget-title {
        margin: 0;
        font-size: 1.2rem;
        font-weight: bold;
        color: #4e73df;
    }
    .image-preview-modal .modal-body {
        text-align: center;
    }
    .image-preview-modal img {
        max-width: 100%;
        max-height: 70vh;
    }
</style>
{% endblock %}

{% block content %}
<div class="widget-container">
    <div class="widget-header">
        <h5 class="widget-title">{{ title }}</h5>
    </div>
    
    <div class="image-gallery" id="image-gallery-{{ reference_type }}-{{ reference_id }}">
        <!-- 图片将通过JavaScript动态加载 -->
        
        <!-- 添加图片按钮 -->
        <div class="add-image-container">
            <button type="button" class="add-image-btn">
                <i class="fas fa-plus"></i>
                <span>添加图片</span>
            </button>
            <input type="file" class="file-input" style="display: none;" accept="image/*">
        </div>
    </div>
</div>

<!-- 图片预览模态框 -->
<div class="modal fade image-preview-modal" id="imagePreviewModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">图片预览</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="" id="previewImage" alt="预览图片">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    // 图片上传小部件
    class ImageWidget {
        constructor(container, options) {
            this.container = container;
            this.options = Object.assign({
                referenceType: '',
                referenceId: '',
                apiBaseUrl: '/daily-management/image-api'
            }, options);
            
            this.init();
        }
        
        init() {
            // 初始化添加图片按钮
            this.initAddButton();
            
            // 加载图片列表
            this.loadImages();
        }
        
        initAddButton() {
            const addButton = this.container.querySelector('.add-image-btn');
            const fileInput = this.container.querySelector('.file-input');
            
            addButton.addEventListener('click', () => {
                fileInput.click();
            });
            
            fileInput.addEventListener('change', async (e) => {
                if (e.target.files.length > 0) {
                    await this.uploadImage(e.target.files[0]);
                    fileInput.value = ''; // 清空文件输入
                }
            });
        }
        
        async loadImages() {
            try {
                const response = await fetch(`${this.options.apiBaseUrl}/list?reference_type=${this.options.referenceType}&reference_id=${this.options.referenceId}`);
                const data = await response.json();
                
                if (data.success) {
                    // 清除现有图片（保留添加按钮）
                    const addContainer = this.container.querySelector('.add-image-container');
                    while (this.container.firstChild && this.container.firstChild !== addContainer) {
                        this.container.removeChild(this.container.firstChild);
                    }
                    
                    // 添加图片
                    data.photos.forEach(photo => {
                        const imageContainer = this.createImageContainer(photo);
                        this.container.insertBefore(imageContainer, addContainer);
                    });
                }
            } catch (error) {
                console.error('加载图片失败:', error);
            }
        }
        
        async uploadImage(file) {
            const addContainer = this.container.querySelector('.add-image-container');
            
            // 创建加载指示器
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'loading-indicator';
            loadingIndicator.innerHTML = `
                <div>处理中...</div>
                <div class="progress upload-progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                </div>
            `;
            addContainer.appendChild(loadingIndicator);
            
            try {
                // 压缩图片
                const compressResult = await this.compressImage(file);
                
                // 显示压缩信息
                const originalSizeKB = (compressResult.originalSize / 1024).toFixed(2);
                const compressedSizeKB = (compressResult.compressedSize / 1024).toFixed(2);
                const savingPercent = (100 - (compressResult.compressedSize / compressResult.originalSize * 100)).toFixed(2);
                
                loadingIndicator.querySelector('div').textContent = `压缩完成: ${originalSizeKB}KB → ${compressedSizeKB}KB (节省${savingPercent}%)`;
                
                // 上传图片
                const formData = new FormData();
                formData.append('photo', compressResult.blob, 'image.jpg');
                formData.append('reference_type', this.options.referenceType);
                formData.append('reference_id', this.options.referenceId);
                
                const xhr = new XMLHttpRequest();
                xhr.open('POST', `${this.options.apiBaseUrl}/upload`, true);
                
                // 进度监听
                xhr.upload.onprogress = (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        loadingIndicator.querySelector('.progress-bar').style.width = `${percentComplete}%`;
                        loadingIndicator.querySelector('div').textContent = `上传中: ${Math.round(percentComplete)}%`;
                    }
                };
                
                xhr.onload = () => {
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // 创建图片容器
                            const imageContainer = this.createImageContainer({
                                id: response.id,
                                file_path: response.file_path,
                                rating: response.rating
                            });
                            
                            // 插入到图片库
                            this.container.insertBefore(imageContainer, addContainer);
                        } else {
                            alert(`上传失败: ${response.error}`);
                        }
                    } else {
                        alert(`上传失败: ${xhr.status}`);
                    }
                    
                    // 移除加载指示器
                    loadingIndicator.remove();
                };
                
                xhr.onerror = () => {
                    alert('网络错误，上传失败');
                    loadingIndicator.remove();
                };
                
                xhr.send(formData);
                
            } catch (error) {
                console.error('处理图片错误:', error);
                alert('图片处理失败: ' + error.message);
                loadingIndicator.remove();
            }
        }
        
        async compressImage(file, maxWidth = 1200, quality = 0.8) {
            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.onload = function(event) {
                    const img = new Image();
                    img.onload = function() {
                        const canvas = document.createElement('canvas');
                        let width = img.width;
                        let height = img.height;
                        
                        // 调整尺寸
                        if (width > maxWidth) {
                            height = (height * maxWidth) / width;
                            width = maxWidth;
                        }
                        
                        canvas.width = width;
                        canvas.height = height;
                        
                        const ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0, width, height);
                        
                        // 转换为Blob
                        canvas.toBlob((blob) => {
                            resolve({
                                blob: blob,
                                originalSize: file.size,
                                compressedSize: blob.size,
                                width: width,
                                height: height
                            });
                        }, 'image/jpeg', quality);
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        }
        
        createImageContainer(photo) {
            const container = document.createElement('div');
            container.className = 'image-container';
            container.dataset.photoId = photo.id;
            
            container.innerHTML = `
                <img src="${photo.file_path}" class="inspection-image" alt="照片">
                <div class="rating" data-value="${photo.rating}">
                    <span class="star ${photo.rating >= 1 ? 'active' : ''}">★</span>
                    <span class="star ${photo.rating >= 2 ? 'active' : ''}">★</span>
                    <span class="star ${photo.rating >= 3 ? 'active' : ''}">★</span>
                    <span class="star ${photo.rating >= 4 ? 'active' : ''}">★</span>
                    <span class="star ${photo.rating >= 5 ? 'active' : ''}">★</span>
                </div>
                <div class="image-actions">
                    <button type="button" class="btn btn-sm btn-danger delete-photo">删除</button>
                    <button type="button" class="btn btn-sm btn-secondary rotate-photo">旋转</button>
                </div>
            `;
            
            // 初始化评分功能
            this.initRating(container.querySelector('.rating'));
            
            // 初始化删除按钮
            container.querySelector('.delete-photo').addEventListener('click', () => {
                this.deletePhoto(photo.id, container);
            });
            
            // 初始化旋转按钮
            container.querySelector('.rotate-photo').addEventListener('click', () => {
                this.rotateImage(container.querySelector('.inspection-image'));
            });
            
            // 初始化图片预览
            container.querySelector('.inspection-image').addEventListener('click', () => {
                this.previewImage(photo.file_path);
            });
            
            return container;
        }
        
        initRating(ratingElement) {
            const stars = ratingElement.querySelectorAll('.star');
            const photoId = ratingElement.closest('.image-container').dataset.photoId;
            
            stars.forEach((star, index) => {
                // 鼠标悬停效果
                star.addEventListener('mouseover', () => {
                    for (let i = 0; i <= index; i++) {
                        stars[i].classList.add('active');
                    }
                    for (let i = index + 1; i < stars.length; i++) {
                        stars[i].classList.remove('active');
                    }
                });
                
                // 鼠标离开效果
                ratingElement.addEventListener('mouseleave', () => {
                    const value = parseInt(ratingElement.dataset.value);
                    stars.forEach((s, i) => {
                        if (i < value) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                });
                
                // 点击设置评分
                star.addEventListener('click', () => {
                    const newValue = index + 1;
                    this.updateRating(photoId, newValue, ratingElement, stars);
                });
            });
        }
        
        async updateRating(photoId, rating, ratingElement, stars) {
            try {
                const response = await fetch(`${this.options.apiBaseUrl}/update-rating`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        photo_id: photoId,
                        rating: rating
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    // 更新UI
                    ratingElement.dataset.value = rating;
                    stars.forEach((s, i) => {
                        if (i < rating) {
                            s.classList.add('active');
                        } else {
                            s.classList.remove('active');
                        }
                    });
                } else {
                    alert(`评分更新失败: ${data.error}`);
                }
            } catch (error) {
                console.error('评分更新错误:', error);
                alert('评分更新失败，请重试');
            }
        }
        
        async deletePhoto(photoId, containerElement) {
            if (confirm('确定要删除这张照片吗？')) {
                try {
                    const response = await fetch(`${this.options.apiBaseUrl}/delete`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            photo_id: photoId
                        })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        containerElement.remove();
                    } else {
                        alert(`删除失败: ${data.error}`);
                    }
                } catch (error) {
                    console.error('删除照片错误:', error);
                    alert('删除失败，请重试');
                }
            }
        }
        
        rotateImage(imgElement) {
            const currentRotation = parseInt(imgElement.dataset.rotation || 0);
            const newRotation = (currentRotation + 90) % 360;
            imgElement.style.transform = `rotate(${newRotation}deg)`;
            imgElement.dataset.rotation = newRotation;
        }
        
        previewImage(imagePath) {
            const previewModal = document.getElementById('imagePreviewModal');
            const previewImage = document.getElementById('previewImage');
            
            previewImage.src = imagePath;
            $(previewModal).modal('show');
        }
    }
    
    // 初始化图片上传小部件
    document.addEventListener('DOMContentLoaded', function() {
        const galleryContainer = document.getElementById('image-gallery-{{ reference_type }}-{{ reference_id }}');
        
        if (galleryContainer) {
            new ImageWidget(galleryContainer, {
                referenceType: '{{ reference_type }}',
                referenceId: '{{ reference_id }}',
                apiBaseUrl: '/daily-management/image-api'
            });
        }
    });
</script>
{% endblock %}
