{% extends 'base.html' %}

{% block title %}打印检查照片详情{% endblock %}

{% block styles %}
<style nonce="{{ csp_nonce }}">
    @d-flex print {
        .no-print {
            display: none !important;
        }
        body {
            padding: 0;
            margin: 0;
        }
        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    
    .print-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .print-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
    }
    
    .print-header p {
        font-size: 14px;
        color: #666;
    }
    
    .print-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .photo-container {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .photo-container img {
        max-width: 100%;
        max-height: 400px;
        border: 1px solid #ddd;
        padding: 5px;
        border-radius: 5px;
    }
    
    .info-container {
        width: 100%;
        margin-bottom: 30px;
    }
    
    .info-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .info-table th, .info-table td {
        border: 1px solid #ddd;
        padding: 8px 12px;
    }
    
    .info-table th {
        background-color: #f8f9fc;
        text-align: right;
        width: 30%;
    }
    
    .rating-stars {
        color: #f6c23e;
    }
    
    .signature-section {
        margin-top: 50px;
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
    
    .signature-box {
        border-top: 1px solid #000;
        width: 200px;
        padding-top: 5px;
        text-align: center;
    }

    @d-flex print {
        .no-print {
            display: none !important;
        }
        body {
            padding: 0;
            margin: 0;
        }
        .container-fluid {
            width: 100%;
            padding: 0;
            margin: 0;
        }
    }
    
    .print-header {
        text-align: center;
        margin-bottom: 20px;
    }
    
    .print-header h1 {
        font-size: 24px;
        margin-bottom: 5px;
    }
    
    .print-header p {
        font-size: 14px;
        color: #666;
    }
    
    .print-content {
        display: flex;
        flex-direction: column;
        align-items: center;
    }
    
    .photo-container {
        margin-bottom: 20px;
        text-align: center;
    }
    
    .photo-container img {
        max-width: 100%;
        max-height: 400px;
        border: 1px solid #ddd;
        padding: 5px;
        border-radius: 5px;
    }
    
    .info-container {
        width: 100%;
        margin-bottom: 30px;
    }
    
    .info-table {
        width: 100%;
        border-collapse: collapse;
    }
    
    .info-table th, .info-table td {
        border: 1px solid #ddd;
        padding: 8px 12px;
    }
    
    .info-table th {
        background-color: #f8f9fc;
        text-align: right;
        width: 30%;
    }
    
    .rating-stars {
        color: #f6c23e;
    }
    
    .signature-section {
        margin-top: 50px;
        display: flex;
        justify-content: space-between;
        width: 100%;
    }
    
    .signature-box {
        border-top: 1px solid #000;
        width: 200px;
        padding-top: 5px;
        text-align: center;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4 no-print">
        <h1 class="h3 mb-0 text-gray-800">打印检查照片详情</h1>
        <div>
            <button class="print-button" class="btn btn-primary">
                <i class="fas fa-print me-1"></i> 打印
            </button>
            <a href="{{ url_for('daily_management.view_inspection_photo', photo_id=photo.id) }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i> 返回
            </a>
        </div>
    </div>

    <div class="print-header">
        <h1>{{ school.name }} - 食堂检查记录详情</h1>
        <p>日期：{{ log.log_date }} &nbsp;&nbsp; 打印时间：{{ now|format_datetime }}</p>
    </div>

    <div class="print-content">
        <div class="photo-container">
            <img src="{{ photo.file_path }}" alt="检查照片">
        </div>

        <div class="info-container">
            <table class="info-table">
                <tr>
                    <th>检查类型</th>
                    <td>
                        {% if photo.reference_type == 'morning' %}早晨检查
                        {% elif photo.reference_type == 'noon' %}中午检查
                        {% elif photo.reference_type == 'evening' %}晚上检查
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>上传时间</th>
                    <td>{{ photo.upload_time|format_datetime }}</td>
                </tr>
                <tr>
                    <th>评分</th>
                    <td>
                        {% if photo.rating %}
                        <span class="rating-stars">
                            {% for i in range(photo.rating) %}★{% endfor %}
                            {% for i in range(5 - photo.rating) %}☆{% endfor %}
                        </span>
                        {% else %}
                        未评分
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <th>上传者</th>
                    <td>{{ photo.uploader.name if photo.uploader else '系统' }}</td>
                </tr>
                <tr>
                    <th>备注</th>
                    <td>{{ photo.description or '无' }}</td>
                </tr>
            </table>
        </div>

        <div class="signature-section">
            <div class="signature-box">
                检查人签名
            </div>
            <div class="signature-box">
                负责人签名
            </div>
        </div>
    </div>
</div>
{% endblock %}
