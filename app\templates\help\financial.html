{% extends "base.html" %}

{% block title %}财务管理帮助{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='financial/css/yonyou-theme.css') }}">
<style>
/* 用友财务帮助页面专用样式 */
.uf-help-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 16px;
}

.uf-breadcrumb {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 8px 12px;
    margin-bottom: 16px;
    font-size: var(--uf-font-size);
}

.uf-breadcrumb a {
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-breadcrumb a:hover {
    text-decoration: underline;
}

.uf-breadcrumb-separator {
    margin: 0 6px;
    color: var(--uf-muted);
}

.uf-page-header {
    background: linear-gradient(135deg, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    padding: 24px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 16px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-page-header h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.uf-page-header p {
    font-size: var(--uf-font-size-large);
    margin: 0;
    opacity: 0.9;
}

.uf-quick-nav {
    background: linear-gradient(135deg, var(--uf-success) 0%, #155724 100%);
    color: white;
    padding: 20px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 20px;
    text-align: center;
    box-shadow: var(--uf-box-shadow);
}

.uf-quick-nav h5 {
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.uf-nav-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.uf-nav-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 16px;
    border-radius: var(--uf-border-radius);
    text-decoration: none;
    font-size: var(--uf-font-size);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--uf-transition);
}

.uf-nav-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    text-decoration: none;
}

.uf-content-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.uf-main-content {
    flex: 2;
}

.uf-sidebar {
    flex: 1;
    min-width: 280px;
}

.uf-alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--uf-border-radius);
    padding: 12px;
    margin: 12px 0;
    color: #856404;
}

/* 用友财务帮助页面专用样式 */
.uf-help-container {
    background: var(--uf-light);
    min-height: 100vh;
    padding: 16px;
}

.uf-breadcrumb {
    background: white;
    border: 1px solid var(--uf-border);
    border-radius: var(--uf-border-radius);
    padding: 8px 12px;
    margin-bottom: 16px;
    font-size: var(--uf-font-size);
}

.uf-breadcrumb a {
    color: var(--uf-primary);
    text-decoration: none;
}

.uf-breadcrumb a:hover {
    text-decoration: underline;
}

.uf-breadcrumb-separator {
    margin: 0 6px;
    color: var(--uf-muted);
}

.uf-page-header {
    background: linear-gradient(135deg, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);
    color: white;
    padding: 24px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 16px;
    text-align: center;
    /* box-shadow: var(--uf-box-shadow); */ /* 移除阴影效果 */
}

.uf-page-header h1 {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.uf-page-header p {
    font-size: var(--uf-font-size-large);
    margin: 0;
    opacity: 0.9;
}

.uf-quick-nav {
    background: linear-gradient(135deg, var(--uf-success) 0%, #155724 100%);
    color: white;
    padding: 20px;
    border-radius: var(--uf-border-radius);
    margin-bottom: 20px;
    text-align: center;
    /* box-shadow: var(--uf-box-shadow); */ /* 移除阴影效果 */
}

.uf-quick-nav h5 {
    font-size: var(--uf-font-size-large);
    font-weight: 600;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.uf-nav-buttons {
    display: flex;
    gap: 12px;
    flex-wrap: wrap;
    justify-content: center;
}

.uf-nav-btn {
    background: rgba(255,255,255,0.2);
    border: 1px solid rgba(255,255,255,0.3);
    color: white;
    padding: 8px 16px;
    border-radius: var(--uf-border-radius);
    text-decoration: none;
    font-size: var(--uf-font-size);
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: var(--uf-transition);
}

.uf-nav-btn:hover {
    background: rgba(255,255,255,0.3);
    border-color: rgba(255,255,255,0.5);
    color: white;
    text-decoration: none;
}

.uf-content-layout {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.uf-main-content {
    flex: 2;
}

.uf-sidebar {
    flex: 1;
    min-width: 280px;
}

.uf-alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: var(--uf-border-radius);
    padding: 12px;
    margin: 12px 0;
    color: #856404;
}
</style>
{% endblock %}

{% block content %}
<div class="uf-help-container">
    <!-- 面包屑导航 -->
    <div class="uf-breadcrumb">
        <a href="{{ url_for('help.index') }}">帮助中心</a>
        <span class="uf-breadcrumb-separator">></span>
        <span>财务管理</span>
    </div>

    <!-- 页面标题 -->
    <div class="uf-page-header">
        <h1><i class="fas fa-calculator"></i> 财务管理帮助</h1>
        <p>完整的财务管理功能指南</p>
    </div>

    <!-- 快速导航 -->
    <div class="uf-quick-nav">
        <h5><i class="fas fa-rocket"></i> 快速导航</h5>
        <div class="uf-nav-buttons">
            <a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-nav-btn">
                <i class="fas fa-chart-line"></i> 会计科目管理
            </a>
            <a href="{{ url_for('financial.vouchers_index') }}" class="uf-nav-btn">
                <i class="fas fa-file-invoice"></i> 财务凭证
            </a>
            <a href="{{ url_for('financial.reports_index') }}" class="uf-nav-btn">
                <i class="fas fa-chart-bar"></i> 财务报表
            </a>
            <a href="{{ url_for('financial.payables_index') }}" class="uf-nav-btn">
                <i class="fas fa-money-bill-wave"></i> 应付账款
            </a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="uf-content-layout">
        <!-- 主要内容 -->
        <div class="uf-main-content">
            <!-- 会计科目管理 -->
            <div class="uf-card" style="margin-bottom: 20px;">
                <div class="uf-card-header" style="background: var(--uf-primary); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-chart-line" style="color: white;"></i>
                        <span>会计科目管理</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <p style="margin-bottom: 16px; line-height: 1.6;">会计科目是财务管理的基础，用于分类记录各种经济业务。</p>
                    
                    <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-cogs"></i> 系统科目 vs 学校科目
                    </h6>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div class="uf-card" style="border: 2px solid var(--uf-primary);">
                            <div class="uf-card-body">
                                <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 8px;">系统科目</h6>
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 标准化会计科目
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 全局共享
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 符合会计准则
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 不可修改
                                    </li>
                                </ul>
                            </div>
                        </div>
                        <div class="uf-card" style="border: 2px solid var(--uf-warning);">
                            <div class="uf-card-body">
                                <h6 style="color: var(--uf-warning); font-weight: 600; margin-bottom: 8px;">学校科目</h6>
                                <ul style="list-style: none; padding: 0; margin: 0;">
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 学校专属科目
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 可自定义修改
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 基于系统科目
                                    </li>
                                    <li style="margin-bottom: 4px; display: flex; align-items: center; gap: 6px;">
                                        <i class="fas fa-check" style="color: var(--uf-success);"></i> 灵活适应需求
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: flex; gap: 8px;">
                        <a href="{{ url_for('help.accounting_subjects_help') }}" class="uf-btn uf-btn-primary">
                            <i class="fas fa-book"></i> 详细指南
                        </a>
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-outline-primary">
                            <i class="fas fa-external-link-alt"></i> 管理科目
                        </a>
                    </div>
                </div>
            </div>

            <!-- 财务凭证 -->
            <div class="uf-card" style="margin-bottom: 20px;">
                <div class="uf-card-header" style="background: var(--uf-success); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-file-invoice" style="color: white;"></i>
                        <span>财务凭证</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <p style="margin-bottom: 16px; line-height: 1.6;">财务凭证是记录经济业务的重要单据，是财务核算的基础。</p>

                    <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-list"></i> 主要功能
                    </h6>
                    <ul style="margin-bottom: 16px; line-height: 1.6;">
                        <li><strong>创建凭证</strong> - 录入各种经济业务</li>
                        <li><strong>编辑凭证</strong> - 修改凭证信息和明细</li>
                        <li><strong>审核凭证</strong> - 凭证审核流程</li>
                        <li><strong>查询凭证</strong> - 按条件查询凭证</li>
                    </ul>

                    <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-exclamation-triangle"></i> 常见问题
                    </h6>
                    <div class="uf-alert-warning">
                        <strong>问题：</strong>添加明细时会计科目下拉框为空<br>
                        <strong>解决：</strong>需要先初始化会计科目，参考<a href="{{ url_for('help.accounting_subjects_help') }}" style="color: var(--uf-primary);">会计科目管理帮助</a>
                    </div>

                    <div style="margin-top: 16px;">
                        <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-success">
                            <i class="fas fa-external-link-alt"></i> 财务凭证管理
                        </a>
                    </div>
                </div>
            </div>

            <!-- 财务报表 -->
            <div class="uf-card" style="margin-bottom: 20px;">
                <div class="uf-card-header" style="background: var(--uf-info); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-chart-bar" style="color: white;"></i>
                        <span>财务报表</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <p style="margin-bottom: 16px; line-height: 1.6;">财务报表提供学校财务状况的全面分析。</p>

                    <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-chart-pie"></i> 报表类型
                    </h6>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 16px; margin-bottom: 16px;">
                        <div>
                            <ul style="line-height: 1.6;">
                                <li><strong>资产负债表</strong> - 财务状况报表</li>
                                <li><strong>利润表</strong> - 经营成果报表</li>
                            </ul>
                        </div>
                        <div>
                            <ul style="line-height: 1.6;">
                                <li><strong>现金流量表</strong> - 现金流动情况</li>
                                <li><strong>科目余额表</strong> - 各科目余额</li>
                            </ul>
                        </div>
                    </div>

                    <div style="margin-top: 16px;">
                        <a href="{{ url_for('financial.reports_index') }}" class="uf-btn uf-btn-info">
                            <i class="fas fa-external-link-alt"></i> 查看财务报表
                        </a>
                    </div>
                </div>
            </div>

            <!-- 应付账款 -->
            <div class="uf-card" style="margin-bottom: 20px;">
                <div class="uf-card-header" style="background: var(--uf-warning); color: #333;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-money-bill-wave" style="color: #333;"></i>
                        <span>应付账款</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <p style="margin-bottom: 16px; line-height: 1.6;">管理学校对供应商的应付款项。</p>

                    <h6 style="color: var(--uf-primary); font-weight: 600; margin-bottom: 12px; display: flex; align-items: center; gap: 6px;">
                        <i class="fas fa-tasks"></i> 主要功能
                    </h6>
                    <ul style="margin-bottom: 16px; line-height: 1.6;">
                        <li><strong>应付登记</strong> - 记录应付款项</li>
                        <li><strong>付款管理</strong> - 处理付款业务</li>
                        <li><strong>账龄分析</strong> - 分析应付账款账龄</li>
                        <li><strong>供应商对账</strong> - 与供应商核对账目</li>
                    </ul>

                    <div style="margin-top: 16px;">
                        <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-warning">
                            <i class="fas fa-external-link-alt"></i> 应付账款管理
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 侧边栏 -->
        <div class="uf-sidebar">
            <!-- 快速链接 -->
            <div class="uf-card" style="margin-bottom: 16px;">
                <div class="uf-card-header" style="background: var(--uf-muted); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-link" style="color: white;"></i>
                        <span>快速链接</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <div style="display: flex; flex-direction: column; gap: 8px;">
                        <a href="{{ url_for('financial.accounting_subjects_index') }}" class="uf-btn uf-btn-outline-primary uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-chart-line"></i> 会计科目
                        </a>
                        <a href="{{ url_for('financial.vouchers_index') }}" class="uf-btn uf-btn-outline-success uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-file-invoice"></i> 财务凭证
                        </a>
                        <a href="{{ url_for('financial.reports_index') }}" class="uf-btn uf-btn-outline-info uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-chart-bar"></i> 财务报表
                        </a>
                        <a href="{{ url_for('financial.payables_index') }}" class="uf-btn uf-btn-outline-warning uf-btn-sm" style="justify-content: flex-start;">
                            <i class="fas fa-money-bill-wave"></i> 应付账款
                        </a>
                    </div>
                </div>
            </div>

            <!-- 相关帮助 -->
            <div class="uf-card">
                <div class="uf-card-header" style="background: var(--uf-info); color: white;">
                    <div class="uf-card-header-title">
                        <i class="fas fa-book" style="color: white;"></i>
                        <span>相关帮助</span>
                    </div>
                </div>
                <div class="uf-card-body">
                    <div style="display: flex; flex-direction: column; gap: 12px;">
                        <a href="{{ url_for('help.accounting_subjects_help') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-chart-line" style="color: var(--uf-primary); width: 16px;"></i>
                            <span>会计科目管理</span>
                        </a>
                        <a href="{{ url_for('help.troubleshooting') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-exclamation-triangle" style="color: var(--uf-warning); width: 16px;"></i>
                            <span>故障排除</span>
                        </a>
                        <a href="{{ url_for('help.index') }}" style="color: var(--uf-primary); text-decoration: none; display: flex; align-items: center; gap: 8px; padding: 4px 0;">
                            <i class="fas fa-home" style="color: var(--uf-success); width: 16px;"></i>
                            <span>帮助中心首页</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
