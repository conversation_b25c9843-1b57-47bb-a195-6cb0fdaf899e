<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>培训记录打印</title>
    <style nonce="{{ csp_nonce }}">
        @d-flex print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                font-family: "SimSun", "宋体", serif;
                line-height: 1.5;
                color: #000;
                background: #fff;
                margin: 0;
                padding: 0;
                font-size: 12pt;
            }
            .container {
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                padding: 0;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #000;
            }
            .header h1 {
                font-size: 24pt;
                font-weight: bold;
                margin: 0;
                padding: 10px 0;
            }
            .header p {
                font-size: 12pt;
                margin: 5px 0;
            }
            .section {
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 16pt;
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #000;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            .info-item {
                margin-bottom: 10px;
            }
            .info-label {
                font-weight: bold;
                display: inline-block;
                min-width: 80px;
            }
            .info-value {
                display: inline-block;
            }
            .text-box {
                border: 1px solid #ccc;
                padding: 10px;
                margin-top: 5px;
                min-height: 60px;
                background-color: #f9f9f9;
            }
            .photo-section {
                margin-top: 20px;
            }
            .photo-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .photo-item {
                text-align: center;
            }
            .photo-item img {
                max-width: 100%;
                max-height: 200px;
                border: 1px solid #ccc;
                padding: 5px;
                background: white;
            }
            .photo-caption {
                margin-top: 5px;
                font-size: 10pt;
                color: #666;
            }
            .footer {
                margin-top: 30px;
                text-align: right;
                font-size: 10pt;
                color: #666;
            }
            .school-info {
                text-align: center;
                margin-bottom: 10px;
            }
            .school-name {
                font-size: 14pt;
                font-weight: bold;
            }
            .qr-code {
                text-align: center;
                margin-top: 20px;
            }
            .qr-code img {
                width: 100px;
                height: 100px;
            }
            .qr-code-caption {
                font-size: 9pt;
                color: #666;
                margin-top: 5px;
            }
            .no-print {
                display: none;
            }
            .table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .table th, .table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
            }
            .table th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            .signature-item {
                flex: 1;
                margin: 0 20px;
                text-align: center;
            }
            .signature-line {
                border-bottom: 1px solid #000;
                margin-bottom: 5px;
                height: 40px;
            }
        }
        
        /* 非打印样式 */
        body {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #333;
        }
        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 0;
            padding: 10px 0;
        }
        .header p {
            font-size: 12pt;
            margin: 5px 0;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 80px;
        }
        .info-value {
            display: inline-block;
        }
        .text-box {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 5px;
            min-height: 60px;
            background-color: #f9f9f9;
        }
        .photo-section {
            margin-top: 20px;
        }
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .photo-item {
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ccc;
            padding: 5px;
            background: white;
        }
        .photo-caption {
            margin-top: 5px;
            font-size: 10pt;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10pt;
            color: #666;
        }
        .school-info {
            text-align: center;
            margin-bottom: 10px;
        }
        .school-name {
            font-size: 14pt;
            font-weight: bold;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            flex: 1;
            margin: 0 20px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        .no-print {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .no-print:hover {
            background-color: #45a049;
        }
    
        @d-flex print {
            @page {
                size: A4;
                margin: 1cm;
            }
            body {
                font-family: "SimSun", "宋体", serif;
                line-height: 1.5;
                color: #000;
                background: #fff;
                margin: 0;
                padding: 0;
                font-size: 12pt;
            }
            .container {
                width: 100%;
                max-width: 210mm;
                margin: 0 auto;
                padding: 0;
            }
            .header {
                text-align: center;
                margin-bottom: 20px;
                padding-bottom: 10px;
                border-bottom: 2px solid #000;
            }
            .header h1 {
                font-size: 24pt;
                font-weight: bold;
                margin: 0;
                padding: 10px 0;
            }
            .header p {
                font-size: 12pt;
                margin: 5px 0;
            }
            .section {
                margin-bottom: 20px;
            }
            .section-title {
                font-size: 16pt;
                font-weight: bold;
                margin-bottom: 10px;
                padding-bottom: 5px;
                border-bottom: 1px solid #000;
            }
            .info-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 10px;
            }
            .info-item {
                margin-bottom: 10px;
            }
            .info-label {
                font-weight: bold;
                display: inline-block;
                min-width: 80px;
            }
            .info-value {
                display: inline-block;
            }
            .text-box {
                border: 1px solid #ccc;
                padding: 10px;
                margin-top: 5px;
                min-height: 60px;
                background-color: #f9f9f9;
            }
            .photo-section {
                margin-top: 20px;
            }
            .photo-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
            .photo-item {
                text-align: center;
            }
            .photo-item img {
                max-width: 100%;
                max-height: 200px;
                border: 1px solid #ccc;
                padding: 5px;
                background: white;
            }
            .photo-caption {
                margin-top: 5px;
                font-size: 10pt;
                color: #666;
            }
            .footer {
                margin-top: 30px;
                text-align: right;
                font-size: 10pt;
                color: #666;
            }
            .school-info {
                text-align: center;
                margin-bottom: 10px;
            }
            .school-name {
                font-size: 14pt;
                font-weight: bold;
            }
            .qr-code {
                text-align: center;
                margin-top: 20px;
            }
            .qr-code img {
                width: 100px;
                height: 100px;
            }
            .qr-code-caption {
                font-size: 9pt;
                color: #666;
                margin-top: 5px;
            }
            .no-print {
                display: none;
            }
            .table {
                width: 100%;
                border-collapse: collapse;
                margin-bottom: 20px;
            }
            .table th, .table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
            }
            .table th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            .signature-section {
                margin-top: 50px;
                display: flex;
                justify-content: space-between;
            }
            .signature-item {
                flex: 1;
                margin: 0 20px;
                text-align: center;
            }
            .signature-line {
                border-bottom: 1px solid #000;
                margin-bottom: 5px;
                height: 40px;
            }
        }
        
        /* 非打印样式 */
        body {
            font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
            line-height: 1.5;
            color: #333;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            width: 100%;
            max-width: 210mm;
            margin: 0 auto;
            background: white;
            padding: 20px;
            /* box-shadow: 0 0 10px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #333;
        }
        .header h1 {
            font-size: 24pt;
            font-weight: bold;
            margin: 0;
            padding: 10px 0;
        }
        .header p {
            font-size: 12pt;
            margin: 5px 0;
        }
        .section {
            margin-bottom: 20px;
        }
        .section-title {
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 10px;
            padding-bottom: 5px;
            border-bottom: 1px solid #333;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }
        .info-item {
            margin-bottom: 10px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            min-width: 80px;
        }
        .info-value {
            display: inline-block;
        }
        .text-box {
            border: 1px solid #ccc;
            padding: 10px;
            margin-top: 5px;
            min-height: 60px;
            background-color: #f9f9f9;
        }
        .photo-section {
            margin-top: 20px;
        }
        .photo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }
        .photo-item {
            text-align: center;
        }
        .photo-item img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ccc;
            padding: 5px;
            background: white;
        }
        .photo-caption {
            margin-top: 5px;
            font-size: 10pt;
            color: #666;
        }
        .footer {
            margin-top: 30px;
            text-align: right;
            font-size: 10pt;
            color: #666;
        }
        .school-info {
            text-align: center;
            margin-bottom: 10px;
        }
        .school-name {
            font-size: 14pt;
            font-weight: bold;
        }
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        .table th, .table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: left;
        }
        .table th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .signature-section {
            margin-top: 50px;
            display: flex;
            justify-content: space-between;
        }
        .signature-item {
            flex: 1;
            margin: 0 20px;
            text-align: center;
        }
        .signature-line {
            border-bottom: 1px solid #000;
            margin-bottom: 5px;
            height: 40px;
        }
        .no-print {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            text-align: center;
            text-decoration: none;
            display: inline-block;
            font-size: 16px;
            margin: 20px 0;
            cursor: pointer;
            border: none;
            border-radius: 4px;
        }
        .no-print:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="school-info">
                <div class="school-name">{{ training.daily_log.area.name }}</div>
            </div>
            <h1>食堂培训记录表</h1>
            <p>记录编号：{{ training.id }}</p>
        </div>

        <div class="section">
            <div class="section-title">基本信息</div>
            <table class="table">
                <tr>
                    <th width="20%">培训主题</th>
                    <td colspan="3">{{ training.training_topic }}</td>
                </tr>
                <tr>
                    <th>培训讲师</th>
                    <td width="30%">{{ training.trainer }}</td>
                    <th width="20%">培训时间</th>
                    <td width="30%">{{ training.training_time|format_datetime }}</td>
                </tr>
                <tr>
                    <th>培训地点</th>
                    <td>{{ training.location or '未指定' }}</td>
                    <th>培训时长</th>
                    <td>{{ training.duration or 0 }} 分钟</td>
                </tr>
                <tr>
                    <th>参训人数</th>
                    <td>{{ training.attendees_count or 0 }} 人</td>
                    <th>创建时间</th>
                    <td>{{ training.created_at|format_datetime }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">培训内容摘要</div>
            <div class="text-box">{{ training.content_summary or '无' }}</div>
        </div>

        <div class="section">
            <div class="section-title">效果评估</div>
            <div class="text-box">{{ training.effectiveness_evaluation or '无' }}</div>
        </div>

        {% if photos %}
        <div class="section photo-section">
            <div class="section-title">培训照片</div>
            <div class="photo-grid">
                {% for photo in photos %}
                <div class="photo-item">
                    <img src="{{ photo.file_path }}" alt="培训照片">
                    <div class="photo-caption">培训照片 #{{ loop.index }}</div>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}

        <div class="signature-section">
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>培训讲师签名</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>食堂负责人签名</div>
            </div>
            <div class="signature-item">
                <div class="signature-line"></div>
                <div>学校负责人签名</div>
            </div>
        </div>

        <div class="footer">
            <p>打印时间：{{ now.strftime('%Y年%m月%d日 %H:%M') }}</p>
        </div>
    </div>

    <div style="text-align: center; margin-top: 20px;">
        <button class="no-print" class="print-button">打印此页</button>
        <a class="no-print" href="{{ url_for('daily_management.view_training', training_id=training.id) }}" style="margin-left: 10px; text-decoration: none; color: #333;">返回详情页</a>
    </div>
</body>
</html>
