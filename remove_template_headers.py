#!/usr/bin/env python3
"""
删除HTML模板文件中的标题元素脚本
删除所有H1、H2、H3、H4、H5、H6标签及其内容
"""

import os
import re
from pathlib import Path

def remove_headers_from_template(file_path):
    """删除模板文件中的标题元素"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 定义要删除的标题标签模式
        # 匹配 <h1...>...</h1>, <h2...>...</h2> 等，包括多行内容
        header_patterns = [
            # 匹配 <h1 class="...">内容</h1> 格式（单行）
            r'<h([1-6])\s+[^>]*>.*?</h\1>\s*\n?',
            # 匹配 <h1>内容</h1> 格式（单行）
            r'<h([1-6])>.*?</h\1>\s*\n?',
            # 匹配多行标题（包含换行符）
            r'<h([1-6])\s+[^>]*>[\s\S]*?</h\1>\s*\n?',
            # 匹配简单多行标题
            r'<h([1-6])>[\s\S]*?</h\1>\s*\n?'
        ]
        
        # 逐个应用模式删除标题
        for pattern in header_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if matches:
                content = re.sub(pattern, '', content, flags=re.IGNORECASE | re.MULTILINE | re.DOTALL)
                changes_made += len(matches)
        
        # 清理多余的空行（连续的空行合并为单个空行）
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return 0
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return 0

def preview_headers_in_template(file_path):
    """预览模板文件中的标题元素（不删除）"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        headers_found = []
        
        # 查找所有标题元素
        header_pattern = r'<h([1-6])[^>]*>(.*?)</h\1>'
        matches = re.findall(header_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for level, title_content in matches:
            # 清理标题内容（去除HTML标签和多余空白）
            clean_title = re.sub(r'<[^>]+>', '', title_content).strip()
            clean_title = re.sub(r'\s+', ' ', clean_title)
            if clean_title:
                headers_found.append(f"H{level}: {clean_title}")
        
        return headers_found
        
    except Exception as e:
        print(f"❌ 预览文件 {file_path} 时出错: {str(e)}")
        return []

def main():
    """主函数"""
    print("🔍 删除HTML模板文件中的标题元素")
    print("=" * 60)
    
    # 模板文件目录
    templates_dir = Path("app/templates")
    
    if not templates_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    # 首先预览所有要删除的标题
    print("📋 预览要删除的标题元素：")
    print("-" * 40)
    
    all_headers = {}
    for html_file in templates_dir.rglob("*.html"):
        headers = preview_headers_in_template(html_file)
        if headers:
            rel_path = html_file.relative_to(Path("."))
            all_headers[str(rel_path)] = headers
    
    if not all_headers:
        print("✅ 没有找到需要删除的标题元素")
        return
    
    # 显示预览
    total_headers = 0
    for file_path, headers in all_headers.items():
        print(f"\n📄 {file_path}:")
        for header in headers:
            print(f"   • {header}")
            total_headers += 1
    
    print(f"\n📊 总计找到 {total_headers} 个标题元素在 {len(all_headers)} 个文件中")
    
    # 询问用户确认
    print("\n" + "=" * 60)
    confirm = input("❓ 确认删除以上所有标题元素吗？(y/N): ").strip().lower()
    
    if confirm not in ['y', 'yes', '是']:
        print("❌ 操作已取消")
        return
    
    # 执行删除操作
    print("\n🔄 开始删除标题元素...")
    print("-" * 40)
    
    total_files = 0
    total_changes = 0
    
    # 处理所有HTML文件
    for html_file in templates_dir.rglob("*.html"):
        changes = remove_headers_from_template(html_file)
        if changes > 0:
            total_files += 1
            total_changes += changes
            rel_path = html_file.relative_to(Path("."))
            print(f"✅ 处理文件: {rel_path} - 删除了 {changes} 个标题元素")
    
    print("\n" + "=" * 60)
    print(f"📊 删除完成:")
    print(f"   修改文件: {total_files} 个")
    print(f"   删除标题: {total_changes} 个")
    
    if total_changes > 0:
        print("\n✨ 所有标题元素已成功删除！")
        print("💡 建议检查页面布局，确保删除标题后页面仍然美观")
    else:
        print("\n✅ 没有找到需要删除的标题元素")

def preview_only():
    """仅预览模式 - 不删除任何内容"""
    print("👀 预览模式：查看所有标题元素")
    print("=" * 50)
    
    templates_dir = Path("app/templates")
    
    if not templates_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    total_headers = 0
    total_files = 0
    
    for html_file in templates_dir.rglob("*.html"):
        headers = preview_headers_in_template(html_file)
        if headers:
            total_files += 1
            rel_path = html_file.relative_to(Path("."))
            print(f"\n📄 {rel_path}:")
            for header in headers:
                print(f"   • {header}")
                total_headers += 1
    
    print(f"\n📊 总计: {total_headers} 个标题元素在 {total_files} 个文件中")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "--preview":
        preview_only()
    else:
        main()
