{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .card-header {
        background-color: #f8f9fa;
        font-weight: bold;
    }
    .system-account-section {
        display: none;
    }
    .form-check-input {
        margin-top: 0.3rem;
    }

    .role-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 10px;
        margin-bottom: 10px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .role-card:hover {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .role-card.selected {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }

    .role-card.disabled {
        opacity: 0.5;
        cursor: not-allowed;
        background-color: #f8f9fa;
    }

    .role-card .role-name {
        font-weight: bold;
        margin-bottom: 5px;
    }

    .role-card .role-description {
        font-size: 0.85rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <h2>{{ title }}</h2>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <form method="post" enctype="multipart/form-data" novalidate novalidate novalidate>            {{ form.hidden_tag() }}

            <!-- 基本信息 -->
            <div class="card mb-4">
                <div class="card-header">基本信息</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.name.label }}
                                {{ form.name(class="form-control") }}
                                {% for error in form.name.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.gender.label }}
                                {{ form.gender(class="form-control") }}
                                {% for error in form.gender.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.birth_date.label }}
                                {{ form.birth_date(class="form-control", type="date") }}
                                {% for error in form.birth_date.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.phone.label }}
                                {{ form.phone(class="form-control") }}
                                {% for error in form.phone.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.address.label }}
                        {{ form.address(class="form-control") }}
                        {% for error in form.address.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.position.label }}
                                {{ form.position(class="form-control") }}
                                {% for error in form.position.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.department.label }}
                                {{ form.department(class="form-control") }}
                                {% for error in form.department.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.status.label }}
                                {{ form.status(class="form-control") }}
                                {% for error in form.status.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.area_id.label }}
                                {{ form.area_id(class="form-control") }}
                                {% for error in form.area_id.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                {{ form.photo.label }}
                                {{ form.photo(class="form-control") }}
                                {% for error in form.photo.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                                {% if employee and employee.photo %}
                                <div class="mt-2">
                                    <img src="{{ url_for('static', filename=employee.photo) }}" alt="{{ employee.name }}" class="img-thumbnail" style="max-height: 100px;">
                                </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.entry_date.label }}
                                {{ form.entry_date(class="form-control", type="date") }}
                                {% for error in form.entry_date.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                {{ form.leave_date.label }}
                                {{ form.leave_date(class="form-control", type="date") }}
                                {% for error in form.leave_date.errors %}
                                <small class="text-danger">{{ error }}</small>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 食品安全责任 -->
            <div class="card mb-4">
                <div class="card-header">食品安全责任</div>
                <div class="card-body">
                    <div class="mb-3">
                        {{ form.responsible_areas.label }}
                        {{ form.responsible_areas(class="form-control", rows=3, placeholder="请输入员工负责的区域，多个区域用逗号分隔", id="responsible_areas") }}
                        {% for error in form.responsible_areas.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="form-text text-muted">例如：主食区,凉菜区,热菜区</small>

                        <div class="mt-2">
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="主食区" id="area1">
                                        <label class="form-check-label" for="area1">主食区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="凉菜区" id="area2">
                                        <label class="form-check-label" for="area2">凉菜区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="热菜区" id="area3">
                                        <label class="form-check-label" for="area3">热菜区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="面点区" id="area4">
                                        <label class="form-check-label" for="area4">面点区</label>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="水果区" id="area5">
                                        <label class="form-check-label" for="area5">水果区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="蔬菜区" id="area6">
                                        <label class="form-check-label" for="area6">蔬菜区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="肉类区" id="area7">
                                        <label class="form-check-label" for="area7">肉类区</label>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="form-check">
                                        <input class="form-check-input area-checkbox" type="checkbox" value="调料区" id="area8">
                                        <label class="form-check-label" for="area8">调料区</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        {{ form.food_safety_certifications.label }}
                        {{ form.food_safety_certifications(class="form-control", rows=3, placeholder="请输入员工持有的食品安全相关证书，多个证书用逗号分隔", id="food_safety_certifications") }}
                        {% for error in form.food_safety_certifications.errors %}
                        <small class="text-danger">{{ error }}</small>
                        {% endfor %}
                        <small class="form-text text-muted">例如：食品安全管理员证,厨师证,营养师证</small>

                        <div class="mt-2">
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input cert-checkbox" type="checkbox" value="食品安全管理员证" id="cert1">
                                        <label class="form-check-label" for="cert1">食品安全管理员证</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input cert-checkbox" type="checkbox" value="厨师证" id="cert2">
                                        <label class="form-check-label" for="cert2">厨师证</label>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input class="form-check-input cert-checkbox" type="checkbox" value="营养师证" id="cert3">
                                        <label class="form-check-label" for="cert3">营养师证</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统账号关联 -->
            <div class="card mb-4">
                <div class="card-header">系统账号关联</div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="form-check">
                            {{ form.create_user_account(class="form-check-input", id="create_user_account") }}
                            {{ form.create_user_account.label(class="form-check-label") }}
                            <small class="form-text text-muted ms-4">建议用户名为电话号码，初始密码123456</small>
                        </div>
                        <div class="alert alert-info mt-2">
                            <h6>系统账号说明：</h6>
                            <ul class="mb-0">
                                <li>创建系统账号后，用户可以登录系统并根据角色访问相应模块</li>
                                <li>如果未选择角色，系统将根据所属区域自动分配默认角色</li>
                                <li>用户只能访问自己所在区域及其下级区域的数据</li>
                                <li>不同角色拥有不同的权限和可见模块</li>
                            </ul>
                        </div>
                    </div>

                    <div id="system-account-section" class="system-account-section mt-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.username.label }}
                                    {{ form.username(class="form-control", id="username") }}
                                    {% for error in form.username.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    <small class="form-text text-muted">默认使用员工电话号码作为用户名</small>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    {{ form.password.label }}
                                    {{ form.password(class="form-control", type="password", value="123456") }}
                                    {% for error in form.password.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    <small class="form-text text-muted">默认初始密码为123456</small>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.roles.label }}
                            {{ form.roles(class="d-none", id="roles_hidden") }}
                            {% for error in form.roles.errors %}
                            <small class="text-danger">{{ error }}</small>
                            {% endfor %}

                            <div class="role-cards-container mt-2">
                                <div class="row" id="role-cards">
                                    <!-- 角色卡片将通过JavaScript动态生成 -->
                                </div>
                            </div>

                            <small class="form-text text-muted mt-2">点击卡片选择或取消选择角色，蓝色背景表示已选中</small>

                            <div class="mt-2">
                                <button type="button" class="btn btn-sm btn-outline-info" data-bs-toggle="collapse" data-bs-target="#roleInfoCollapse">
                                    查看角色说明
                                </button>
                                <div class="collapse mt-2" id="roleInfoCollapse">
                                    <div class="card card-body">
                                        <h6>常用角色说明：</h6>
                                        <ul class="mb-0">
                                            <li><strong>食堂管理员</strong>：管理食堂日常运营，包括菜单、库存、采购等</li>
                                            <li><strong>学校管理员</strong>：管理学校下属食堂，查看统计报表</li>
                                            <li><strong>乡镇管理员</strong>：管理乡镇下属学校，查看统计报表</li>
                                            <li><strong>区域管理员</strong>：管理县市区下属乡镇，查看统计报表</li>
                                            <li><strong>系统管理员</strong>：拥有系统所有权限</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if employee and employee.user %}
                    <div class="alert alert-info mt-3">
                        <h5>已关联系统账号</h5>
                        <p><strong>用户名：</strong>{{ employee.user.username }}</p>
                        <p><strong>角色：</strong>
                            {% for role in employee.user.roles %}
                            <span class="badge bg-primary">{{ role.name }}</span>
                            {% endfor %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>

            <div class="mb-3 text-center">
                <a href="{{ url_for('employee.index') }}" class="btn btn-secondary">取消</a>
                {{ form.submit(class="btn btn-primary") }}
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 系统账号关联显示/隐藏
        $('#create_user_account').change(function() {
            if($(this).is(':checked')) {
                $('#system-account-section').show();
                // 自动填充用户名为电话号码
                if($('#username').val() === '') {
                    $('#username').val($('#phone').val());
                }
            } else {
                $('#system-account-section').hide();
            }
        });

        // 初始化时检查
        if($('#create_user_account').is(':checked')) {
            $('#system-account-section').show();
            // 自动填充用户名为电话号码
            if($('#username').val() === '') {
                $('#username').val($('#phone').val());
            }
        }

        // 如果已有关联账号，默认不显示创建账号选项
        {% if employee and employee.user %}
            $('#create_user_account').prop('checked', false);
            $('#system-account-section').hide();
        {% endif %}

        // 当电话号码变化时，如果创建系统账号被勾选，则自动更新用户名
        $('#phone').change(function() {
            if($('#create_user_account').is(':checked')) {
                $('#username').val($(this).val());
            }
        });

        // 负责区域勾选功能
        $('.area-checkbox').change(function() {
            updateTextArea('#responsible_areas', '.area-checkbox');
        });

        // 食品安全证书勾选功能
        $('.cert-checkbox').change(function() {
            updateTextArea('#food_safety_certifications', '.cert-checkbox');
        });

        // 从文本框内容初始化勾选框状态
        initCheckboxesFromTextArea('#responsible_areas', '.area-checkbox');
        initCheckboxesFromTextArea('#food_safety_certifications', '.cert-checkbox');

        // 角色卡片相关功能
        // 角色数据
        var roleData = [
            {% for role in roles_data %}
            {
                id: {{ role.id }},
                name: '{{ role.name }}',
                description: '{{ role.description if role.description else "" }}',
                level: {% if role.name == '食堂管理员' %}4{% elif role.name == '学校管理员' %}3{% elif role.name == '乡镇管理员' %}2{% elif role.name == '区域管理员' %}1{% else %}0{% endif %}
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ];

        // 获取当前用户的区域级别
        var currentUserAreaLevel = {{ current_user.area_level if current_user.area_level else 0 }};

        // 获取当前选中的区域级别
        function getSelectedAreaLevel() {
            var areaId = $('#area_id').val();
            if(areaId && areaId > 0) {
                // 这里需要通过AJAX获取区域级别，但为了简化，我们使用一个映射
                var areaLevelMap = {
                    {% for area in form.area_id.choices %}
                    {% if area[0] > 0 %}
                    {{ area[0] }}: {{ area[1].split(' - ')[0]|int }},
                    {% endif %}
                    {% endfor %}
                };
                return areaLevelMap[areaId] || 0;
            }
            return 0;
        }

        // 生成角色卡片
        function generateRoleCards() {
            var $container = $('#role-cards');
            $container.empty();

            var selectedAreaLevel = getSelectedAreaLevel();
            var selectedRoles = $('#roles_hidden').val() ? $('#roles_hidden').val().split(',').map(Number) : [];

            roleData.forEach(function(role) {
                // 根据用户区域级别和选中区域级别决定是否禁用
                var isDisabled = false;

                // 如果当前用户不是系统管理员，且角色级别高于用户级别，则禁用
                if(currentUserAreaLevel > 0 && role.level < currentUserAreaLevel) {
                    isDisabled = true;
                }

                // 如果选中的区域级别与角色不匹配，也禁用
                if(selectedAreaLevel > 0 && role.level > 0 && role.level != selectedAreaLevel) {
                    isDisabled = true;
                }

                var isSelected = selectedRoles.includes(role.id);
                var cardClass = 'role-card' + (isSelected ? ' selected' : '') + (isDisabled ? ' disabled' : '');

                var $card = $('<div class="col-md-4 mb-3">' +
                    '<div class="' + cardClass" data-role-id="' + role.id + '">' +
                    '<div class="role-name">' + role.name + '</div>' +
                    '<div class="role-description">' + role.description + '</div>' +
                    '</div></div>');

                $container.append($card);
            });

            // 绑定点击事件
            $('.role-card:not(.disabled)').click(function() {
                var $this = $(this);
                var roleId = $this.data('role-id');

                $this.toggleClass('selected');

                // 更新隐藏的select
                updateSelectedRoles();
            });
        }

        // 更新选中的角色
        function updateSelectedRoles() {
            var selectedRoles = [];
            $('.role-card.selected').each(function() {
                selectedRoles.push($(this).data('role-id'));
            });

            // 更新隐藏的select
            var $select = $('#roles_hidden');
            $select.val(selectedRoles);
        }

        // 初始化角色卡片
        generateRoleCards();

        // 当区域变化时，重新生成角色卡片
        $('#area_id').change(function() {
            generateRoleCards();
        });

        // 更新文本区域函数
        function updateTextArea(textAreaId, checkboxClass) {
            var selectedValues = [];
            $(checkboxClass + ':checked').each(function() {
                selectedValues.push($(this).val());
            });

            // 获取文本框中已有的内容
            var currentText = $(textAreaId).val();
            var manualEntries = [];

            // 如果有内容，检查是否有手动输入的项目（不在勾选框中的）
            if(currentText) {
                var entries = currentText.split(',');
                entries.forEach(function(entry) {
                    entry = entry.trim();
                    // 检查该项是否在勾选框中
                    var found = false;
                    $(checkboxClass).each(function() {
                        if($(this).val() === entry) {
                            found = true;
                            return false; // 跳出循环
                        }
                    });

                    // 如果不在勾选框中，保留它
                    if(!found && entry) {
                        manualEntries.push(entry);
                    }
                });
            }

            // 合并勾选的值和手动输入的值
            var allValues = selectedValues.concat(manualEntries);
            $(textAreaId).val(allValues.join(','));
        }

        // 从文本框内容初始化勾选框状态
        function initCheckboxesFromTextArea(textAreaId, checkboxClass) {
            var currentText = $(textAreaId).val();
            if(currentText) {
                var entries = currentText.split(',');
                entries.forEach(function(entry) {
                    entry = entry.trim();
                    $(checkboxClass).each(function() {
                        if($(this).val() === entry) {
                            $(this).prop('checked', true);
                        }
                    });
                });
            }
        }
    });
</script>
{% endblock %}
