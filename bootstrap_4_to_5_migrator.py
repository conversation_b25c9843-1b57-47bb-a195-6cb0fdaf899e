#!/usr/bin/env python3
"""
Bootstrap 4 到 Bootstrap 5.3.6 迁移工具
自动查找并替换模板中的Bootstrap 4元素为Bootstrap 5.3.6元素
"""

import os
import re
from pathlib import Path
from typing import Dict, List, Tuple

class Bootstrap4To5Migrator:
    def __init__(self):
        self.project_root = Path(".")
        self.files_checked = 0
        self.files_modified = 0
        self.total_replacements = 0
        
        # Bootstrap 4 到 5 的映射规则
        self.migration_rules = {
            # 1. 数据属性变更 (data-* 改为 data-bs-*)
            'data_attributes': [
                (r'data-toggle="', 'data-bs-toggle="'),
                (r'data-target="', 'data-bs-target="'),
                (r'data-dismiss="', 'data-bs-dismiss="'),
                (r'data-slide="', 'data-bs-slide="'),
                (r'data-slide-to="', 'data-bs-slide-to="'),
                (r'data-parent="', 'data-bs-parent="'),
                (r'data-spy="', 'data-bs-spy="'),
                (r'data-offset="', 'data-bs-offset="'),
                (r'data-interval="', 'data-bs-interval="'),
                (r'data-pause="', 'data-bs-pause="'),
                (r'data-wrap="', 'data-bs-wrap="'),
                (r'data-keyboard="', 'data-bs-keyboard="'),
                (r'data-backdrop="', 'data-bs-backdrop="'),
                (r'data-focus="', 'data-bs-focus="'),
                (r'data-placement="', 'data-bs-placement="'),
                (r'data-container="', 'data-bs-container="'),
                (r'data-delay="', 'data-bs-delay="'),
                (r'data-html="', 'data-bs-html="'),
                (r'data-selector="', 'data-bs-selector="'),
                (r'data-template="', 'data-bs-template="'),
                (r'data-title="', 'data-bs-title="'),
                (r'data-trigger="', 'data-bs-trigger="'),
                (r'data-content="', 'data-bs-content="'),
                (r'data-boundary="', 'data-bs-boundary="'),
                (r'data-reference="', 'data-bs-reference="'),
                (r'data-display="', 'data-bs-display="'),
                (r'data-ride="', 'data-bs-ride="'),
            ],
            
            # 2. 类名变更
            'class_changes': [
                # 关闭按钮
                (r'class="close"', 'class="btn-close"'),
                (r'class="([^"]*\s+)?close(\s+[^"]*)??"', r'class="\1btn-close\2"'),
                
                # 表单控件
                (r'class="form-control-file"', 'class="form-control"'),
                (r'class="([^"]*\s+)?form-control-file(\s+[^"]*)??"', r'class="\1form-control\2"'),
                
                # 输入组
                (r'class="input-group-prepend"', 'class="input-group-text"'),
                (r'class="input-group-append"', 'class="input-group-text"'),
                (r'class="([^"]*\s+)?input-group-prepend(\s+[^"]*)??"', r'class="\1input-group-text\2"'),
                (r'class="([^"]*\s+)?input-group-append(\s+[^"]*)??"', r'class="\1input-group-text\2"'),
                
                # 媒体对象
                (r'class="media"', 'class="d-flex"'),
                (r'class="media-object"', 'class="flex-shrink-0"'),
                (r'class="media-body"', 'class="flex-grow-1 ms-3"'),
                (r'class="([^"]*\s+)?media(\s+[^"]*)??"', r'class="\1d-flex\2"'),
                (r'class="([^"]*\s+)?media-object(\s+[^"]*)??"', r'class="\1flex-shrink-0\2"'),
                (r'class="([^"]*\s+)?media-body(\s+[^"]*)??"', r'class="\1flex-grow-1 ms-3\2"'),
                
                # 徽章
                (r'class="badge badge-primary"', 'class="badge bg-primary"'),
                (r'class="badge badge-secondary"', 'class="badge bg-secondary"'),
                (r'class="badge badge-success"', 'class="badge bg-success"'),
                (r'class="badge badge-danger"', 'class="badge bg-danger"'),
                (r'class="badge badge-warning"', 'class="badge bg-warning"'),
                (r'class="badge badge-info"', 'class="badge bg-info"'),
                (r'class="badge badge-light"', 'class="badge bg-light"'),
                (r'class="badge badge-dark"', 'class="badge bg-dark"'),
                (r'class="badge badge-pill"', 'class="badge rounded-pill"'),
                
                # 按钮
                (r'class="btn-block"', 'class="d-grid"'),
                (r'class="([^"]*\s+)?btn-block(\s+[^"]*)??"', r'class="\1d-grid\2"'),
                
                # 间距类 (左右改为开始结束)
                (r'class="([^"]*\s+)?ml-(\d+)(\s+[^"]*)??"', r'class="\1ms-\2\3"'),
                (r'class="([^"]*\s+)?mr-(\d+)(\s+[^"]*)??"', r'class="\1me-\2\3"'),
                (r'class="([^"]*\s+)?pl-(\d+)(\s+[^"]*)??"', r'class="\1ps-\2\3"'),
                (r'class="([^"]*\s+)?pr-(\d+)(\s+[^"]*)??"', r'class="\1pe-\2\3"'),
                
                # 文本对齐
                (r'class="([^"]*\s+)?text-left(\s+[^"]*)??"', r'class="\1text-start\2"'),
                (r'class="([^"]*\s+)?text-right(\s+[^"]*)??"', r'class="\1text-end\2"'),
                
                # 浮动
                (r'class="([^"]*\s+)?float-left(\s+[^"]*)??"', r'class="\1float-start\2"'),
                (r'class="([^"]*\s+)?float-right(\s+[^"]*)??"', r'class="\1float-end\2"'),
                
                # 边框
                (r'class="([^"]*\s+)?border-left(\s+[^"]*)??"', r'class="\1border-start\2"'),
                (r'class="([^"]*\s+)?border-right(\s+[^"]*)??"', r'class="\1border-end\2"'),
                
                # 卡片
                (r'class="card-deck"', 'class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"'),
                (r'class="card-columns"', 'class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4"'),
                
                # 列表组
                (r'class="list-group-item-action"', 'class="list-group-item list-group-item-action"'),
                
                # 导航
                (r'class="navbar-toggler-icon"', 'class="navbar-toggler-icon"'),  # 保持不变
                
                # 表单
                (r'class="form-group"', 'class="mb-3"'),
                (r'class="([^"]*\s+)?form-group(\s+[^"]*)??"', r'class="\1mb-3\2"'),
                
                # 自定义控件
                (r'class="custom-control custom-checkbox"', 'class="form-check"'),
                (r'class="custom-control custom-radio"', 'class="form-check"'),
                (r'class="custom-control custom-switch"', 'class="form-check form-switch"'),
                (r'class="custom-control-input"', 'class="form-check-input"'),
                (r'class="custom-control-label"', 'class="form-check-label"'),
                (r'class="custom-select"', 'class="form-select"'),
                (r'class="custom-file"', 'class="mb-3"'),
                (r'class="custom-file-input"', 'class="form-control"'),
                (r'class="custom-file-label"', 'class="form-label"'),
                (r'class="custom-range"', 'class="form-range"'),
            ],
            
            # 3. HTML结构变更
            'structure_changes': [
                # 关闭按钮结构
                (r'<button[^>]*class="[^"]*close[^"]*"[^>]*>\s*<span[^>]*>&times;</span>\s*</button>', 
                 '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>'),
                
                # 输入组结构简化
                (r'<div class="input-group-prepend">\s*<span class="input-group-text">([^<]+)</span>\s*</div>', 
                 '<span class="input-group-text">\\1</span>'),
                (r'<div class="input-group-append">\s*<span class="input-group-text">([^<]+)</span>\s*</div>', 
                 '<span class="input-group-text">\\1</span>'),
            ],
            
            # 4. JavaScript API变更
            'javascript_changes': [
                (r'\.modal\(\'show\'\)', '.modal.show()'),
                (r'\.modal\(\'hide\'\)', '.modal.hide()'),
                (r'\.modal\(\'toggle\'\)', '.modal.toggle()'),
                (r'\.tooltip\(\'show\'\)', '.tooltip.show()'),
                (r'\.tooltip\(\'hide\'\)', '.tooltip.hide()'),
                (r'\.popover\(\'show\'\)', '.popover.show()'),
                (r'\.popover\(\'hide\'\)', '.popover.hide()'),
                (r'\.dropdown\(\'show\'\)', '.dropdown.show()'),
                (r'\.dropdown\(\'hide\'\)', '.dropdown.hide()'),
                (r'\.collapse\(\'show\'\)', '.collapse.show()'),
                (r'\.collapse\(\'hide\'\)', '.collapse.hide()'),
                (r'\.tab\(\'show\'\)', '.tab.show()'),
            ]
        }
    
    def migrate_file(self, file_path: Path) -> int:
        """迁移单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            file_changes = 0
            
            # 应用所有迁移规则
            for category, rules in self.migration_rules.items():
                for pattern, replacement in rules:
                    new_content = re.sub(pattern, replacement, content, flags=re.IGNORECASE | re.MULTILINE)
                    if new_content != content:
                        matches = len(re.findall(pattern, content, flags=re.IGNORECASE | re.MULTILINE))
                        file_changes += matches
                        content = new_content
                        print(f"   ✓ {category}: 替换了 {matches} 处 '{pattern[:50]}...'")
            
            # 如果有修改，写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                self.files_modified += 1
                self.total_replacements += file_changes
                return file_changes
            
            return 0
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
            return 0
    
    def scan_and_migrate(self):
        """扫描并迁移所有模板文件"""
        templates_dir = self.project_root / "app" / "templates"
        
        if not templates_dir.exists():
            print("❌ 模板目录不存在")
            return
        
        print("🔄 扫描并迁移Bootstrap 4到5.3.6...")
        print("=" * 60)
        
        # 处理所有HTML文件
        for html_file in templates_dir.rglob("*.html"):
            changes = self.migrate_file(html_file)
            self.files_checked += 1
            
            if changes > 0:
                rel_path = html_file.relative_to(self.project_root)
                print(f"✅ 迁移文件: {rel_path} - {changes} 处修改")
        
        # 处理JavaScript文件
        static_js_dir = self.project_root / "app" / "static" / "js"
        if static_js_dir.exists():
            for js_file in static_js_dir.rglob("*.js"):
                changes = self.migrate_file(js_file)
                self.files_checked += 1
                
                if changes > 0:
                    rel_path = js_file.relative_to(self.project_root)
                    print(f"✅ 迁移JS文件: {rel_path} - {changes} 处修改")
    
    def run(self):
        """运行迁移工具"""
        print("🚀 Bootstrap 4 到 5.3.6 迁移工具")
        print("=" * 60)
        
        self.scan_and_migrate()
        
        print("\n" + "=" * 60)
        print(f"📊 迁移完成:")
        print(f"   扫描文件: {self.files_checked} 个")
        print(f"   修改文件: {self.files_modified} 个")
        print(f"   总替换数: {self.total_replacements} 处")
        
        if self.total_replacements > 0:
            print(f"\n✨ 已成功迁移 {self.total_replacements} 处Bootstrap 4元素到5.3.6！")
            print(f"📝 主要变更:")
            print(f"   • data-* 属性改为 data-bs-*")
            print(f"   • 类名更新 (如 ml-/mr- 改为 ms-/me-)")
            print(f"   • 组件结构优化")
            print(f"   • JavaScript API更新")
            print(f"\n💡 建议:")
            print(f"   1. 测试所有页面功能")
            print(f"   2. 检查自定义CSS是否需要调整")
            print(f"   3. 验证JavaScript交互是否正常")
        else:
            print(f"\n✅ 没有发现需要迁移的Bootstrap 4元素")
            print(f"🎉 项目已经使用Bootstrap 5.3.6！")

if __name__ == "__main__":
    migrator = Bootstrap4To5Migrator()
    migrator.run()
