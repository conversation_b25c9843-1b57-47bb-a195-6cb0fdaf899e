{% extends 'base.html' %}

{% block title %}食材批次管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材批次管理</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('material_batch.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 新增批次
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="get" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>食材</label>
                                    <select name="ingredient_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>
                                            {{ ingredient.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>供应商</label>
                                    <select name="supplier_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>状态</label>
                                    <select name="status" class="form-control">
                                        <option value="">全部</option>
                                        <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                                        <option value="预警" {% if status == '预警' %}selected{% endif %}>预警</option>
                                        <option value="过期" {% if status == '过期' %}selected{% endif %}>过期</option>
                                        <option value="已用完" {% if status == '已用完' %}selected{% endif %}>已用完</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>临近过期天数</label>
                                    <input type="number" name="expiry_days" class="form-control" value="{{ expiry_days or '' }}" placeholder="天数">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label>区域</label>
                                    <select name="area_id" class="form-control">
                                        <option value="">全部</option>
                                        {% for area in areas %}
                                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>
                                            {{ area.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12 text-end">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-search"></i> 搜索
                                </button>
                                <a href="{{ url_for('material_batch.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-redo"></i> 重置
                                </a>
                            </div>
                        </div>
                    </form>

                    <!-- 批次列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>批次号</th>
                                    <th>食材</th>
                                    <th>供应商</th>
                                    <th>生产日期</th>
                                    <th>过期日期</th>
                                    <th>当前库存</th>
                                    <th>单位</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr>
                                    <td>{{ batch.batch_number }}</td>
                                    <td>{{ batch.ingredient.name }}</td>
                                    <td>{{ batch.supplier.name }}</td>
                                    <td>{{ batch.production_date }}</td>
                                    <td>{{ batch.expiry_date }}</td>
                                    <td>{{ batch.current_quantity }}</td>
                                    <td>{{ batch.unit }}</td>
                                    <td>
                                        {% if batch.status == '正常' %}
                                        <span class="badge bg-success">正常</span>
                                        {% elif batch.status == '预警' %}
                                        <span class="badge bg-warning">预警</span>
                                        {% elif batch.status == '过期' %}
                                        <span class="badge bg-danger">过期</span>
                                        {% elif batch.status == '已用完' %}
                                        <span class="badge bg-secondary">已用完</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-info btn-sm">
                                            <i class="fas fa-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('batch_flow.create', batch_id=batch.id) }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-exchange-alt"></i> 流水
                                        </a>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="9" class="text-center">暂无数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% include 'includes/pagination.html' %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
