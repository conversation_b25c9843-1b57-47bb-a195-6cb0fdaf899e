{% extends 'base.html' %}

{% block title %}查看留样记录{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">留样记录详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('food_sample.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <a href="{{ url_for('food_sample.print_sample', id=food_sample.id) }}" class="btn btn-secondary btn-sm" target="_blank">
                            <i class="fas fa-print"></i> 打印留样记录
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">留样编号</th>
                                    <td>{{ food_sample.sample_number }}</td>
                                </tr>
                                <tr>
                                    <th>区域</th>
                                    <td>{{ food_sample.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>食谱名称</th>
                                    <td>{{ food_sample.recipe.name }}</td>
                                </tr>
                                <tr>
                                    <th>用餐日期</th>
                                    <td>{{ food_sample.meal_date }}</td>
                                </tr>
                                <tr>
                                    <th>餐次</th>
                                    <td>{{ food_sample.meal_type }}</td>
                                </tr>
                                <tr>
                                    <th>留样数量</th>
                                    <td>{{ food_sample.sample_quantity }} {{ food_sample.sample_unit }}</td>
                                </tr>
                                <tr>
                                    <th>存储位置</th>
                                    <td>{{ food_sample.storage_location }}</td>
                                </tr>
                                <tr>
                                    <th>存储温度</th>
                                    <td>{{ food_sample.storage_temperature }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">留样时间</th>
                                    <td>{{  food_sample.start_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                                </tr>
                                <tr>
                                    <th>留样结束时间</th>
                                    <td>{{  food_sample.end_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if food_sample.status == '已留样' %}
                                        <span class="badge bg-info">已留样</span>
                                        {% elif food_sample.status == '已销毁' %}
                                        <span class="badge bg-secondary">已销毁</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>操作员</th>
                                    <td>{{ food_sample.operator.real_name or food_sample.operator.username }}</td>
                                </tr>
                                {% if food_sample.status == '已销毁' %}
                                <tr>
                                    <th>销毁时间</th>
                                    <td>{{  food_sample.destruction_time|format_datetime('%Y-%m-%d %H:%M')  }}</td>
                                </tr>
                                <tr>
                                    <th>销毁操作员</th>
                                    <td>{{ food_sample.destruction_operator.real_name or food_sample.destruction_operator.username }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ food_sample.created_at }}</td>
                                </tr>
                                <tr>
                                    <th>更新时间</th>
                                    <td>{{ food_sample.updated_at }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 留样图片 -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">留样图片</h4>
                                </div>
                                <div class="card-body text-center">
                                    {% if food_sample.sample_image %}
                                    <a href="{{ url_for('static', filename=food_sample.sample_image) }}" target="_blank">
                                        <img src="{{ url_for('static', filename=food_sample.sample_image) }}" alt="留样图片" style="max-height: 300px; max-width: 100%;">
                                    </a>
                                    {% else %}
                                    <p>无留样图片</p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 日菜单功能已移除，不再显示菜单计划信息 -->

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            {% if food_sample.status == '已留样' %}
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#destroyModal">
                                <i class="fas fa-trash"></i> 销毁留样
                            </button>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 销毁确认模态框 -->
<div class="modal fade" id="destroyModal" tabindex="-1" role="dialog" aria-labelledby="destroyModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="destroyModalLabel">确认销毁</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要销毁留样记录 <strong>{{ food_sample.sample_number }}</strong> 吗？此操作不可撤销。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('food_sample.destroy', id=food_sample.id) }}" method="post" novalidate novalidate><button type="submit" class="btn btn-danger">确认销毁</button>
                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
