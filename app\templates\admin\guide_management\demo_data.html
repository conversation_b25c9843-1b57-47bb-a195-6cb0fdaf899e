{% extends "base.html" %}

{% block title %}演示数据管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url_for('system.index') }}">系统管理</a></li>
                    <li class="breadcrumb-item"><a href="{{ url_for('guide_management.dashboard') }}">引导管理</a></li>
                    <li class="breadcrumb-item active">演示数据</li>
                </ol>
            </nav>
        </div>
        <div class="col-md-4 text-end">
            <button class="btn btn-danger" data-onclick="bulkCleanDemoData()">
                <i class="fas fa-trash me-1"></i>批量清理
            </button>
            <a href="{{ url_for('guide_management.dashboard') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-1"></i>返回
            </a>
        </div>
    </div>

    <!-- 演示数据统计 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ demo_stats.suppliers }}</h4>
                            <p class="mb-0">演示供应商</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-truck fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ demo_stats.ingredients }}</h4>
                            <p class="mb-0">演示食材</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-carrot fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ demo_stats.recipes }}</h4>
                            <p class="mb-0">演示食谱</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-utensils fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4>{{ demo_stats.total }}</h4>
                            <p class="mb-0">总演示数据</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-database fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 演示数据管理功能 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cogs me-2"></i>演示数据管理功能</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <div class="card border-primary">
                                <div class="card-body text-center">
                                    <i class="fas fa-search fa-3x text-primary mb-3"></i>
                                    <h5>查看演示数据</h5>
                                    <p class="text-muted">查看系统中所有标记为演示的数据</p>
                                    <button class="btn btn-primary" data-onclick="viewDemoData()">
                                        <i class="fas fa-eye me-1"></i>查看详情
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card border-warning">
                                <div class="card-body text-center">
                                    <i class="fas fa-broom fa-3x text-warning mb-3"></i>
                                    <h5>清理过期数据</h5>
                                    <p class="text-muted">清理超过30天的演示数据</p>
                                    <button class="btn btn-warning" data-onclick="cleanExpiredDemoData()">
                                        <i class="fas fa-broom me-1"></i>清理过期
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-4 mb-3">
                            <div class="card border-danger">
                                <div class="card-body text-center">
                                    <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                                    <h5>清空所有演示数据</h5>
                                    <p class="text-muted">删除系统中所有演示数据</p>
                                    <button class="btn btn-danger" data-onclick="clearAllDemoData()">
                                        <i class="fas fa-trash-alt me-1"></i>清空所有
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 演示数据详情 -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list me-2"></i>演示数据详情</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>说明：</strong>演示数据是为新用户引导创建的示例数据，包含"演示"、"demo"等标识。
                        这些数据帮助新用户快速了解系统功能，但在正式使用时应该清理。
                    </div>
                    
                    <!-- 演示数据分类展示 -->
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-truck me-2"></i>演示供应商数据</h6>
                            <div class="list-group mb-3">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    绿色农场有限公司
                                    <span class="badge bg-primary">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    新鲜蔬菜供应商
                                    <span class="badge bg-primary">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    优质肉类供应商
                                    <span class="badge bg-primary">演示</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6><i class="fas fa-carrot me-2"></i>演示食材数据</h6>
                            <div class="list-group mb-3">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示白菜
                                    <span class="badge bg-success">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示土豆
                                    <span class="badge bg-success">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示猪肉
                                    <span class="badge bg-success">演示</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="fas fa-utensils me-2"></i>演示食谱数据</h6>
                            <div class="list-group mb-3">
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示红烧肉
                                    <span class="badge bg-info">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示青菜汤
                                    <span class="badge bg-info">演示</span>
                                </div>
                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                    演示蒸蛋
                                    <span class="badge bg-info">演示</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <h6><i class="fas fa-cog me-2"></i>清理规则</h6>
                            <div class="alert alert-light">
                                <ul class="mb-0">
                                    <li>名称包含"演示"、"demo"的数据</li>
                                    <li>备注中标记为"演示数据"的记录</li>
                                    <li>创建时间超过30天的演示数据</li>
                                    <li>与演示数据关联的子记录</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 演示数据详情模态框 -->
<div class="modal fade" id="demoDataModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">演示数据详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="demoDataContent">
                <!-- 动态加载内容 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 查看演示数据详情
function viewDemoData() {
    $('#demoDataModal').modal('show');
    $('#demoDataContent').html(`
        <div class="text-center">
            <i class="fas fa-spinner fa-spin fa-2x"></i>
            <p>正在加载演示数据...</p>
        </div>
    `);
    
    // 这里可以添加AJAX请求获取详细数据
    setTimeout(() => {
        $('#demoDataContent').html(`
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>数据类型</th>
                            <th>名称</th>
                            <th>创建时间</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td><span class="badge bg-primary">供应商</span></td>
                            <td>绿色农场有限公司</td>
                            <td>2024-01-15 10:30:00</td>
                            <td><span class="badge bg-success">正常</span></td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-success">食材</span></td>
                            <td>演示白菜</td>
                            <td>2024-01-15 10:35:00</td>
                            <td><span class="badge bg-success">正常</span></td>
                        </tr>
                        <tr>
                            <td><span class="badge bg-info">食谱</span></td>
                            <td>演示红烧肉</td>
                            <td>2024-01-15 10:40:00</td>
                            <td><span class="badge bg-success">正常</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        `);
    }, 1000);
}

// 清理过期演示数据
function cleanExpiredDemoData() {
    if (confirm('确定要清理超过30天的演示数据吗？\n\n此操作不可恢复！')) {
        // 这里添加AJAX请求
        alert('过期演示数据清理功能开发中...');
    }
}

// 清空所有演示数据
function clearAllDemoData() {
    if (confirm('确定要清空所有演示数据吗？\n\n此操作将删除系统中所有标记为演示的数据，不可恢复！')) {
        if (confirm('请再次确认：这将删除所有演示供应商、食材、食谱等数据！')) {
            // 这里添加AJAX请求
            alert('清空所有演示数据功能开发中...');
        }
    }
}

// 批量清理演示数据
function bulkCleanDemoData() {
    if (confirm('确定要进行批量清理演示数据吗？\n\n系统将自动识别并清理所有演示数据。')) {
        // 这里添加AJAX请求
        alert('批量清理功能开发中...');
    }
}
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>