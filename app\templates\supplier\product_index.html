{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <small class="text-muted">管理供应商产品的审核、上架和下架操作</small>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('product_batch.index') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-layer-group"></i> 批次管理
                            </a>
                            <a href="{{ url_for('supplier_product.create') }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-plus"></i> 添加产品
                            </a>
                            <a href="{{ url_for('supplier.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回供应商列表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 搜索表单 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="supplier_id">供应商</label>
                                    <select class="form-control" id="supplier_id" name="supplier_id">
                                        <option value="">-- 所有供应商 --</option>
                                        {% for supplier in suppliers %}
                                        <option value="{{ supplier.id }}" {% if supplier_id == supplier.id %}selected{% endif %}>
                                            {{ supplier.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="ingredient_id">食材</label>
                                    <select class="form-control" id="ingredient_id" name="ingredient_id">
                                        <option value="">-- 所有食材 --</option>
                                        {% for ingredient in ingredients %}
                                        <option value="{{ ingredient.id }}" {% if ingredient_id == ingredient.id %}selected{% endif %}>
                                            {{ ingredient.name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="mb-3">
                                    <label for="status">状态</label>
                                    <select class="form-control" id="status" name="status">
                                        <option value="">-- 所有状态 --</option>
                                        <option value="1" {% if status == 1 %}selected{% endif %}>已上架</option>
                                        <option value="0" {% if status == 0 %}selected{% endif %}>未上架</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="keyword">关键词</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" value="{{ keyword }}" placeholder="产品名称/型号">
                                </div>
                            </div>
                            <div class="col-md-1">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <button type="submit" class="btn btn-primary w-100">搜索</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 批量操作工具栏 -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="card bg-light">
                                <div class="card-body py-2">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <button type="button" class="btn btn-sm btn-outline-primary" id="selectAllBtn">
                                                <i class="fas fa-check-square"></i> 全选
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-secondary" id="clearSelectionBtn">
                                                <i class="fas fa-square"></i> 清空
                                            </button>
                                            <span class="text-muted ms-2">已选择 <span id="selectedCount">0</span> 个产品</span>
                                        </div>
                                        <div>
                                            <button type="button" class="btn btn-sm btn-success" id="batchApproveBtn" disabled>
                                                <i class="fas fa-check"></i> 批量审核通过
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" id="batchRejectBtn" disabled>
                                                <i class="fas fa-times"></i> 批量拒绝
                                            </button>
                                            <button type="button" class="btn btn-sm btn-info" id="batchShelfBtn" disabled>
                                                <i class="fas fa-arrow-up"></i> 批量上架
                                            </button>
                                            <button type="button" class="btn btn-sm btn-warning" id="batchUnshelfBtn" disabled>
                                                <i class="fas fa-arrow-down"></i> 批量下架
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger" id="batchDeleteBtn" disabled>
                                                <i class="fas fa-trash"></i> 批量删除
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 产品列表 -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" id="selectAll" class="form-check-input">
                                    </th>
                                    <th>ID</th>
                                    <th>产品名称</th>
                                    <th>供应商</th>
                                    <th>食材</th>
                                    <th>型号/规格</th>
                                    <th>单价</th>
                                    <th>审核状态</th>
                                    <th>上架状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for product in products %}
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input product-checkbox"
                                               value="{{ product.id }}"
                                               data-shelf-status="{{ product.shelf_status }}"
                                               data-is-available="{{ product.is_available }}">
                                    </td>
                                    <td>{{ product.id }}</td>
                                    <td>{{ product.product_name or product.ingredient.name }}</td>
                                    <td>{{ product.supplier.name }}</td>
                                    <td>{{ product.ingredient.name }}</td>
                                    <td>
                                        {% if product.model_number %}
                                        <span class="badge bg-info">{{ product.model_number }}</span>
                                        {% endif %}
                                        {% if product.specification %}
                                        <span class="badge bg-secondary">{{ product.specification }}</span>
                                        {% endif %}
                                    </td>
                                    <td>¥{{ product.price }}</td>
                                    <td>
                                        {% if product.shelf_status == 0 %}
                                        <span class="badge bg-warning">待审核</span>
                                        {% elif product.shelf_status == 1 %}
                                        <span class="badge bg-success">已审核</span>
                                        {% else %}
                                        <span class="badge bg-danger">已拒绝</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if product.is_available == 1 %}
                                        <span class="badge bg-success">已上架</span>
                                        {% else %}
                                        <span class="badge bg-secondary">未上架</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{{ url_for('supplier_product.view', id=product.id) }}" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('supplier_product.edit', id=product.id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if product.shelf_status == 1 and product.is_available == 0 %}
                                            <button type="button" class="btn btn-sm btn-success shelf-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-arrow-up"></i>
                                            </button>
                                            {% elif product.is_available == 1 %}
                                            <button type="button" class="btn btn-sm btn-warning unshelf-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-arrow-down"></i>
                                            </button>
                                            {% endif %}
                                            {% if product.shelf_status == 0 %}
                                            <button type="button" class="btn btn-sm btn-success approve-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-check"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-danger reject-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-times"></i>
                                            </button>
                                            {% endif %}
                                            <button type="button" class="btn btn-sm btn-danger delete-btn" data-id="{{ product.id }}">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="10" class="text-center">暂无产品数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if pagination.pages > 1 %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if pagination.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_product.index', page=pagination.prev_num, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-left"></i></span>
                            </li>
                            {% endif %}

                            {% for page in pagination.iter_pages() %}
                                {% if page %}
                                    {% if page != pagination.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('supplier_product.index', page=page, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">{{ page }}</a>
                                    </li>
                                    {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page }}</span>
                                    </li>
                                    {% endif %}
                                {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                                {% endif %}
                            {% endfor %}

                            {% if pagination.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('supplier_product.index', page=pagination.next_num, supplier_id=supplier_id, ingredient_id=ingredient_id, status=status, keyword=keyword) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link"><i class="fas fa-chevron-right"></i></span>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这个产品吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmDelete">确认删除</button>
            </div>
        </div>
    </div>
</div>

<!-- 拒绝原因模态框 -->
<div class="modal fade" id="rejectModal" tabindex="-1" role="dialog" aria-labelledby="rejectModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rejectModalLabel">拒绝原因</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="rejectForm">
                    <div class="mb-3">
                        <label for="rejectReason">请输入拒绝原因</label>
                        <textarea class="form-control" id="rejectReason" rows="3" required></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" id="confirmReject">确认拒绝</button>
            </div>
        </div>
    </div>
</div>

<!-- 批量操作确认模态框 -->
<div class="modal fade" id="batchOperationModal" tabindex="-1" role="dialog" aria-labelledby="batchOperationModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="batchOperationModalLabel">批量操作确认</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="batchOperationContent">
                    <!-- 动态内容 -->
                </div>
                <div id="batchRejectReasonGroup" style="display: none;">
                    <div class="mb-3 mt-3">
                        <label for="batchRejectReason">请输入拒绝原因</label>
                        <textarea class="form-control" id="batchRejectReason" rows="3" required></textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="confirmBatchOperation">确认操作</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 批量操作相关变量
        var selectedProducts = [];
        var currentBatchOperation = null;

        // 更新选中状态
        function updateSelectionStatus() {
            selectedProducts = [];
            $('.product-checkbox:checked').each(function() {
                selectedProducts.push({
                    id: $(this).val(),
                    shelfStatus: $(this).data('shelf-status'),
                    isAvailable: $(this).data('is-available')
                });
            });

            var count = selectedProducts.length;
            $('#selectedCount').text(count);

            // 更新批量操作按钮状态
            var hasSelection = count > 0;
            $('#batchApproveBtn, #batchRejectBtn, #batchShelfBtn, #batchUnshelfBtn, #batchDeleteBtn').prop('disabled', !hasSelection);

            // 根据选中产品状态智能启用/禁用按钮
            if (hasSelection) {
                var canApprove = selectedProducts.some(p => p.shelfStatus == 0);
                var canReject = selectedProducts.some(p => p.shelfStatus == 0);
                var canShelf = selectedProducts.some(p => p.shelfStatus == 1 && p.isAvailable == 0);
                var canUnshelf = selectedProducts.some(p => p.isAvailable == 1);

                $('#batchApproveBtn').prop('disabled', !canApprove);
                $('#batchRejectBtn').prop('disabled', !canReject);
                $('#batchShelfBtn').prop('disabled', !canShelf);
                $('#batchUnshelfBtn').prop('disabled', !canUnshelf);
            }
        }

        // 全选/取消全选
        $('#selectAll').change(function() {
            $('.product-checkbox').prop('checked', this.checked);
            updateSelectionStatus();
        });

        $('#selectAllBtn').click(function() {
            $('.product-checkbox').prop('checked', true);
            $('#selectAll').prop('checked', true);
            updateSelectionStatus();
        });

        $('#clearSelectionBtn').click(function() {
            $('.product-checkbox').prop('checked', false);
            $('#selectAll').prop('checked', false);
            updateSelectionStatus();
        });

        // 单个复选框变化
        $(document).on('change', '.product-checkbox', function() {
            updateSelectionStatus();

            // 更新全选状态
            var totalCheckboxes = $('.product-checkbox').length;
            var checkedCheckboxes = $('.product-checkbox:checked').length;
            $('#selectAll').prop('checked', totalCheckboxes === checkedCheckboxes);
        });

        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("supplier_product.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });

        // 上架功能
        $('.shelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.shelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('上架失败，请稍后重试！');
                }
            });
        });

        // 下架功能
        $('.unshelf-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.unshelf_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('下架失败，请稍后重试！');
                }
            });
        });

        // 审核通过功能
        $('.approve-btn').click(function() {
            var productId = $(this).data('id');
            $.ajax({
                url: '{{ url_for("supplier_product.approve_product", id=0) }}'.replace('0', productId),
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        toastr.success(response.message);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message);
                    }
                },
                error: function() {
                    toastr.error('审核失败，请稍后重试！');
                }
            });
        });

        // 批量操作按钮事件
        $('#batchApproveBtn').click(function() {
            if (selectedProducts.length === 0) return;

            var eligibleProducts = selectedProducts.filter(p => p.shelfStatus == 0);
            if (eligibleProducts.length === 0) {
                toastr.warning('没有可审核的产品！');
                return;
            }

            currentBatchOperation = 'approve';
            $('#batchOperationModalLabel').text('批量审核通过');
            $('#batchOperationContent').html(`
                <p>确定要审核通过选中的 <strong>${eligibleProducts.length}</strong> 个产品吗？</p>
                <p class="text-muted">只有状态为"待审核"的产品会被处理。</p>
            `);
            $('#batchRejectReasonGroup').hide();
            $('#confirmBatchOperation').removeClass('btn-danger').addClass('btn-success').text('确认审核通过');
            $('#batchOperationModal').modal('show');
        });

        $('#batchRejectBtn').click(function() {
            if (selectedProducts.length === 0) return;

            var eligibleProducts = selectedProducts.filter(p => p.shelfStatus == 0);
            if (eligibleProducts.length === 0) {
                toastr.warning('没有可拒绝的产品！');
                return;
            }

            currentBatchOperation = 'reject';
            $('#batchOperationModalLabel').text('批量拒绝');
            $('#batchOperationContent').html(`
                <p>确定要拒绝选中的 <strong>${eligibleProducts.length}</strong> 个产品吗？</p>
                <p class="text-muted">只有状态为"待审核"的产品会被处理。</p>
            `);
            $('#batchRejectReasonGroup').show();
            $('#confirmBatchOperation').removeClass('btn-success').addClass('btn-danger').text('确认拒绝');
            $('#batchOperationModal').modal('show');
        });

        $('#batchShelfBtn').click(function() {
            if (selectedProducts.length === 0) return;

            var eligibleProducts = selectedProducts.filter(p => p.shelfStatus == 1 && p.isAvailable == 0);
            if (eligibleProducts.length === 0) {
                toastr.warning('没有可上架的产品！');
                return;
            }

            currentBatchOperation = 'shelf';
            $('#batchOperationModalLabel').text('批量上架');
            $('#batchOperationContent').html(`
                <p>确定要上架选中的 <strong>${eligibleProducts.length}</strong> 个产品吗？</p>
                <p class="text-muted">只有状态为"已审核且未上架"的产品会被处理。</p>
            `);
            $('#batchRejectReasonGroup').hide();
            $('#confirmBatchOperation').removeClass('btn-danger').addClass('btn-info').text('确认上架');
            $('#batchOperationModal').modal('show');
        });

        $('#batchUnshelfBtn').click(function() {
            if (selectedProducts.length === 0) return;

            var eligibleProducts = selectedProducts.filter(p => p.isAvailable == 1);
            if (eligibleProducts.length === 0) {
                toastr.warning('没有可下架的产品！');
                return;
            }

            currentBatchOperation = 'unshelf';
            $('#batchOperationModalLabel').text('批量下架');
            $('#batchOperationContent').html(`
                <p>确定要下架选中的 <strong>${eligibleProducts.length}</strong> 个产品吗？</p>
                <p class="text-muted">只有状态为"已上架"的产品会被处理。</p>
            `);
            $('#batchRejectReasonGroup').hide();
            $('#confirmBatchOperation').removeClass('btn-success').addClass('btn-warning').text('确认下架');
            $('#batchOperationModal').modal('show');
        });

        $('#batchDeleteBtn').click(function() {
            if (selectedProducts.length === 0) return;

            currentBatchOperation = 'delete';
            $('#batchOperationModalLabel').text('批量删除');
            $('#batchOperationContent').html(`
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i>
                    <strong>警告：</strong>此操作不可恢复！
                </div>
                <p>确定要删除选中的 <strong>${selectedProducts.length}</strong> 个产品吗？</p>
            `);
            $('#batchRejectReasonGroup').hide();
            $('#confirmBatchOperation').removeClass('btn-success btn-info btn-warning').addClass('btn-danger').text('确认删除');
            $('#batchOperationModal').modal('show');
        });

        // 拒绝功能
        var rejectId = null;

        $('.reject-btn').click(function() {
            rejectId = $(this).data('id');
            $('#rejectModal').modal('show');
        });

        $('#confirmReject').click(function() {
            if (rejectId) {
                var reason = $('#rejectReason').val();
                if (!reason) {
                    toastr.error('请输入拒绝原因！');
                    return;
                }

                $.ajax({
                    url: '{{ url_for("supplier_product.reject_product", id=0) }}'.replace('0', rejectId),
                    type: 'POST',
                    data: {
                        reason: reason
                    },
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#rejectModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('拒绝失败，请稍后重试！');
                        $('#rejectModal').modal('hide');
                    }
                });
            }
        });

        // 批量操作确认
        $('#confirmBatchOperation').click(function() {
            if (!currentBatchOperation || selectedProducts.length === 0) return;

            var productIds = selectedProducts.map(p => p.id);
            var data = { product_ids: productIds };

            // 如果是批量拒绝，需要添加拒绝原因
            if (currentBatchOperation === 'reject') {
                var reason = $('#batchRejectReason').val();
                if (!reason) {
                    toastr.error('请输入拒绝原因！');
                    return;
                }
                data.reason = reason;
            }

            var operationNames = {
                'approve': '审核通过',
                'reject': '拒绝',
                'shelf': '上架',
                'unshelf': '下架',
                'delete': '删除'
            };

            $.ajax({
                url: '{{ url_for("supplier_product.batch_operation") }}',
                type: 'POST',
                contentType: 'application/json',
                data: JSON.stringify({
                    operation: currentBatchOperation,
                    ...data
                }),
                success: function(response) {
                    if (response.success) {
                        toastr.success(`批量${operationNames[currentBatchOperation]}成功！处理了 ${response.processed_count} 个产品。`);
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    } else {
                        toastr.error(response.message || `批量${operationNames[currentBatchOperation]}失败！`);
                    }
                    $('#batchOperationModal').modal('hide');
                },
                error: function() {
                    toastr.error(`批量${operationNames[currentBatchOperation]}失败，请稍后重试！`);
                    $('#batchOperationModal').modal('hide');
                }
            });
        });

        // 清理模态框数据
        $('#batchOperationModal').on('hidden.bs.modal', function() {
            currentBatchOperation = null;
            $('#batchRejectReason').val('');
        });

        // 初始化选择状态
        updateSelectionStatus();
    });
</script>
{% endblock %}
