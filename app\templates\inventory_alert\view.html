{% extends 'base.html' %}

{% block title %}查看库存预警{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">库存预警详情</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory_alert.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">ID</th>
                                    <td>{{ inventory_alert.id }}</td>
                                </tr>
                                <tr>
                                    <th>区域</th>
                                    <td>{{ inventory_alert.area.name }}</td>
                                </tr>
                                <tr>
                                    <th>食材</th>
                                    <td>{{ inventory_alert.ingredient.name }}</td>
                                </tr>
                                <tr>
                                    <th>当前库存</th>
                                    <td>{{ inventory_alert.current_quantity }} {{ inventory_alert.unit }}</td>
                                </tr>
                                <tr>
                                    <th>最小库存</th>
                                    <td>{{ inventory_alert.min_quantity or '-' }} {{ inventory_alert.unit if inventory_alert.min_quantity else '' }}</td>
                                </tr>
                                <tr>
                                    <th>最大库存</th>
                                    <td>{{ inventory_alert.max_quantity or '-' }} {{ inventory_alert.unit if inventory_alert.max_quantity else '' }}</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">预警类型</th>
                                    <td>
                                        {% if inventory_alert.alert_type == '库存不足' %}
                                        <span class="badge bg-danger">库存不足</span>
                                        {% elif inventory_alert.alert_type == '库存过多' %}
                                        <span class="badge bg-warning">库存过多</span>
                                        {% elif inventory_alert.alert_type == '临近过期' %}
                                        <span class="badge bg-info">临近过期</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        {% if inventory_alert.status == '未处理' %}
                                        <span class="badge bg-warning">未处理</span>
                                        {% elif inventory_alert.status == '已处理' %}
                                        <span class="badge bg-success">已处理</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                <tr>
                                    <th>创建时间</th>
                                    <td>{{ inventory_alert.created_at }}</td>
                                </tr>
                                {% if inventory_alert.status == '已处理' %}
                                <tr>
                                    <th>处理人</th>
                                    <td>{{ inventory_alert.processor.real_name or inventory_alert.processor.username }}</td>
                                </tr>
                                <tr>
                                    <th>处理时间</th>
                                    <td>{{ inventory_alert.processed_at }}</td>
                                </tr>
                                {% endif %}
                                <tr>
                                    <th>备注</th>
                                    <td>{{ inventory_alert.notes or '-' }}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- 操作按钮 -->
                    <div class="row mt-4">
                        <div class="col-12 text-center">
                            {% if inventory_alert.status == '未处理' %}
                            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#processModal">
                                <i class="fas fa-check"></i> 处理预警
                            </button>
                            <a href="{{ url_for('inventory_alert.create_requisition', id=inventory_alert.id) }}" class="btn btn-success">
                                <i class="fas fa-shopping-cart"></i> 创建采购申请
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 处理确认模态框 -->
<div class="modal fade" id="processModal" tabindex="-1" role="dialog" aria-labelledby="processModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="processModalLabel">处理库存预警</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ url_for('inventory_alert.process', id=inventory_alert.id) }}" method="post" novalidate novalidate><div class="modal-body">
                    <div class="mb-3">
                        <label for="notes">处理备注</label>
                        <textarea name="notes" id="notes" class="form-control" rows="3" placeholder="请输入处理备注"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">确认处理</button>
                </div>
            
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
        </div>
    </div>
</div>
{% endblock %}
