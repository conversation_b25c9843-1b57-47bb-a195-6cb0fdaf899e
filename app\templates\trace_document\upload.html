{% extends 'base.html' %}

{% block title %}上传溯源文档{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">上传溯源文档</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回批次详情
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">批次信息</h4>
                                </div>
                                <div class="card-body">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">批次号</th>
                                            <td>{{ batch.batch_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>食材</th>
                                            <td>{{ batch.ingredient.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>供应商</th>
                                            <td>{{ batch.supplier.name }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <form method="post" enctype="multipart/form-data" novalidate novalidate>
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="document_type">文档类型 <span class="text-danger">*</span></label>
                                    <select name="document_type" id="document_type" class="form-control" required>
                                        <option value="">请选择文档类型</option>
                                        <option value="检验报告">检验报告</option>
                                        <option value="合格证">合格证</option>
                                        <option value="产地证明">产地证明</option>
                                        <option value="其他">其他</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="document_no">文档编号</label>
                                    <input type="text" name="document_no" id="document_no" class="form-control">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="document_file">文档文件 <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <div class="form-control">
                                    <input type="file" name="document_file" id="document_file" class="form-control-input" required>
                                    <label class="form-control-label" for="document_file">选择文件</label>
                                </div>
                            </div>
                            <small class="form-text text-muted">支持的文件格式：PDF, JPG, PNG, DOC, DOCX</small>
                        </div>

                        <div class="mb-3">
                            <label for="remark">备注</label>
                            <textarea name="remark" id="remark" class="form-control" rows="3"></textarea>
                        </div>

                        <div class="mb-3 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-upload"></i> 上传
                            </button>
                            <a href="{{ url_for('material_batch.view', id=batch.id) }}" class="btn btn-secondary">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(function() {
        // 确保文件输入元素正确初始化
        var fileInput = $('#document_file');
        var fileLabel = $('.form-control-label');

        // 修复文件选择功能
        function initFileUpload() {
            // 确保文件输入元素可以被点击
            fileInput.css({
                'position': 'absolute',
                'opacity': '0',
                'width': '100%',
                'height': '100%',
                'cursor': 'pointer',
                'z-index': '1'
            });

            // 点击标签时触发文件选择
            $('.form-control').off('click').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                fileInput[0].click();
            });

            // 显示选择的文件名
            fileInput.off('change').on('change', function() {
                var fileName = '';
                if (this.files && this.files.length > 0) {
                    fileName = this.files[0].name;
                } else {
                    fileName = $(this).val().split('\\').pop();
                }

                if (fileName) {
                    fileLabel.html(fileName);
                    fileLabel.addClass('file-selected');
                } else {
                    fileLabel.html('选择文件');
                    fileLabel.removeClass('file-selected');
                }
            });
        }

        // 初始化文件上传
        initFileUpload();

        // 表单提交前验证
        $('form').submit(function(e) {
            var fileInputElement = fileInput[0];
            if (!fileInputElement.files || fileInputElement.files.length === 0) {
                alert('请选择要上传的文件');
                e.preventDefault();
                return false;
            }

            var fileName = fileInputElement.files[0].name;
            var allowedExtensions = /(\.pdf|\.jpg|\.jpeg|\.png|\.doc|\.docx)$/i;
            if (!allowedExtensions.test(fileName)) {
                alert('请上传有效的文件类型：PDF, JPG, PNG, DOC, DOCX');
                fileInputElement.value = '';
                fileLabel.html('选择文件');
                fileLabel.removeClass('file-selected');
                e.preventDefault();
                return false;
            }

            return true;
        });
    });
</script>
<style nonce="{{ csp_nonce }}">
    .form-control {
        position: relative;
        cursor: pointer;
    }

    .form-control-input {
        position: absolute !important;
        opacity: 0 !important;
        width: 100% !important;
        height: 100% !important;
        cursor: pointer !important;
        z-index: 1 !important;
    }

    .form-control-label {
        cursor: pointer;
        border: 1px solid #ced4da;
        border-radius: 0.25rem;
        padding: 0.375rem 0.75rem;
        background-color: #fff;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }

    .form-control-label:hover {
        border-color: #80bdff;
    }

    .form-control-label.file-selected {
        color: #495057;
        background-color: #e9ecef;
    }
</style>
{% endblock %}
