{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">创建产品批次</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('product_batch.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-8 offset-md-2">
                            <!-- 步骤进度条 -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <small class="text-muted">创建产品批次流程</small>
                                    <small class="text-muted">步骤 1/5</small>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-success" role="progressbar" class="w-20" aria-valuenow="20" aria-valuemin="0" aria-valuemax="100"></div>
                                </div>
                                <div class="d-flex justify-content-between mt-2">
                                    <small class="text-success fw-bold">1. 基本信息</small>
                                    <small class="text-muted">2. 选择食材</small>
                                    <small class="text-muted">3. 设置属性</small>
                                    <small class="text-muted">4. 个性调整</small>
                                    <small class="text-muted">5. 确认创建</small>
                                </div>
                            </div>

                            <!-- 批次号生成说明 -->
                            <div class="alert alert-info mb-4">
                                <h6><i class="fas fa-info-circle"></i> 批次号自动生成规则</h6>
                                <p class="mb-2">系统将自动生成唯一的批次号：</p>
                                <ul class="mb-0">
                                    <li><strong>格式：</strong>PB + 日期(YYYYMMDD) + 6位随机数</li>
                                    <li><strong>示例：</strong>PB20240528123456</li>
                                    <li><strong>说明：</strong>PB表示产品批次，后面是日期和随机数，确保唯一性</li>
                                </ul>
                            </div>

                            <form method="post" novalidate novalidate>
                                {{ form.csrf_token }}

                                <div class="mb-3">
                                    {{ form.category_id.label }}
                                    {{ form.category_id(class="form-control") }}
                                    {% if form.category_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.category_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">选择产品所属的食材分类，如：蔬菜、肉类等</small>
                                </div>

                                <div class="mb-3">
                                    {{ form.supplier_id.label }}
                                    {{ form.supplier_id(class="form-control") }}
                                    {% if form.supplier_id.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.supplier_id.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <small class="form-text text-muted">选择产品的供应商</small>
                                </div>

                                <div class="mb-3 text-center">
                                    {{ form.submit(class="btn btn-primary") }}
                                    <a href="{{ url_for('product_batch.index') }}" class="btn btn-secondary">取消</a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
