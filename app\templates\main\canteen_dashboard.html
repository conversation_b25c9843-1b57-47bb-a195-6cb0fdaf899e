{% extends 'base.html' %}

{% block title %}食堂管理仪表盘 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/process_navigation.css') }}">
<style nonce="{{ csp_nonce }}">
    .dashboard-card {
        transition: all 0.3s;
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .menu-card {
        height: 100%;
    }
    .menu-item {
        border-bottom: 1px solid #eee;
        padding: 8px 0;
    }
    .menu-item:last-child {
        border-bottom: none;
    }
    .status-badge {
        font-size: 0.8rem;
    }
    .section-title {
        border-start: 4px solid var(--primary);
        padding-left: 10px;
        margin-bottom: 20px;
    }
    .quick-actions .btn {
        margin-bottom: 10px;
    }
    .calendar-day {
        text-align: center;
        padding: 10px;
        border: 1px solid #ddd;
        border-radius: 5px;
        margin-bottom: 10px;
    }
    .calendar-day.today {
        background-color: #f8f9fa;
        border-color: #007bff;
    }
    .calendar-day .date {
        font-weight: bold;
        margin-bottom: 5px;
    }
    .calendar-day .weekday {
        color: #6c757d;
        margin-bottom: 5px;
    }
    .calendar-day .menu-type {
        font-size: 0.8rem;
        margin: 2px 0;
    }
    .calendar-day .menu-type.has-menu {
        color: #28a745;
    }
    .calendar-day .menu-type.no-menu {
        color: #dc3545;
    }
    .inspection-item {
        padding: 8px 0;
        border-bottom: 1px solid #eee;
    }
    .inspection-item:last-child {
        border-bottom: none;
    }
    .inspection-item .badge {
        font-size: 0.8rem;
    }
</style>
{% endblock %}

{% block content %}
<!-- 导入流程导航组件 -->
{% from 'components/process_navigation.html' import process_navigation %}

<div class="row mb-4">
    <div class="col-md-8 col-12">
        <h2>食堂管理仪表盘</h2>
        <p class="text-muted">欢迎回来，{{ current_user.real_name or current_user.username }}</p>
    </div>
    <div class="col-md-4 col-12">
        <!-- 桌面端按钮 -->
        <div class="desktop-only text-end">
            <button id="refreshDashboard" class="btn btn-info">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary">
                <i class="fas fa-clipboard-list"></i> 日常管理
            </a>
        </div>
        <!-- 移动端按钮 -->
        <div class="mobile-only action-buttons">
            <button id="refreshDashboardMobile" class="btn btn-info">
                <i class="fas fa-sync-alt"></i> 刷新数据
            </button>
            <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary">
                <i class="fas fa-clipboard-list"></i> 日常管理
            </a>
        </div>
    </div>
</div>

<!-- 食堂管理流程导航 -->
<div class="row mb-4">
    <div class="col-12">
        {{ process_navigation(
            menu_plan=menu_plan,
            purchase_plan=purchase_plan,
            inspection=inspection,
            storage_in=storage_in,
            consumption_plan=consumption_plan,
            storage_out=storage_out,
            inventory=inventory,
            samples=samples,
            tracing=tracing,
            progress_percentage=progress_percentage,
            today_tasks=today_tasks,
            available_routes=[]
        ) }}
    </div>
</div>

<!-- 今日概览 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">今日概览 ({{ now.strftime('%Y-%m-%d') }})</h4>
    </div>
    <div class="col-md-3 col-sm-6 col-12 mobile-mb-2">
        <div class="card text-white bg-primary mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">今日就餐人数</h6>
                        <h2 class="mb-0" id="today-diners-count">--</h2>
                    </div>
                    <i class="fas fa-users fa-3x mobile-hidden"></i>
                    <i class="fas fa-users fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="#" class="text-white" id="today-diners-link">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 col-12 mobile-mb-2">
        <div class="card text-white bg-success mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">检查记录</h6>
                        <h2 class="mb-0" id="today-inspections-count">--</h2>
                    </div>
                    <i class="fas fa-clipboard-check fa-3x mobile-hidden"></i>
                    <i class="fas fa-clipboard-check fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="#" class="text-white" id="today-inspections-link">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 col-12 mobile-mb-2">
        <div class="card text-white bg-info mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">陪餐记录</h6>
                        <h2 class="mb-0" id="today-companions-count">--</h2>
                    </div>
                    <i class="fas fa-utensils fa-3x mobile-hidden"></i>
                    <i class="fas fa-utensils fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="#" class="text-white" id="today-companions-link">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-3 col-sm-6 col-12 mobile-mb-2">
        <div class="card text-white bg-warning mb-4">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="card-title">待处理问题</h6>
                        <h2 class="mb-0" id="pending-issues-count">--</h2>
                    </div>
                    <i class="fas fa-exclamation-triangle fa-3x mobile-hidden"></i>
                    <i class="fas fa-exclamation-triangle fa-2x mobile-only" style="display: none;"></i>
                </div>
            </div>
            <div class="card-footer d-flex justify-content-between mobile-hidden">
                <span>查看详情</span>
                <a href="#" class="text-white" id="pending-issues-link">
                    <i class="fas fa-arrow-circle-right"></i>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 今日菜单 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">今日菜单</h4>
    </div>
    <div class="col-md-4 col-12 mobile-mb-2">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">🌅 早餐</h5>
            </div>
            <div class="card-body" id="breakfast-menu">
                <div class="text-center py-4">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 col-12 mobile-mb-2">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">🌞 午餐</h5>
            </div>
            <div class="card-body" id="lunch-menu">
                <div class="text-center py-4">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4 col-12 mobile-mb-2">
        <div class="card menu-card mb-4">
            <div class="card-header bg-light">
                <h5 class="mb-0">🌙 晚餐</h5>
            </div>
            <div class="card-body" id="dinner-menu">
                <div class="text-center py-4">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 本周菜单安排 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">本周菜单安排</h4>
    </div>
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row" id="weekly-menu">
                    <div class="col-12 text-center py-4">
                        <p class="text-muted">加载中...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 食堂日常管理 -->
<div class="row">
    <div class="col-12">
        <h4 class="section-title">食堂日常管理</h4>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">今日检查记录</h5>
            </div>
            <div class="card-body" id="today-inspections">
                <div class="text-center py-4">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-sm btn-primary" id="add-inspection-btn">
                    <i class="fas fa-plus"></i> 添加检查记录
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="mb-0">待处理问题</h5>
            </div>
            <div class="card-body" id="pending-issues">
                <div class="text-center py-4">
                    <p class="text-muted">加载中...</p>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-sm btn-warning" id="add-issue-btn">
                    <i class="fas fa-plus"></i> 添加问题记录
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">快速操作</h4>
    </div>
    <div class="col-md-12">
        <div class="card">
            <div class="card-body quick-actions">
                <!-- 桌面端快速操作 -->
                <div class="row desktop-only">
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-primary w-100" id="create-daily-log-btn">
                            <i class="fas fa-clipboard"></i> 创建今日工作日志
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-success w-100" id="add-inspection-btn2">
                            <i class="fas fa-clipboard-check"></i> 添加检查记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-info w-100" id="add-companion-btn">
                            <i class="fas fa-utensils"></i> 添加陪餐记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-warning w-100" id="add-issue-btn2">
                            <i class="fas fa-exclamation-triangle"></i> 添加问题记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-secondary w-100" id="add-training-btn">
                            <i class="fas fa-chalkboard-teacher"></i> 添加培训记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-danger w-100" id="add-event-btn">
                            <i class="fas fa-calendar-day"></i> 添加特殊事件
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('food_sample.create') }}" class="btn btn-dark w-100">
                            <i class="fas fa-vial"></i> 添加留样记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('menu_plan.index') }}" class="btn btn-light w-100">
                            <i class="fas fa-calendar-alt"></i> 菜单计划管理
                        </a>
                    </div>
                </div>

                <!-- 移动端快速操作 -->
                <div class="mobile-only action-buttons">
                    <a href="#" class="btn btn-primary" id="create-daily-log-btn-mobile">
                        <i class="fas fa-clipboard"></i>
                        创建工作日志
                    </a>
                    <a href="#" class="btn btn-success" id="add-inspection-btn2-mobile">
                        <i class="fas fa-clipboard-check"></i>
                        检查记录
                    </a>
                    <a href="#" class="btn btn-info" id="add-companion-btn-mobile">
                        <i class="fas fa-utensils"></i>
                        陪餐记录
                    </a>
                    <a href="#" class="btn btn-warning" id="add-issue-btn2-mobile">
                        <i class="fas fa-exclamation-triangle"></i>
                        问题记录
                    </a>
                    <a href="#" class="btn btn-secondary" id="add-training-btn-mobile">
                        <i class="fas fa-chalkboard-teacher"></i>
                        培训记录
                    </a>
                    <a href="#" class="btn btn-danger" id="add-event-btn-mobile">
                        <i class="fas fa-calendar-day"></i>
                        特殊事件
                    </a>
                    <a href="{{ url_for('food_sample.create') }}" class="btn btn-dark">
                        <i class="fas fa-vial"></i>
                        留样记录
                    </a>
                    <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-light">
                        <i class="fas fa-calendar-alt"></i>
                        周菜单管理
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/process_navigation.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 刷新仪表盘数据 - 桌面端和移动端
        $('#refreshDashboard, #refreshDashboardMobile').click(function() {
            loadDashboardData();
        });

        // 加载仪表盘数据
        loadDashboardData();

        // 加载周菜单数据
        loadWeeklyMenuData();

        // 绑定快速操作按钮
        bindQuickActionButtons();

        // 绑定移动端快速操作按钮
        bindMobileQuickActionButtons();

    // 加载仪表盘数据
    function loadDashboardData() {
        $.ajax({
            url: '/api/v2/dashboard/summary',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                updateTodayOverview(data);
                updateTodayMenu(data);
                updateTodayInspections(data);
                updatePendingIssues(data);
            },
            error: function(xhr, status, error) {
                console.error('加载仪表盘数据失败:', error);
            }
        });
    }

    // 更新今日概览
    function updateTodayOverview(data) {
        // 更新就餐人数
        $('#today-diners-count').text(data.daily_log.total_count);

        // 更新检查记录数
        $('#today-inspections-count').text(data.inspections.count);

        // 更新陪餐记录数
        $('#today-companions-count').text(data.dining_companions.count);

        // 更新待处理问题数
        $('#pending-issues-count').text(data.issues.pending_count);

        // 更新链接
        if (data.daily_log.exists) {
            $('#today-diners-link').attr('href', '/daily-management/logs/' + data.daily_log.id);
            $('#today-inspections-link').attr('href', '/daily-management/logs/' + data.daily_log.id + '/inspections');
            $('#today-companions-link').attr('href', '/daily-management/logs/' + data.daily_log.id + '/companions');
        } else {
            $('#today-diners-link').attr('href', '/daily-management/logs/create?date=' + data.date);
            $('#today-inspections-link').attr('href', '/daily-management/logs/create?date=' + data.date);
            $('#today-companions-link').attr('href', '/daily-management/logs/create?date=' + data.date);
        }

        $('#pending-issues-link').attr('href', '/daily-management/issues?status=pending');
    }

    // 更新今日菜单
    function updateTodayMenu(data) {
        updateMealMenu('breakfast-menu', data.menu['早餐']);
        updateMealMenu('lunch-menu', data.menu['午餐']);
        updateMealMenu('dinner-menu', data.menu['晚餐']);
    }

    // 更新餐次菜单
    function updateMealMenu(elementId, menuData) {
        var element = $('#' + elementId);
        element.empty();

        if (!menuData) {
            element.html('<div class="text-center py-4"><p class="text-muted">暂无菜单</p></div>');
            return;
        }

        var html = '';
        html += '<div class="mb-2">';
        html += '<span class="badge badge-' + getStatusBadgeClass(menuData.status) ' status-badge">' + menuData.status + '</span>';
        html += '<span class="ms-2">预计就餐人数: ' + (menuData.expected_diners || '未设置') + '</span>';
        html += '</div>';

        if (menuData.recipes && menuData.recipes.length > 0) {
            html += '<div class="mt-3">';
            for (var i = 0; i < menuData.recipes.length; i++) {
                var recipe = menuData.recipes[i];
                html += '<div class="menu-item">';
                html += '<div class="d-flex justify-content-between">';
                html += '<div>' + recipe.name + '</div>';
                if (recipe.quantity) {
                    html += '<div>' + recipe.quantity + ' 份</div>';
                }
                html += '</div>';
                html += '</div>';
            }
            html += '</div>';
        } else {
            html += '<div class="text-center py-2"><p class="text-muted">暂无菜品</p></div>';
        }

        element.html(html);
    }

    // 获取状态徽章样式
    function getStatusBadgeClass(status) {
        switch (status) {
            case '计划中':
                return 'secondary';
            case '已发布':
                return 'primary';
            case '已执行':
                return 'success';
            default:
                return 'secondary';
        }
    }

    // 更新今日检查记录
    function updateTodayInspections(data) {
        var element = $('#today-inspections');
        element.empty();

        if (data.inspections.count === 0) {
            element.html('<div class="text-center py-4"><p class="text-muted">暂无检查记录</p></div>');
            return;
        }

        // 这里需要额外请求检查记录详情
        if (data.daily_log.exists) {
            $.ajax({
                url: '/api/v2/daily-logs/' + data.daily_log.id + '/inspections',
                type: 'GET',
                dataType: 'json',
                success: function(inspections) {
                    var html = '';
                    for (var i = 0; i < Math.min(inspections.length, 5); i++) {
                        var inspection = inspections[i];
                        html += '<div class="inspection-item">';
                        html += '<div class="d-flex justify-content-between">';
                        html += '<div>' + inspection.inspection_type + ' - ' + inspection.inspection_item + '</div>';
                        html += '<div><span class="badge badge-' + (inspection.result === 'normal' ? 'success' : 'danger') '">' + (inspection.result === 'normal' ? '正常' : '异常') + '</span></div>';
                        html += '</div>';
                        html += '</div>';
                    }
                    element.html(html);
                },
                error: function(xhr, status, error) {
                    element.html('<div class="text-center py-4"><p class="text-danger">加载检查记录失败</p></div>');
                }
            });
        } else {
            element.html('<div class="text-center py-4"><p class="text-muted">今日尚未创建工作日志</p></div>');
        }
    }

    // 更新待处理问题
    function updatePendingIssues(data) {
        var element = $('#pending-issues');
        element.empty();

        if (data.issues.pending_count === 0) {
            element.html('<div class="text-center py-4"><p class="text-muted">暂无待处理问题</p></div>');
            return;
        }

        // 这里需要额外请求待处理问题详情
        $.ajax({
            url: '/api/v2/issues?status=pending',
            type: 'GET',
            dataType: 'json',
            success: function(issues) {
                var html = '';
                for (var i = 0; i < Math.min(issues.length, 5); i++) {
                    var issue = issues[i];
                    html += '<div class="inspection-item">';
                    html += '<div class="d-flex justify-content-between">';
                    html += '<div>' + issue.title + '</div>';
                    html += '<div><span class="badge badge-' + getPriorityBadgeClass(issue.priority) '">' + issue.priority + '</span></div>';
                    html += '</div>';
                    html += '</div>';
                }
                element.html(html);
            },
            error: function(xhr, status, error) {
                element.html('<div class="text-center py-4"><p class="text-danger">加载待处理问题失败</p></div>');
            }
        });
    }

    // 获取优先级徽章样式
    function getPriorityBadgeClass(priority) {
        switch (priority) {
            case 'high':
                return 'danger';
            case 'normal':
                return 'warning';
            case 'low':
                return 'info';
            default:
                return 'secondary';
        }
    }

    // 加载周菜单数据
    function loadWeeklyMenuData() {
        $.ajax({
            url: '/api/v2/dashboard/weekly',
            type: 'GET',
            dataType: 'json',
            success: function(data) {
                updateWeeklyMenu(data);
            },
            error: function(xhr, status, error) {
                console.error('加载周菜单数据失败:', error);
                $('#weekly-menu').html('<div class="col-12 text-center py-4"><p class="text-danger">加载周菜单数据失败</p></div>');
            }
        });
    }

    // 更新周菜单
    function updateWeeklyMenu(data) {
        var element = $('#weekly-menu');
        element.empty();

        if (!data.week_data || data.week_data.length === 0) {
            element.html('<div class="col-12 text-center py-4"><p class="text-muted">暂无周菜单数据</p></div>');
            return;
        }

        var html = '';
        for (var i = 0; i < data.week_data.length; i++) {
            var day = data.week_data[i];
            var today = new Date().toISOString().split('T')[0];
            var isToday = day.date === today;

            html += '<div class="col-md-' + (12 / Math.min(7, data.week_data.length)) '">';
            html += '<div class="calendar-day' + (isToday ? ' today' : '')">';
            html += '<div class="date">' + day.date.substring(5) + '</div>';
            html += '<div class="weekday">' + day.weekday + '</div>';

            // 早餐
            if (day.menu && day.menu['早餐']) {
                html += '<div class="menu-type has-menu"><i class="fas fa-check-circle"></i> 早餐</div>';
            } else {
                html += '<div class="menu-type no-menu"><i class="fas fa-times-circle"></i> 早餐</div>';
            }

            // 午餐
            if (day.menu && day.menu['午餐']) {
                html += '<div class="menu-type has-menu"><i class="fas fa-check-circle"></i> 午餐</div>';
            } else {
                html += '<div class="menu-type no-menu"><i class="fas fa-times-circle"></i> 午餐</div>';
            }

            // 晚餐
            if (day.menu && day.menu['晚餐']) {
                html += '<div class="menu-type has-menu"><i class="fas fa-check-circle"></i> 晚餐</div>';
            } else {
                html += '<div class="menu-type no-menu"><i class="fas fa-times-circle"></i> 晚餐</div>';
            }

            html += '</div>';
            html += '</div>';
        }

        element.html(html);
    }

    // 绑定快速操作按钮
    function bindQuickActionButtons() {
        // 创建今日工作日志
        $('#create-daily-log-btn, #create-daily-log-btn2').click(function(e) {
            e.preventDefault();
            window.location.href = '/daily-management/logs/create?date=' + new Date().toISOString().split('T')[0];
        });

        // 添加检查记录
        $('#add-inspection-btn, #add-inspection-btn2').click(function(e) {
            e.preventDefault();
            // 需要先检查今日日志是否存在
            $.ajax({
                url: '/api/v2/dashboard/summary',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.daily_log.exists) {
                        window.location.href = '/daily-management/logs/' + data.daily_log.id + '/inspections/create';
                    } else {
                        alert('请先创建今日工作日志');
                        window.location.href = '/daily-management/logs/create?date=' + data.date;
                    }
                }
            });
        });

        // 添加陪餐记录
        $('#add-companion-btn').click(function(e) {
            e.preventDefault();
            // 需要先检查今日日志是否存在
            $.ajax({
                url: '/api/v2/dashboard/summary',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.daily_log.exists) {
                        window.location.href = '/daily-management/logs/' + data.daily_log.id + '/companions/create';
                    } else {
                        alert('请先创建今日工作日志');
                        window.location.href = '/daily-management/logs/create?date=' + data.date;
                    }
                }
            });
        });

        // 添加培训记录
        $('#add-training-btn').click(function(e) {
            e.preventDefault();
            // 需要先检查今日日志是否存在
            $.ajax({
                url: '/api/v2/dashboard/summary',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.daily_log.exists) {
                        window.location.href = '/daily-management/logs/' + data.daily_log.id + '/trainings/create';
                    } else {
                        alert('请先创建今日工作日志');
                        window.location.href = '/daily-management/logs/create?date=' + data.date;
                    }
                }
            });
        });

        // 添加特殊事件
        $('#add-event-btn').click(function(e) {
            e.preventDefault();
            // 需要先检查今日日志是否存在
            $.ajax({
                url: '/api/v2/dashboard/summary',
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    if (data.daily_log.exists) {
                        window.location.href = '/daily-management/logs/' + data.daily_log.id + '/events/create';
                    } else {
                        alert('请先创建今日工作日志');
                        window.location.href = '/daily-management/logs/create?date=' + data.date;
                    }
                }
            });
        });

        // 添加问题记录
        $('#add-issue-btn, #add-issue-btn2').click(function(e) {
            e.preventDefault();
            window.location.href = '/daily-management/issues/create';
        });
    }

    // 绑定移动端快速操作按钮
    function bindMobileQuickActionButtons() {
        // 移动端按钮与桌面端按钮功能相同，只是ID不同
        $('#create-daily-log-btn-mobile').click(function() {
            $('#create-daily-log-btn').click();
        });

        $('#add-inspection-btn2-mobile').click(function() {
            $('#add-inspection-btn2').click();
        });

        $('#add-companion-btn-mobile').click(function() {
            $('#add-companion-btn').click();
        });

        $('#add-issue-btn2-mobile').click(function() {
            $('#add-issue-btn2').click();
        });

        $('#add-training-btn-mobile').click(function() {
            $('#add-training-btn').click();
        });

        $('#add-event-btn-mobile').click(function() {
            $('#add-event-btn').click();
        });
    }

    });
</script>
{% endblock %}
