{% extends 'base.html' %}

{% block title %}凭证文本视图 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
  .text-view-container {
    margin-top: 20px;
  }
  .text-display {
    font-family: 'Courier New', 'Monaco', 'Menlo', monospace;
    font-size: 14px;
    line-height: 1.4;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 20px;
    white-space: pre-wrap;
    overflow-x: auto;
    min-height: 400px;
  }
  .text-display.dark-theme {
    background-color: #2d3748;
    color: #e2e8f0;
    border-color: #4a5568;
  }
  .text-controls {
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    border: 1px solid #dee2e6;
  }
  .font-size-control {
    display: inline-block;
    margin-right: 15px;
  }
  .theme-toggle {
    display: inline-block;
    margin-right: 15px;
  }
  .text-actions {
    display: inline-block;
  }
  .format-tabs {
    margin-bottom: 20px;
  }
  .format-tabs .nav-link {
    color: #495057;
    border: 1px solid #dee2e6;
    border-bottom: none;
  }
  .format-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
  }
  .search-box {
    position: relative;
    display: inline-block;
    margin-right: 15px;
  }
  .search-box input {
    padding-right: 35px;
  }
  .search-box .search-btn {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: #6c757d;
  }
  .highlight {
    background-color: #fff3cd;
    color: #856404;
  }
  .print-view {
    display: none;
  }
  @d-flex print {
    .no-print {
      display: none !important;
    }
    .print-view {
      display: block !important;
    }
    .text-display {
      border: none;
      background: white !important;
      color: black !important;
      font-size: 12px;
      padding: 0;
    }
  }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- 面包屑导航 -->
  <nav aria-label="breadcrumb" class="no-print">
    <ol class="breadcrumb">
      <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
      <li class="breadcrumb-item"><a href="{{ url_for('financial.reports_index') }}">财务管理</a></li>
      <li class="breadcrumb-item"><a href="{{ url_for('financial.vouchers_index') }}">记账凭证</a></li>
      <li class="breadcrumb-item"><a href="{{ url_for('financial.view_voucher', id=voucher.id) }}">{{ voucher.voucher_number }}</a></li>
      <li class="breadcrumb-item active">文本视图</li>
    </ol>
  </nav>

  <div class="row">
    <div class="col-md-12">
      <div class="card shadow mb-4">
        <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between no-print">
          <h6 class="m-0 fw-bold text-primary">
            <i class="fas fa-file-alt"></i> 凭证文本视图 - {{ voucher.voucher_number }}
          </h6>
          <div class="dropdown no-arrow">
            <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
              <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
            </a>
            <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in" aria-labelledby="dropdownMenuLink">
              <div class="dropdown-header">操作:</div>
              <a class="dropdown-item" href="#" onclick="copyToClipboard()"><i class="fas fa-copy fa-sm fa-fw me-2 text-gray-400"></i>复制文本</a>
              <a class="dropdown-item" href="#" onclick="downloadText()"><i class="fas fa-download fa-sm fa-fw me-2 text-gray-400"></i>下载文本</a>
              <a class="dropdown-item" href="#" onclick="printView()"><i class="fas fa-print fa-sm fa-fw me-2 text-gray-400"></i>打印</a>
              <div class="dropdown-divider"></div>
              <a class="dropdown-item" href="{{ url_for('financial.view_voucher', id=voucher.id) }}"><i class="fas fa-eye fa-sm fa-fw me-2 text-gray-400"></i>标准视图</a>
              <a class="dropdown-item" href="{{ url_for('financial.edit_voucher_professional', id=voucher.id) }}"><i class="fas fa-edit fa-sm fa-fw me-2 text-gray-400"></i>专业编辑器</a>
            </div>
          </div>
        </div>
        <div class="card-body">
          <!-- 格式选择标签 -->
          <ul class="nav nav-tabs format-tabs no-print" id="formatTabs" role="tablist">
            <li class="nav-item" role="presentation">
              <a class="nav-link active" id="standard-tab" data-bs-toggle="tab" href="#standard" role="tab" aria-controls="standard" aria-selected="true">
                <i class="fas fa-table"></i> 标准格式
              </a>
            </li>
            <li class="nav-item" role="presentation">
              <a class="nav-link" id="beancount-tab" data-bs-toggle="tab" href="#beancount" role="tab" aria-controls="beancount" aria-selected="false">
                <i class="fas fa-code"></i> Beancount格式
              </a>
            </li>
          </ul>

          <!-- 文本控制工具栏 -->
          <div class="text-controls no-print">
            <div class="row">
              <div class="col-md-8">
                <div class="font-size-control">
                  <label for="fontSize" class="form-label">字体大小:</label>
                  <select id="fontSize" class="form-control form-control-sm" style="width: auto; display: inline-block;">
                    <option value="12">12px</option>
                    <option value="14" selected>14px</option>
                    <option value="16">16px</option>
                    <option value="18">18px</option>
                    <option value="20">20px</option>
                  </select>
                </div>
                
                <div class="theme-toggle">
                  <label class="form-label">主题:</label>
                  <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-secondary active" id="lightTheme">浅色</button>
                    <button type="button" class="btn btn-outline-secondary" id="darkTheme">深色</button>
                  </div>
                </div>
                
                <div class="search-box">
                  <input type="text" class="form-control form-control-sm" id="searchText" placeholder="搜索文本...">
                  <button class="search-btn" onclick="searchInText()">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
              
              <div class="col-md-4 text-end">
                <div class="text-actions">
                  <button class="btn btn-primary btn-sm" onclick="copyToClipboard()">
                    <i class="fas fa-copy"></i> 复制
                  </button>
                  <button class="btn btn-success btn-sm" onclick="downloadText()">
                    <i class="fas fa-download"></i> 下载
                  </button>
                  <button class="btn btn-info btn-sm" onclick="printView()">
                    <i class="fas fa-print"></i> 打印
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 文本内容标签页 -->
          <div class="tab-content" id="formatTabsContent">
            <!-- 标准格式 -->
            <div class="tab-pane fade show active" id="standard" role="tabpanel" aria-labelledby="standard-tab">
              <div class="text-display" id="standardText">{{ voucher_text }}</div>
            </div>
            
            <!-- Beancount格式 -->
            <div class="tab-pane fade" id="beancount" role="tabpanel" aria-labelledby="beancount-tab">
              <div class="text-display" id="beancountText">{{ beancount_text }}</div>
            </div>
          </div>

          <!-- 打印视图 -->
          <div class="print-view">
            <div class="text-display" id="printText">{{ voucher_text }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
  // 字体大小控制
  $('#fontSize').on('change', function() {
    const fontSize = $(this).val() + 'px';
    $('.text-display').css('font-size', fontSize);
  });
  
  // 主题切换
  $('#lightTheme').on('click', function() {
    $('.text-display').removeClass('dark-theme');
    $('#lightTheme').addClass('active');
    $('#darkTheme').removeClass('active');
  });
  
  $('#darkTheme').on('click', function() {
    $('.text-display').addClass('dark-theme');
    $('#darkTheme').addClass('active');
    $('#lightTheme').removeClass('active');
  });
  
  // 标签页切换时更新打印内容
  $('a[data-bs-toggle="tab"]').on('shown.bs.tab', function (e) {
    const target = $(e.target).attr("href");
    const content = $(target + ' .text-display').text();
    $('#printText').text(content);
  });
  
  // 搜索功能
  $('#searchText').on('keyup', function(e) {
    if (e.key === 'Enter') {
      searchInText();
    }
  });
});

function copyToClipboard() {
  const activeTab = $('.tab-pane.active .text-display');
  const text = activeTab.text();
  
  navigator.clipboard.writeText(text).then(function() {
    showAlert('文本已复制到剪贴板', 'success');
  }).catch(function(err) {
    console.error('复制失败:', err);
    showAlert('复制失败，请手动选择文本复制', 'danger');
  });
}

function downloadText() {
  const activeTab = $('.tab-pane.active .text-display');
  const text = activeTab.text();
  const filename = '{{ voucher.voucher_number }}_文本视图.txt';
  
  const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  showAlert('文本文件下载完成', 'success');
}

function printView() {
  window.print();
}

function searchInText() {
  const searchTerm = $('#searchText').val().trim();
  const activeTab = $('.tab-pane.active .text-display');
  
  if (!searchTerm) {
    // 清除高亮
    const originalText = activeTab.attr('data-original') || activeTab.text();
    activeTab.html(originalText);
    return;
  }
  
  // 保存原始文本
  if (!activeTab.attr('data-original')) {
    activeTab.attr('data-original', activeTab.text());
  }
  
  const originalText = activeTab.attr('data-original');
  const regex = new RegExp(`(${searchTerm})`, 'gi');
  const highlightedText = originalText.replace(regex, '<span class="highlight">$1</span>');
  
  activeTab.html(highlightedText);
  
  const matchCount = (originalText.match(regex) || []).length;
  if (matchCount > 0) {
    showAlert(`找到 ${matchCount} 个匹配项`, 'info');
  } else {
    showAlert('未找到匹配项', 'warning');
  }
}

function showAlert(message, type) {
  const alertClass = type === 'danger' ? 'alert-danger' : 
                    type === 'success' ? 'alert-success' : 
                    type === 'warning' ? 'alert-warning' : 'alert-info';
  
  const alert = `
    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
    </div>
  `;
  
  // 移除旧的提示
  $('.alert').remove();
  
  // 添加新提示
  $('.card-body').prepend(alert);
  
  // 3秒后自动移除
  setTimeout(function() {
    $('.alert').fadeOut();
  }, 3000);
}
</script>
{% endblock %}
