{% extends "financial/base.html" %}

{% block title %}凭证汇总表{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">凭证汇总表</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('financial.reports_index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回报表列表
                        </a>
                        <button class="btn btn-success btn-sm" onclick="exportReport()">
                            <i class="fas fa-download"></i> 导出Excel
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 报表参数 -->
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="start_date">开始日期</label>
                                    <input type="date" class="form-control" id="start_date" name="start_date" 
                                           value="{{ start_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="end_date">结束日期</label>
                                    <input type="date" class="form-control" id="end_date" name="end_date" 
                                           value="{{ end_date }}">
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label for="voucher_type">凭证类型</label>
                                    <select class="form-control" id="voucher_type" name="voucher_type">
                                        <option value="">-- 所有类型 --</option>
                                        <option value="收款凭证" {% if voucher_type == '收款凭证' %}selected{% endif %}>收款凭证</option>
                                        <option value="付款凭证" {% if voucher_type == '付款凭证' %}selected{% endif %}>付款凭证</option>
                                        <option value="转账凭证" {% if voucher_type == '转账凭证' %}selected{% endif %}>转账凭证</option>
                                        <option value="记账凭证" {% if voucher_type == '记账凭证' %}selected{% endif %}>记账凭证</option>
                                        <option value="入库凭证" {% if voucher_type == '入库凭证' %}selected{% endif %}>入库凭证</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="mb-3">
                                    <label>&nbsp;</label>
                                    <div>
                                        <button type="submit" class="btn btn-info btn-sm">
                                            <i class="fas fa-sync"></i> 刷新报表
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- 凭证统计汇总 -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h5>凭证统计汇总</h5>
                            <div class="row">
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-info"><i class="fas fa-file-invoice"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">凭证总数</span>
                                            <span class="info-box-number">{{ summary.total_vouchers }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-success"><i class="fas fa-check-circle"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">已审核</span>
                                            <span class="info-box-number">{{ summary.reviewed_vouchers }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-warning"><i class="fas fa-clock"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">待审核</span>
                                            <span class="info-box-number">{{ summary.pending_vouchers }}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="info-box">
                                        <span class="info-box-icon bg-primary"><i class="fas fa-dollar-sign"></i></span>
                                        <div class="info-box-content">
                                            <span class="info-box-text">总金额</span>
                                            <span class="info-box-number">{{ "%.0f"|format(summary.total_amount) }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 按类型统计 -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>按凭证类型统计</h5>
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>凭证类型</th>
                                        <th class="text-end">数量</th>
                                        <th class="text-end">金额</th>
                                        <th class="text-end">占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for type_stat in summary.by_type %}
                                    <tr>
                                        <td>{{ type_stat.voucher_type }}</td>
                                        <td class="text-end">{{ type_stat.count }}</td>
                                        <td class="text-end">{{ "%.2f"|format(type_stat.amount) }}</td>
                                        <td class="text-end">{{ "%.1f"|format(type_stat.percentage) }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>按状态统计</h5>
                            <table class="table table-bordered">
                                <thead class="thead-light">
                                    <tr>
                                        <th>状态</th>
                                        <th class="text-end">数量</th>
                                        <th class="text-end">金额</th>
                                        <th class="text-end">占比</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for status_stat in summary.by_status %}
                                    <tr>
                                        <td>
                                            {% if status_stat.status == '草稿' %}
                                                <span class="badge bg-secondary">{{ status_stat.status }}</span>
                                            {% elif status_stat.status == '待审核' %}
                                                <span class="badge bg-warning">{{ status_stat.status }}</span>
                                            {% elif status_stat.status == '已审核' %}
                                                <span class="badge bg-success">{{ status_stat.status }}</span>
                                            {% elif status_stat.status == '已记账' %}
                                                <span class="badge bg-primary">{{ status_stat.status }}</span>
                                            {% else %}
                                                <span class="badge bg-light">{{ status_stat.status }}</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-end">{{ status_stat.count }}</td>
                                        <td class="text-end">{{ "%.2f"|format(status_stat.amount) }}</td>
                                        <td class="text-end">{{ "%.1f"|format(status_stat.percentage) }}%</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- 凭证明细列表 -->
                    <div class="row">
                        <div class="col-md-12">
                            <h5>凭证明细列表</h5>
                            {% if vouchers %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead class="thead-light">
                                        <tr>
                                            <th>凭证号</th>
                                            <th>日期</th>
                                            <th>类型</th>
                                            <th>摘要</th>
                                            <th class="text-end">金额</th>
                                            <th>状态</th>
                                            <th>创建人</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for voucher in vouchers %}
                                        <tr>
                                            <td>{{ voucher.voucher_number }}</td>
                                            <td>{{ voucher.voucher_date.strftime('%Y-%m-%d') }}</td>
                                            <td>{{ voucher.voucher_type }}</td>
                                            <td>{{ voucher.summary or '无摘要' }}</td>
                                            <td class="text-end">{{ "%.2f"|format(voucher.total_amount) }}</td>
                                            <td>
                                                {% if voucher.status == '草稿' %}
                                                    <span class="badge bg-secondary">{{ voucher.status }}</span>
                                                {% elif voucher.status == '待审核' %}
                                                    <span class="badge bg-warning">{{ voucher.status }}</span>
                                                {% elif voucher.status == '已审核' %}
                                                    <span class="badge bg-success">{{ voucher.status }}</span>
                                                {% elif voucher.status == '已记账' %}
                                                    <span class="badge bg-primary">{{ voucher.status }}</span>
                                                {% else %}
                                                    <span class="badge bg-light">{{ voucher.status }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ voucher.creator.username if voucher.creator else '未知' }}</td>
                                            <td>
                                                <a href="{{ url_for('financial.view_voucher', id=voucher.id) }}" 
                                                   class="btn btn-info btn-sm" title="查看">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info text-center">
                                <i class="fas fa-info-circle"></i> 暂无凭证数据
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 报表说明 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle"></i> 报表说明</h6>
                                <ul class="mb-0">
                                    <li>统计期间：{{ start_date }} 至 {{ end_date }}</li>
                                    <li>编制单位：{{ user_area.name }}</li>
                                    <li>金额单位：人民币元</li>
                                    <li>统计范围：{{ voucher_type if voucher_type else '所有类型' }}凭证</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportReport() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    const voucherType = document.getElementById('voucher_type').value;
    const url = `{{ url_for('financial.export_report', report_type='voucher_summary') }}?start_date=${startDate}&end_date=${endDate}&voucher_type=${voucherType}`;
    window.open(url, '_blank');
}
</script>
{% endblock %}
