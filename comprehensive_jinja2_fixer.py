#!/usr/bin/env python3
"""
全面的Jinja2语法检查和修复工具
专门修复常见的Jinja2语法错误模式
"""

import os
import re
from pathlib import Path

class ComprehensiveJinja2Fixer:
    def __init__(self):
        self.project_root = Path(".")
        self.files_checked = 0
        self.files_fixed = 0
        self.total_fixes = 0
        
        # 定义修复规则
        self.fix_patterns = [
            {
                'name': '修复错误的elif条件语句',
                'pattern': r'{% elif\s+([^}]+)\s*%}([^{]*){% \'([^\']+)\' %}([^{]*){% \'([^\']+)\' %}([^{]*){% else %}([^{]*){% endif %}',
                'replacement': r'{% elif \1 %}\2{% elif status == \'\3\' %}\4{% elif status == \'\5\' %}\6{% else %}\7{% endif %}',
                'description': '修复缺失elif关键字的条件语句'
            },
            {
                'name': '修复条件语句中的变量引用错误',
                'pattern': r'{% elif\s+unified_status\s*==\s*([^}]+)%}',
                'replacement': r'{% elif status == \1%}',
                'description': '修复错误的变量名引用'
            },
            {
                'name': '修复复杂的条件语句语法错误',
                'pattern': r'{% if\s+([^}]+)\s*%}([^{]*){% elif\s+([^}]+)\s*%}([^{]*){% \'([^\']+)\' %}([^{]*){% \'([^\']+)\' %}([^{]*){% else %}([^{]*){% endif %}',
                'replacement': r'{% if \1 %}\2{% elif \3 %}\4{% elif status == \'\5\' %}\6{% elif status == \'\7\' %}\8{% else %}\9{% endif %}',
                'description': '修复复杂条件语句的语法错误'
            },
            {
                'name': '修复嵌套if语句错误',
                'pattern': r'{% if\s+([^}]+)\s*%}{% if\s+([^}]+)\s*not in\s+([^}]+)\s*%}([^{]*){% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>',
                'replacement': r'{% if \1 and \2 not in \3 %}\4{% endif %}>',
                'description': '修复嵌套if语句语法错误'
            },
            {
                'name': '修复按钮disabled属性语法',
                'pattern': r'{% if\s+([^}]+)\s*%}{% if\s+variable not in\s+([^}]+)\s*%}disabled{% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>',
                'replacement': r'{% if \1 and \1 not in \2 %}disabled{% endif %}>',
                'description': '修复按钮disabled属性的语法错误'
            },
            {
                'name': '修复class属性中的条件语句',
                'pattern': r'class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                'replacement': r'class="\1{% if not \2 %} \3{% endif %}"',
                'description': '修复class属性中的条件语句语法'
            },
            {
                'name': '修复div标签中的条件语句',
                'pattern': r'<div class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                'replacement': r'<div class="\1{% if not \2 %} \3{% endif %}"',
                'description': '修复div标签中的条件语句语法'
            }
        ]
    
    def fix_specific_patterns(self, content: str) -> tuple[str, int]:
        """修复特定的语法模式"""
        changes_made = 0
        
        # 特殊修复：处理类似 inventory/index.html 和 stock_out/index.html 的错误
        # 模式：{% elif unified_status == '待检' %}warning{% '冻结' %}info{% '已过期' %}danger
        pattern1 = r'{% elif\s+unified_status\s*==\s*\'([^\']+)\'\s*%}([^{]*){% \'([^\']+)\' %}([^{]*){% \'([^\']+)\' %}([^{]*)'
        def fix_elif_chain(match):
            status1 = match.group(1)
            content1 = match.group(2)
            status2 = match.group(3)
            content2 = match.group(4)
            status3 = match.group(5)
            content3 = match.group(6)
            return f'{{% elif status == \'{status1}\' %}}{content1}{{% elif status == \'{status2}\' %}}{content2}{{% elif status == \'{status3}\' %}}{content3}'
        
        new_content = re.sub(pattern1, fix_elif_chain, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复更复杂的模式
        pattern2 = r'{% elif\s+([^}]+)\s*==\s*\'([^\']+)\'\s*%}([^{]*){% \'([^\']+)\' %}([^{]*){% \'([^\']+)\' %}([^{]*)'
        def fix_complex_elif(match):
            var_check = match.group(1)
            status1 = match.group(2)
            content1 = match.group(3)
            status2 = match.group(4)
            content2 = match.group(5)
            status3 = match.group(6)
            content3 = match.group(7)
            # 假设变量名应该是相同的对象
            base_var = var_check.split('.')[0] if '.' in var_check else var_check
            return f'{{% elif {base_var}.status == \'{status1}\' %}}{content1}{{% elif {base_var}.status == \'{status2}\' %}}{content2}{{% elif {base_var}.status == \'{status3}\' %}}{content3}'
        
        new_content = re.sub(pattern2, fix_complex_elif, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        return content, changes_made
    
    def fix_file(self, file_path: Path) -> int:
        """修复单个文件中的Jinja2语法错误"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            total_changes = 0
            
            # 应用特定模式修复
            content, specific_changes = self.fix_specific_patterns(content)
            total_changes += specific_changes
            
            # 应用通用修复规则
            for rule in self.fix_patterns:
                new_content = re.sub(
                    rule['pattern'], 
                    rule['replacement'], 
                    content, 
                    flags=re.MULTILINE | re.DOTALL
                )
                if new_content != content:
                    content = new_content
                    total_changes += 1
                    print(f"   ✓ {rule['name']}")
            
            # 清理和格式化
            if total_changes > 0:
                # 清理连续的空行
                content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
                # 清理行尾空白
                content = re.sub(r'[ \t]+\n', '\n', content)
            
            # 写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                return total_changes
            
            return 0
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
            return 0
    
    def scan_and_fix_templates(self):
        """扫描并修复所有模板文件"""
        templates_dir = self.project_root / "app" / "templates"
        
        if not templates_dir.exists():
            print("❌ 模板目录不存在")
            return
        
        print("🔧 全面检查和修复Jinja2模板语法错误...")
        print("=" * 60)
        
        for html_file in templates_dir.rglob("*.html"):
            changes = self.fix_file(html_file)
            self.files_checked += 1
            
            if changes > 0:
                self.files_fixed += 1
                self.total_fixes += changes
                rel_path = html_file.relative_to(self.project_root)
                print(f"✅ 修复文件: {rel_path} - {changes} 处修改")
    
    def run(self):
        """运行修复工具"""
        print("🚀 全面Jinja2模板语法修复工具")
        print("=" * 60)
        
        self.scan_and_fix_templates()
        
        print("\n" + "=" * 60)
        print(f"📊 修复完成:")
        print(f"   检查文件: {self.files_checked} 个")
        print(f"   修复文件: {self.files_fixed} 个")
        print(f"   总修复数: {self.total_fixes} 处")
        
        if self.total_fixes > 0:
            print(f"\n✨ 已修复 {self.total_fixes} 处Jinja2语法错误！")
            print(f"📝 主要修复类型:")
            print(f"   • 条件语句语法错误")
            print(f"   • 变量引用错误")
            print(f"   • 嵌套语句结构问题")
            print(f"   • 属性语法错误")
            print(f"\n💡 建议重新启动应用程序测试修复效果")
        else:
            print(f"\n✅ 没有发现需要修复的语法错误")
            print(f"🎉 所有模板文件语法正确！")

if __name__ == "__main__":
    fixer = ComprehensiveJinja2Fixer()
    fixer.run()
