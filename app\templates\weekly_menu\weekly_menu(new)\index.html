{% extends 'base.html' %}

{% block title %}周菜单列表 - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row mb-4">
    <div class="col-md-8">
      <h2>周菜单列表</h2>
      <p class="text-muted">查看所有周菜单计划</p>
    </div>
    <div class="col-md-4 text-end">
      <a href="{{ url_for('weekly_menu.plan') }}" class="btn btn-primary">
        <i class="fas fa-plus"></i> 新建周菜单
      </a>
    </div>
  </div>

  <!-- 筛选条件 -->
  <div class="card mb-4">
    <div class="card-header">
      <h5 class="mb-0">筛选条件</h5>
    </div>
    <div class="card-body">
      <form method="get" class="form-inline">
        <div class="mb-3 me-3 mb-2">
          <label for="area_id" class="me-2">区域</label>
          <select class="form-control" id="area_id" name="area_id">
            <option value="">全部区域</option>
            {% for area in areas %}
            <option value="{{ area.id }}" {% if area.id == area_id %}selected{% endif %}>
              {{ area.get_level_name() }} - {{ area.name }}
            </option>
            {% endfor %}
          </select>
        </div>
        <div class="mb-3 me-3 mb-2">
          <label for="status" class="me-2">状态</label>
          <select class="form-control" id="status" name="status">
            <option value="">全部状态</option>
            <option value="计划中" {% if status == '计划中' %}selected{% endif %}>计划中</option>
            <option value="已发布" {% if status == '已发布' %}selected{% endif %}>已发布</option>
          </select>
        </div>
        <div class="mb-3 me-3 mb-2">
          <label for="week" class="me-2">周期</label>
          <input type="week" class="form-control" id="week" name="week" value="{{ week }}">
        </div>
        <div class="mb-3 me-3 mb-2">
          <label for="start_date" class="me-2">开始日期</label>
          <input type="date" class="form-control" id="start_date" name="start_date" value="{{ start_date }}">
        </div>
        <div class="mb-3 me-3 mb-2">
          <label for="end_date" class="me-2">结束日期</label>
          <input type="date" class="form-control" id="end_date" name="end_date" value="{{ end_date }}">
        </div>
        <button type="submit" class="btn btn-primary mb-2">应用筛选</button>
        <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-secondary mb-2 ms-2">重置</a>
      </form>
    </div>
  </div>

  <!-- 周菜单列表 -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">周菜单列表</h5>
    </div>
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-hover">
          <thead>
            <tr>
              <th>ID</th>
              <th>区域</th>
              <th>周期</th>
              <th>状态</th>
              <th>创建人</th>
              <th>创建时间</th>
              <th>操作</th>
            </tr>
          </thead>
          <tbody>
            {% for weekly_menu in weekly_menus.items %}
            <tr>
              <td>{{ weekly_menu.id }}</td>
              <td>{{ weekly_menu.area.name }}</td>
              <td>{{ weekly_menu.week_display }}</td>
              <td>
                {% if weekly_menu.status == '计划中' %}
                <span class="badge bg-warning">计划中</span>
                {% elif weekly_menu.status == '已发布' %}
                <span class="badge bg-success">已发布</span>
                {% else %}
                <span class="badge bg-secondary">{{ weekly_menu.status }}</span>
                {% endif %}
              </td>
              <td>{{ weekly_menu.creator.real_name or weekly_menu.creator.username }}</td>
              <td>{{ weekly_menu.created_at|format_datetime }}</td>
              <td>
                <div class="btn-group btn-group-sm">
                  <a href="{{ url_for('weekly_menu.view', id=weekly_menu.id, t=now()|int) }}" class="btn btn-info" title="查看">
                    <i class="fas fa-eye"></i>
                  </a>
                  {% if weekly_menu.status == '计划中' %}
                  <a href="{{ url_for('weekly_menu.plan', area_id=weekly_menu.area_id, week_start=weekly_menu.week_start|format_datetime('%Y-%m-%d')) }}" class="btn btn-primary" title="编辑">
                    <i class="fas fa-edit"></i>
                  </a>
                  {% endif %}
                  <a href="{{ url_for('weekly_menu.print_menu', id=weekly_menu.id, t=now()|int) }}" class="btn btn-secondary" title="打印" target="_blank">
                    <i class="fas fa-print"></i>
                  </a>
                </div>
              </td>
            </tr>
            {% else %}
            <tr>
              <td colspan="7" class="text-center">暂无周菜单数据</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      {% if weekly_menus.pages > 1 %}
      <nav aria-label="Page navigation">
        <ul class="pagination justify-content-center">
          {% if weekly_menus.has_prev %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=weekly_menus.prev_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">上一页</a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">上一页</span>
          </li>
          {% endif %}

          {% for page_num in weekly_menus.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
          {% if page_num %}
          {% if page_num == weekly_menus.page %}
          <li class="page-item active">
            <span class="page-link">{{ page_num }}</span>
          </li>
          {% else %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=page_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">{{ page_num }}</a>
          </li>
          {% endif %}
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">...</span>
          </li>
          {% endif %}
          {% endfor %}

          {% if weekly_menus.has_next %}
          <li class="page-item">
            <a class="page-link" href="{{ url_for('weekly_menu.index', page=weekly_menus.next_num, area_id=area_id, status=status, start_date=start_date, end_date=end_date, week=week) }}">下一页</a>
          </li>
          {% else %}
          <li class="page-item disabled">
            <span class="page-link">下一页</span>
          </li>
          {% endif %}
        </ul>
      </nav>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}
