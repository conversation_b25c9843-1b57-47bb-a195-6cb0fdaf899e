#!/usr/bin/env python3
"""
快速修复Jinja2模板语法错误脚本
专门修复常见的语法错误模式
"""

import os
import re
from pathlib import Path

def fix_jinja2_syntax_errors(file_path):
    """修复单个文件中的Jinja2语法错误 - 重写版本"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        original_content = content
        changes_made = 0

        # 定义修复规则 - 使用更简洁的方式
        fix_rules = [
            # 规则1: 修复嵌套if语句错误
            {
                'pattern': r'{% if\s+([^}]+)\s*%}{% if\s+not in\s+([^}]+)\s*%}',
                'replacement': r'{% if \1 and variable not in \2 %}',
                'description': '修复嵌套if语句'
            },

            # 规则2: 修复复杂的按钮disabled属性
            {
                'pattern': r'{% if\s+([^}]+)\s*%}{% if\s+variable not in\s+([^}]+)\s*%}disabled{% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>',
                'replacement': r'{% if \1 and \1 not in \2 %}disabled{% endif %}>',
                'description': '修复按钮disabled属性'
            },

            # 规则3: 修复错误的class属性语法
            {
                'pattern': r'class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                'replacement': r'class="\1{% if not \2 %} \3{% endif %}"',
                'description': '修复class属性语法'
            },

            # 规则4: 修复div class语法错误
            {
                'pattern': r'<div class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                'replacement': r'<div class="\1{% if not \2 %} \3{% endif %}"',
                'description': '修复div class语法'
            },

            # 规则5: 修复一般属性语法错误
            {
                'pattern': r'(\w+)="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"',
                'replacement': r'\1="\2{% if not \3 %} \4{% endif %}"',
                'description': '修复一般属性语法'
            },

            # 规则6: 修复简单的嵌套错误
            {
                'pattern': r'{% if\s+([^}]+)\s*%}{% if\s+not in\s+([^}]+)\s*%}([^{]*){% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>',
                'replacement': r'{% if \1 and variable not in \2 %}\3{% endif %}>',
                'description': '修复复杂嵌套语法'
            }
        ]

        # 应用修复规则
        for rule in fix_rules:
            new_content = re.sub(
                rule['pattern'],
                rule['replacement'],
                content,
                flags=re.MULTILINE | re.DOTALL
            )
            if new_content != content:
                content = new_content
                changes_made += 1
                print(f"   ✓ {rule['description']}")

        # 清理和格式化
        if changes_made > 0:
            # 清理连续的空行
            content = re.sub(r'\n\s*\n\s*\n+', '\n\n', content)
            # 清理行尾空白
            content = re.sub(r'[ \t]+\n', '\n', content)
            # 清理多余的空格
            content = re.sub(r'  +', ' ', content)

        # 写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made

        return 0

    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return 0

def main():
    """主函数"""
    print("🔧 快速修复Jinja2模板语法错误")
    print("=" * 50)
    
    # 模板文件目录
    templates_dir = Path("app/templates")
    
    if not templates_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    total_files = 0
    total_changes = 0
    fixed_files = []
    
    # 处理所有HTML文件
    for html_file in templates_dir.rglob("*.html"):
        changes = fix_jinja2_syntax_errors(html_file)
        total_files += 1
        if changes > 0:
            total_changes += changes
            rel_path = html_file.relative_to(Path("."))
            fixed_files.append(str(rel_path))
            print(f"✅ 修复文件: {rel_path} - {changes} 处修改")
    
    print("\n" + "=" * 50)
    print(f"📊 修复完成:")
    print(f"   扫描文件: {total_files} 个")
    print(f"   修复文件: {len(fixed_files)} 个")
    print(f"   总修复数: {total_changes} 处")
    
    if total_changes > 0:
        print(f"\n✨ 已修复 {total_changes} 处Jinja2语法错误！")
        print(f"📄 修复的文件:")
        for file_path in fixed_files:
            print(f"   • {file_path}")
        print(f"\n💡 建议重新启动应用程序以确保修复生效")
    else:
        print(f"\n✅ 没有发现需要修复的语法错误")

if __name__ == "__main__":
    main()
