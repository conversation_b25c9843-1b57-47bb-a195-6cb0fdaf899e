#!/usr/bin/env python3
"""
快速修复Jinja2模板语法错误脚本
专门修复常见的语法错误模式
"""

import os
import re
from pathlib import Path

def fix_jinja2_syntax_errors(file_path):
    """修复单个文件中的Jinja2语法错误"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 修复1: 错误的嵌套if语句
        # 例如: {% if condition %}{% if not in list %} -> {% if condition and variable not in list %}
        pattern1 = r'{% if\s+([^}]+)\s*%}{% if\s+not in\s+([^}]+)\s*%}'
        def fix_nested_if_not_in(match):
            condition = match.group(1).strip()
            list_expr = match.group(2).strip()
            return f'{{% if {condition} and variable not in {list_expr} %}}'
        
        new_content = re.sub(pattern1, fix_nested_if_not_in, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复2: 错误的class属性语法
        # 例如: class="a {% if b %}c{% endif %} not d %}e{%"
        pattern2 = r'class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"'
        def fix_class_syntax(match):
            prefix = match.group(1).strip()
            condition = match.group(2).strip()
            suffix = match.group(3).strip()
            return f'class="{prefix}{{% if not {condition} %}} {suffix}{{% endif %}}"'
        
        new_content = re.sub(pattern2, fix_class_syntax, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复3: 错误的按钮disabled属性
        # 例如: {% if plan.status %}{% if variable not in ['已发布', '已执行'] %}disabled{% endif %}{% if {% if plan.status endif %}
        pattern3 = r'{% if\s+([^}]+)\s*%}{% if\s+variable not in\s+([^}]+)\s*%}disabled{% endif %}{% if\s+{% if\s+([^}]+)\s+endif\s*%}>'
        def fix_button_disabled(match):
            status_check = match.group(1).strip()
            status_list = match.group(2).strip()
            return f'{{% if {status_check} and {status_check} not in {status_list} %}}disabled{{% endif %}}>'
        
        new_content = re.sub(pattern3, fix_button_disabled, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复4: 简化的disabled属性错误
        # 例如: {% if plan.status %}{% if variable not in ['已发布', '已执行'] %}disabled{% endif %}{% if {% if plan.status endif %}>
        pattern4 = r'{% if\s+([^}]+)\s*%}{% if\s+variable not in\s+([^}]+)\s*%}disabled{% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>'
        new_content = re.sub(pattern4, fix_button_disabled, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复5: 更复杂的嵌套if错误
        # 例如: {% if condition %}{% if not in list %}disabled{% endif %}{% if {% if condition endif %}>
        pattern5 = r'{% if\s+([^}]+)\s*%}{% if\s+not in\s+([^}]+)\s*%}([^{]*){% endif %}{% if\s+{% if\s+([^}]+)\s*endif\s*%}>'
        def fix_complex_nested(match):
            condition = match.group(1).strip()
            list_expr = match.group(2).strip()
            content_between = match.group(3).strip()
            return f'{{% if {condition} and variable not in {list_expr} %}}{content_between}{{% endif %}}>'
        
        new_content = re.sub(pattern5, fix_complex_nested, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复6: 错误的div class语法
        # 例如: <div class="week-item {% if week.start_date == week_start %}active{% endif %} not week.is_editable %}not-editable{%"
        pattern6 = r'<div class="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"'
        def fix_div_class(match):
            prefix = match.group(1).strip()
            condition = match.group(2).strip()
            suffix = match.group(3).strip()
            return f'<div class="{prefix}{{% if not {condition} %}} {suffix}{{% endif %}}"'
        
        new_content = re.sub(pattern6, fix_div_class, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复7: 一般性的属性语法错误
        # 例如: attribute="value {% if condition %} not other_condition %}extra{%"
        pattern7 = r'(\w+)="([^"]*)\s+not\s+([^}]+)\}([^"]*)\{%"'
        def fix_general_attribute(match):
            attr_name = match.group(1)
            prefix = match.group(2).strip()
            condition = match.group(3).strip()
            suffix = match.group(4).strip()
            return f'{attr_name}="{prefix}{{% if not {condition} %}} {suffix}{{% endif %}}"'
        
        new_content = re.sub(pattern7, fix_general_attribute, content)
        if new_content != content:
            content = new_content
            changes_made += 1
        
        # 修复8: 清理多余的空白和换行
        if changes_made > 0:
            # 清理连续的空行
            content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
            # 清理行尾空白
            content = re.sub(r'[ \t]+\n', '\n', content)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return 0
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return 0

def main():
    """主函数"""
    print("🔧 快速修复Jinja2模板语法错误")
    print("=" * 50)
    
    # 模板文件目录
    templates_dir = Path("app/templates")
    
    if not templates_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    total_files = 0
    total_changes = 0
    fixed_files = []
    
    # 处理所有HTML文件
    for html_file in templates_dir.rglob("*.html"):
        changes = fix_jinja2_syntax_errors(html_file)
        total_files += 1
        if changes > 0:
            total_changes += changes
            rel_path = html_file.relative_to(Path("."))
            fixed_files.append(str(rel_path))
            print(f"✅ 修复文件: {rel_path} - {changes} 处修改")
    
    print("\n" + "=" * 50)
    print(f"📊 修复完成:")
    print(f"   扫描文件: {total_files} 个")
    print(f"   修复文件: {len(fixed_files)} 个")
    print(f"   总修复数: {total_changes} 处")
    
    if total_changes > 0:
        print(f"\n✨ 已修复 {total_changes} 处Jinja2语法错误！")
        print(f"📄 修复的文件:")
        for file_path in fixed_files:
            print(f"   • {file_path}")
        print(f"\n💡 建议重新启动应用程序以确保修复生效")
    else:
        print(f"\n✅ 没有发现需要修复的语法错误")

if __name__ == "__main__":
    main()
