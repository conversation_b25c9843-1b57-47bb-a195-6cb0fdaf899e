{% extends 'base.html' %}

{% block title %}编辑出库单
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">编辑出库单</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_out.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 出库单基本信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <table class="table table-bordered">
                                <tr>
                                    <th class="w-30">出库单号</th>
                                    <td>{{ stock_out.stock_out_number }}</td>
                                </tr>
                                <tr>
                                    <th>仓库</th>
                                    <td>{{ stock_out.warehouse.name }}</td>
                                </tr>
                                <tr>
                                    <th>状态</th>
                                    <td>
                                        <span class="badge bg-warning">{{ stock_out.status }}</span>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <form method="post" action="{{ url_for('stock_out.edit', id=stock_out.id) }}" novalidate novalidate>
        {{ csrf_token() }}
                <div class="mb-3">
                                    <label for="stock_out_date">出库日期 <span class="text-danger">*</span></label>
                                    <input type="date" class="form-control" id="stock_out_date" name="stock_out_date" value="{{  stock_out.stock_out_date|format_datetime('%Y-%m-%d')  }}" required>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="stock_out_type">出库类型 <span class="text-danger">*</span></label>
                                    <select class="form-control" id="stock_out_type" name="stock_out_type" required>
                                        <option value="">-- 请选择出库类型 --</option>
                                        <option value="消耗出库" {% if stock_out.stock_out_type == '消耗出库' %}selected{% endif %}>消耗出库</option>
                                        <option value="调拨出库" {% if stock_out.stock_out_type == '调拨出库' %}selected{% endif %}>调拨出库</option>
                                        <option value="报损出库" {% if stock_out.stock_out_type == '报损出库' %}selected{% endif %}>报损出库</option>
                                        <option value="退货出库" {% if stock_out.stock_out_type == '退货出库' %}selected{% endif %}>退货出库</option>
                                        <option value="其他出库" {% if stock_out.stock_out_type == '其他出库' %}selected{% endif %}>其他出库</option>
                                    </select>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="recipient">领用人</label>
                                    <input type="text" class="form-control" id="recipient" name="recipient" value="{{ stock_out.recipient or '' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="department">领用部门</label>
                                    <input type="text" class="form-control" id="department" name="department" value="{{ stock_out.department or '' }}">
                                </div>
                                
                                <div class="mb-3">
                                    <label for="notes">备注</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3">{{ stock_out.notes or '' }}</textarea>
                                </div>
                                
                                <div class="mb-3 text-end">
                                    <button type="submit" class="btn btn-primary">保存基本信息</button>
                                </div>
                            
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                        </div>
                    </div>
                    
                    <!-- 出库明细 -->
                    <div class="card mt-4">
                        <div class="card-header">
                            <h4 class="card-title">出库明细</h4>
                        </div>
                        <div class="card-body">
                            <!-- 添加明细表单 -->
                            <form method="post" action="{{ url_for('stock_out.add_item', id=stock_out.id) }}" class="mb-4" novalidate novalidate>
        {{ csrf_token() }}
                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="ingredient_id">食材 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="ingredient_id" name="ingredient_id" data-onchange="loadInventory(this.value)" required>
                                                <option value="">-- 请选择食材 --</option>
                                                {% for ingredient in ingredients %}
                                                {% if ingredient.id in inventory_by_ingredient %}
                                                <option value="{{ ingredient.id }}">{{ ingredient.name }}</option>
                                                {% endif %}
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="inventory_id">库存批次 <span class="text-danger">*</span></label>
                                            <select class="form-control" id="inventory_id" name="inventory_id" data-onchange="updateMaxQuantity()" required disabled>
                                                <option value="">-- 请先选择食材 --</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label for="quantity">出库数量 <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="quantity" name="quantity" step="0.01" min="0.01" required>
                                            <small id="quantity_help" class="form-text text-muted">可用库存: <span id="max_quantity">0</span> <span id="unit"></span></small>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="mb-3">
                                            <label for="notes">备注</label>
                                            <input type="text" class="form-control" id="notes" name="notes">
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label>&nbsp;</label>
                                            <button type="submit" class="btn btn-primary w-100">添加明细</button>
                                        </div>
                                    </div>
                                </div>
                            
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                            
                            <!-- 明细列表 -->
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>食材</th>
                                            <th>批次号</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>备注</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_out_items %}
                                        <tr>
                                            <td>{{ item.ingredient.name }}</td>
                                            <td>{{ item.batch_number }}</td>
                                            <td>{{ item.quantity }}</td>
                                            <td>{{ item.unit }}</td>
                                            <td>{{ item.notes or '-' }}</td>
                                            <td>
                                                <form action="{{ url_for('stock_out.remove_item', id=stock_out.id, item_id=item.id) }}" method="post" style="display: inline;" novalidate novalidate><button type="submit" class="btn btn-danger btn-sm" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                                                        <i class="fas fa-trash"></i> 删除
                                                    </button>
                                                
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                                            </td>
                                        </tr>
                                        {% else %}
                                        <tr>
                                            <td colspan="6" class="text-center">暂无出库明细，请添加</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 操作按钮 -->
                            <div class="row mt-4">
                                <div class="col-12 text-center">
                                    <a href="{{ url_for('stock_out.view', id=stock_out.id) }}" class="btn btn-info">
                                        <i class="fas fa-eye"></i> 查看详情
                                    </a>
                                    <form action="{{ url_for('stock_out.approve', id=stock_out.id) }}" method="post" style="display: inline;" novalidate novalidate><button type="submit" class="btn btn-success" {% if not stock_out_items %}disabled{% endif %}>
                                            <i class="fas fa-check"></i> 提交审核
                                        </button>
                                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                                    <form action="{{ url_for('stock_out.cancel', id=stock_out.id) }}" method="post" style="display: inline;" novalidate novalidate><button type="submit" class="btn btn-danger" data-action="critical-confirm" data-original-data-action="safe-confirm" data-confirm-code="return confirm(" style="cursor: pointer;">
                                            <i class="fas fa-times"></i> 取消出库单
                                        </button>
                                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" style="cursor: pointer;"></form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 库存数据
    const inventoryData = {
        {% for ingredient_id, inventories in inventory_by_ingredient.items() %}
        "{{ ingredient_id }}": [
            {% for inventory in inventories %}
            {
                "id": {{ inventory.id }},
                "batch_number": "{{ inventory.batch_number }}",
                "quantity": {{ inventory.quantity }},
                "unit": "{{ inventory.unit }}",
                "expiry_date": "{{  inventory.expiry_date|format_datetime('%Y-%m-%d')  }}"
            }{% if not loop.last %},{% endif %}
            {% endfor %}
        ]{% if not loop.last %},{% endif %}
        {% endfor %}
    };
    
    // 加载库存批次
    function loadInventory(ingredientId) {
        const inventorySelect = document.getElementById('inventory_id');
        inventorySelect.innerHTML = '<option value="">-- 请选择批次 --</option>';
        inventorySelect.disabled = true;
        
        if (!ingredientId) return;
        
        const inventories = inventoryData[ingredientId] || [];
        if (inventories.length > 0) {
            inventorySelect.disabled = false;
            inventories.forEach(inventory => {
                const option = document.createElement('option');
                option.value = inventory.id;
                option.textContent = `${inventory.batch_number} (${inventory.quantity}${inventory.unit}, 过期日期: ${inventory.expiry_date})`;
                option.dataset.quantity = inventory.quantity;
                option.dataset.unit = inventory.unit;
                inventorySelect.appendChild(option);
            });
        }
        
        updateMaxQuantity();
    }
    
    // 更新最大可用数量
    function updateMaxQuantity() {
        const inventorySelect = document.getElementById('inventory_id');
        const maxQuantitySpan = document.getElementById('max_quantity');
        const unitSpan = document.getElementById('unit');
        const quantityInput = document.getElementById('quantity');
        
        if (inventorySelect.value) {
            const selectedOption = inventorySelect.options[inventorySelect.selectedIndex];
            const maxQuantity = parseFloat(selectedOption.dataset.quantity);
            const unit = selectedOption.dataset.unit;
            
            maxQuantitySpan.textContent = maxQuantity;
            unitSpan.textContent = unit;
            quantityInput.max = maxQuantity;
            quantityInput.placeholder = `最大 ${maxQuantity} ${unit}`;
        } else {
            maxQuantitySpan.textContent = '0';
            unitSpan.textContent = '';
            quantityInput.max = '';
            quantityInput.placeholder = '';
        }
    }
</script>

{% endblock %}
