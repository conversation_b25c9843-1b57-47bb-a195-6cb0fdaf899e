/* 侧边栏布局系统 - 左侧固定导航+右侧内容区 */

/* === 基础布局结构 === */
.app-wrapper {
  display: flex;
  min-height: 100vh;
  position: relative;
}

/* 左侧固定导航栏 - 固定宽度200px */
.sidebar {
  width: 200px; /* 固定宽度200px，所有设备统一 */
  min-height: 100vh;
  background: var(--theme-primary, #2563eb);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 1000;
  transition: transform 0.3s ease;
  overflow-y: auto;
  /* box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
}

/* 侧边栏头部 */
.sidebar-header {
  padding: 1rem 1rem; /* 从1.5rem减小到1rem，更紧凑 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(0, 0, 0, 0.1);
}

.sidebar-brand {
  display: flex;
  align-items: center;
  text-decoration: none;
  color: white;
  font-size: 1rem; /* 从1.25rem减小到1rem，更紧凑 */
  font-weight: 500; /* 从600减小到500，不那么粗 */
}

.sidebar-brand:hover {
  color: white;
  text-decoration: none;
}

.sidebar-logo {
  height: 40px;
  max-width: 120px;
  object-fit: contain;
  margin-right: 12px;
}

.sidebar-brand-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.sidebar-school-name {
  font-size: 0.75rem; /* 从0.85rem减小到0.75rem */
  font-weight: 400;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 3px; /* 从4px减小到3px */
  padding: 3px 6px; /* 从4px 8px减小到3px 6px */
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px; /* 从6px减小到4px */
  display: inline-block;
}

/* 侧边栏导航菜单 */
.sidebar-nav {
  padding: 1rem 0;
}

.sidebar-nav-item {
  margin-bottom: 2px;
}

.sidebar-nav-link {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  transition: all 0.3s ease;
  border-start: 3px solid transparent;
  font-size: 14px;
}

.sidebar-nav-link:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
  border-start-color: rgba(255, 255, 255, 0.5);
}

.sidebar-nav-link.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  border-start-color: white;
  font-weight: 500;
}

.sidebar-nav-link i {
  width: 20px;
  margin-right: 12px;
  text-align: center;
  font-size: 16px;
}

/* 下拉菜单 */
.sidebar-dropdown {
  position: relative;
}

.sidebar-dropdown-toggle {
  position: relative;
}

.sidebar-dropdown-toggle::after {
  content: '\f107';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 20px;
  transition: transform 0.3s ease;
}

.sidebar-dropdown.show .sidebar-dropdown-toggle::after {
  transform: rotate(180deg);
}

.sidebar-dropdown-menu {
  background: rgba(0, 0, 0, 0.2);
  margin: 0;
  padding: 0;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
}

.sidebar-dropdown.show .sidebar-dropdown-menu {
  max-height: 500px;
}

.sidebar-dropdown-item {
  display: block;
  padding: 10px 20px 10px 52px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 13px;
  transition: all 0.3s ease;
}

.sidebar-dropdown-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  text-decoration: none;
}

.sidebar-dropdown-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
  font-weight: 500;
}

/* 右侧主内容区 */
.main-content {
  flex: 1;
  margin-left: 200px; /* 对应固定侧边栏宽度 */
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
}

/* 顶部工具栏 - 美丽的渐变效果，无毛玻璃 */
.top-toolbar {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-light, #60a5fa) 100%);
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1), 0 1px 3px rgba(0, 0, 0, 0.08); */ /* 移除阴影效果 */
  position: sticky;
  top: 0;
  z-index: 1060; /* 提高层级，确保按钮可点击 */
  color: white;
  /* 添加微妙的动画效果 */
  transition: all 0.3s ease;
  /* 添加光泽效果 */
  position: relative;
  overflow: hidden;
}

/* 添加光泽动画效果 - 无毛玻璃 */
.top-toolbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
  pointer-events: none;
}

.top-toolbar:hover::before {
  left: 100%;
}

.toolbar-left {
  display: flex;
  align-items: center;
}

.page-title {
  font-size: 1.1rem;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

.sidebar-toggle {
  background: none;
  border: none;
  font-size: 18px;
  color: rgba(255, 255, 255, 0.9); /* 白色，适配深色背景 */
  margin-right: 15px;
  padding: 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
  display: none; /* 默认隐藏，移动端显示 */
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1); /* 半透明白色背景 */
  color: white; /* 纯白色 */
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* 工具栏中的所有按钮样式 - 清晰的渐变背景效果，无毛玻璃 */
.top-toolbar .btn {
  border: none !important;
  background: rgba(255, 255, 255, 0.15) !important;
  color: rgba(255, 255, 255, 0.95) !important;
  border-radius: 8px;
  font-size: 14px;
  padding: 8px 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  position: relative;
  overflow: hidden;
}

/* 按钮光泽动画效果 - 无毛玻璃 */
.top-toolbar .btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
  pointer-events: none;
}

.top-toolbar .btn:hover::before {
  transform: translateX(100%);
}

.top-toolbar .btn:hover {
  background: rgba(255, 255, 255, 0.25) !important;
  color: white !important;
  transform: translateY(-1px);
  /* box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15); */ /* 移除阴影效果 */
}

.top-toolbar .btn:active {
  transform: translateY(0);
}

.top-toolbar .btn:focus {
  /* box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.3) !important; */ /* 移除阴影效果 */
  color: white !important;
}

/* 特别处理btn-link类 */
.top-toolbar .btn-link {
  text-decoration: none !important;
}

.top-toolbar .btn-link:hover {
  text-decoration: none !important;
}

/* 工具栏图标样式 */
.top-toolbar .fas {
  font-size: 16px;
  margin-right: 4px;
}

/* 通知徽章样式 */
.top-toolbar .badge {
  font-size: 10px;
  padding: 2px 6px;
}

/* 内容区域 */
.content-area {
  flex: 1;
  padding: 1.5rem;
  overflow-x: auto;
}

/* 页脚 */
.app-footer {
  background: white;
  padding: 1rem 1.5rem;
  border-top: 1px solid #e9ecef;
  text-align: center;
  color: #6c757d;
  font-size: 14px;
}

/* === 移动端适配 === */
@media (max-width: 768px) {
  /* 侧边栏在移动端默认隐藏 */
  .sidebar {
    transform: translateX(-100%);
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  /* 主内容区在移动端占满宽度 */
  .main-content {
    margin-left: 0;
  }
  
  /* 显示侧边栏切换按钮 */
  .sidebar-toggle {
    display: inline-block !important;
  }
  
  /* 移动端内容区域调整 */
  .content-area {
    padding: 1rem;
  }
  
  /* 侧边栏遮罩层 */
  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
  }
  
  .sidebar-overlay.show {
    display: block;
  }
}

/* === 主题适配 === */
[data-theme="primary"] .sidebar {
  background: var(--theme-primary, #2563eb);
}

[data-theme="secondary"] .sidebar {
  background: var(--theme-secondary, #6c757d);
}

[data-theme="success"] .sidebar {
  background: var(--theme-success, #198754);
}

[data-theme="warning"] .sidebar {
  background: var(--theme-warning, #fd7e14);
}

[data-theme="info"] .sidebar {
  background: var(--theme-info, #0dcaf0);
}

[data-theme="danger"] .sidebar {
  background: var(--theme-danger, #dc3545);
}

/* 顶部工具栏主题渐变适配 - 无毛玻璃效果 */
[data-theme="primary"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-light, #60a5fa) 100%);
}

[data-theme="secondary"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-secondary, #6c757d) 0%, #8e9aaf 100%);
}

[data-theme="success"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-success, #198754) 0%, #20c997 100%);
}

[data-theme="warning"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-warning, #fd7e14) 0%, #ffb347 100%);
}

[data-theme="info"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-info, #0dcaf0) 0%, #6edff6 100%);
}

[data-theme="danger"] .top-toolbar {
  background: linear-gradient(135deg, var(--theme-danger, #dc3545) 0%, #f1556c 100%);
}

/* 扩展主题渐变支持 */
[data-theme="classic-neutral"] .top-toolbar {
  background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
}

[data-theme="modern-neutral"] .top-toolbar {
  background: linear-gradient(135deg, #4a5568 0%, #718096 100%);
}

[data-theme="noble-elegant"] .top-toolbar {
  background: linear-gradient(135deg, #191970 0%, #483d8b 100%);
}

[data-theme="royal-solemn"] .top-toolbar {
  background: linear-gradient(135deg, #4b0082 0%, #663399 100%);
}

[data-theme="deep-sea-tech"] .top-toolbar {
  background: linear-gradient(135deg, #2563EB 0%, #8B5CF6 100%);
}

[data-theme="soft-morandi"] .top-toolbar {
  background: linear-gradient(135deg, #6D28D9 0%, #EC4899 100%);
}

[data-theme="minimal-dawn"] .top-toolbar {
  background: linear-gradient(135deg, #F97316 0%, #EF4444 100%);
}

[data-theme="dark-neon"] .top-toolbar {
  background: linear-gradient(135deg, #8B5CF6 0%, #F43F5E 100%);
}

[data-theme="nature-eco"] .top-toolbar {
  background: linear-gradient(135deg, #10B981 0%, #0EA5E9 100%);
}

/* === 动画效果 === */
.sidebar-nav-link,
.sidebar-dropdown-item {
  position: relative;
  overflow: hidden;
}

.sidebar-nav-link::before,
.sidebar-dropdown-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s ease;
}

.sidebar-nav-link:hover::before,
.sidebar-dropdown-item:hover::before {
  left: 100%;
}

/* === 滚动条样式 === */
.sidebar::-webkit-scrollbar {
  width: 6px;
}

.sidebar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

/* === 响应式断点优化 === */
@media (max-width: 576px) {
  .sidebar {
    width: 200px; /* 小屏幕也保持固定宽度200px */
  }

  .content-area {
    padding: 0.75rem;
  }

  .top-toolbar {
    padding: 0.75rem 1rem;
  }
}

/* 大屏幕保持固定宽度 - 无需特殊设置 */
@media (min-width: 1200px) {
  /* 侧边栏保持200px固定宽度，提供一致的用户体验 */
  .sidebar {
    width: 200px; /* 与基础宽度保持一致 */
  }

  .main-content {
    margin-left: 200px; /* 与基础宽度保持一致 */
  }
}
