{% extends 'base.html' %}

{% block title %}食材质量追溯{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">食材质量追溯 - 批次号: {{ batch_number }}</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('stock_in.index') }}" class="btn btn-secondary btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                        <button type="button" class="btn btn-primary btn-sm" data-onclick="printTrace()">
                            <i class="fas fa-print"></i> 打印追溯报告
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 追溯信息概览 -->
                    <div class="alert alert-info">
                        <h5><i class="icon fas fa-info"></i> 追溯信息概览</h5>
                        <p>批次号: <strong>{{ batch_number }}</strong></p>
                        <p>食材名称: <strong>{{ stock_in_items[0].ingredient.name }}</strong></p>
                        <p>食材分类: <strong>{{ stock_in_items[0].ingredient.category_rel.name if stock_in_items[0].ingredient.category_rel else '未分类' }}</strong></p>
                        <p>生产日期: <strong>{{ stock_in_items[0].production_date|format_datetime('%Y-%m-%d') }}</strong></p>
                        <p>过期日期: <strong>{{ stock_in_items[0].expiry_date|format_datetime('%Y-%m-%d') }}</strong></p>
                        <p>供应商: <strong>{{ stock_in_items[0].supplier.name if stock_in_items[0].supplier else '-' }}</strong></p>
                    </div>
                    
                    <!-- 追溯流程图 -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h4 class="card-title">追溯流程</h4>
                                </div>
                                <div class="card-body">
                                    <div class="timeline">
                                        {% if purchase_records %}
                                        <div class="time-label">
                                            <span class="bg-success">采购环节</span>
                                        </div>
                                        {% for record in purchase_records %}
                                        <div>
                                            <i class="fas fa-shopping-cart bg-success"></i>
                                            <div class="timeline-item">
                                                <span class="time"><i class="fas fa-clock"></i> {{ record.created_at|format_datetime('%Y-%m-%d %H:%M') }}</span>
                                                <h3 class="timeline-header">采购订单: {{ record.order.order_number }}</h3>
                                                <div class="timeline-body">
                                                    <p>食材: {{ record.ingredient.name }}</p>
                                                    <p>数量: {{ record.quantity }} {{ record.unit }}</p>
                                                    <p>单价: {{ record.unit_price }} 元</p>
                                                    <p>供应商: {{ record.order.supplier.name if record.order.supplier else '-' }}</p>
                                                </div>
                                                <div class="timeline-footer">
                                                    <a href="{{ url_for('purchase_order.view', id=record.order_id) }}" class="btn btn-primary btn-sm">查看采购订单</a>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        
                                        <div class="time-label">
                                            <span class="bg-info">入库环节</span>
                                        </div>
                                        {% for item in stock_in_items %}
                                        <div>
                                            <i class="fas fa-box bg-info"></i>
                                            <div class="timeline-item">
                                                <span class="time"><i class="fas fa-clock"></i> {{ item.created_at|format_datetime('%Y-%m-%d %H:%M') }}</span>
                                                <h3 class="timeline-header">入库单: {{ item.stock_in.stock_in_number }}</h3>
                                                <div class="timeline-body">
                                                    <p>食材: {{ item.ingredient.name }}</p>
                                                    <p>批次号: {{ item.batch_number }}</p>
                                                    <p>数量: {{ item.quantity }} {{ item.unit }}</p>
                                                    <p>生产日期: {{ item.production_date|format_datetime('%Y-%m-%d') }}</p>
                                                    <p>过期日期: {{ item.expiry_date|format_datetime('%Y-%m-%d') }}</p>
                                                    <p>存储位置: {{ item.storage_location.name }} ({{ item.storage_location.location_code }})</p>
                                                    <p>质量状态: {{ item.quality_status }}</p>
                                                </div>
                                                <div class="timeline-footer">
                                                    <a href="{{ url_for('stock_in.view', id=item.stock_in_id) }}" class="btn btn-info btn-sm">查看入库单</a>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                        
                                        {% if inspection_records %}
                                        <div class="time-label">
                                            <span class="bg-warning">检验环节</span>
                                        </div>
                                        {% for record in inspection_records %}
                                        <div>
                                            <i class="fas fa-microscope bg-warning"></i>
                                            <div class="timeline-item">
                                                <span class="time"><i class="fas fa-clock"></i> {{ record.created_at|format_datetime('%Y-%m-%d %H:%M') }}</span>
                                                <h3 class="timeline-header">检验记录</h3>
                                                <div class="timeline-body">
                                                    <p>检验类型: {{ record.inspection_type }}</p>
                                                    <p>检验结果: <span class="badge badge-{{ 'success' if record.result == '合格' else 'danger' }}">{{ record.result }}</span></p>
                                                    <p>检验人员: {{ record.inspector.real_name or record.inspector.username }}</p>
                                                    <p>检验日期: {{ record.inspection_date|format_datetime('%Y-%m-%d') }}</p>
                                                    <p>备注: {{ record.notes or '-' }}</p>
                                                </div>
                                                {% if record.document %}
                                                <div class="timeline-footer">
                                                    <a href="{{ url_for('static', filename=record.document.file_path) }}" target="_blank" class="btn btn-warning btn-sm">查看检验证明</a>
                                                </div>
                                                {% endif %}
                                            </div>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        
                                        {% if inventory_records %}
                                        <div class="time-label">
                                            <span class="bg-primary">库存环节</span>
                                        </div>
                                        {% for record in inventory_records %}
                                        <div>
                                            <i class="fas fa-warehouse bg-primary"></i>
                                            <div class="timeline-item">
                                                <span class="time"><i class="fas fa-clock"></i> {{ record.created_at|format_datetime('%Y-%m-%d %H:%M') }}</span>
                                                <h3 class="timeline-header">库存记录</h3>
                                                <div class="timeline-body">
                                                    <p>仓库: {{ record.warehouse.name }}</p>
                                                    <p>存储位置: {{ record.storage_location.name }} ({{ record.storage_location.location_code }})</p>
                                                    <p>当前数量: {{ record.quantity }} {{ record.unit }}</p>
                                                    <p>状态: {{ record.status }}</p>
                                                </div>
                                            </div>
                                        </div>
                                        {% endfor %}
                                        {% endif %}
                                        
                                        <div>
                                            <i class="fas fa-clock bg-gray"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    function printTrace() {
        window.print();
    }
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>