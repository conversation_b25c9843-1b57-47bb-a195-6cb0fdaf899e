{% extends 'base.html' %}

{% block content %}
<div class="container-fluid">
    <h1 class="h3 mb-4 text-gray-800">
        检查照片详情
    </h1>

    <!-- 功能按钮 -->
    <div class="mb-4">
        <a href="{{ url_for('daily_management.edit_inspection_photo', photo_id=photo.id) }}" class="btn btn-primary">
            <i class="fas fa-edit"></i> 编辑记录
        </a>
        <a href="{{ url_for('daily_management.simplified_inspection', log_id=photo.reference_id) }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回列表
        </a>
        <a href="{{ url_for('daily_management.print_inspection_photo_detail', photo_id=photo.id) }}" class="btn btn-info" target="_blank">
            <i class="fas fa-print"></i> 打印记录
        </a>
        <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal">
            <i class="fas fa-trash"></i> 删除记录
        </button>
    </div>

    <!-- 检查照片详情 -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">基本信息</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>检查类型：</strong> 
                                {% if photo.reference_type == 'morning' %}早晨检查
                                {% elif photo.reference_type == 'noon' %}中午检查
                                {% elif photo.reference_type == 'evening' %}晚上检查
                                {% endif %}
                            </p>
                            <p><strong>上传时间：</strong> {{ photo.upload_time|format_datetime }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>评分：</strong>
                                {% if photo.rating %}
                                    {% for i in range(photo.rating) %}
                                        <i class="fas fa-star text-warning"></i>
                                    {% endfor %}
                                    {% for i in range(5 - photo.rating) %}
                                        <i class="far fa-star text-warning"></i>
                                    {% endfor %}
                                {% else %}
                                    未评分
                                {% endif %}
                            </p>
                            <p><strong>上传者：</strong> {{ photo.uploader.name if photo.uploader else '系统' }}</p>
                        </div>
                    </div>

                    <div class="mt-3">
                        <p><strong>备注：</strong></p>
                        <div class="p-3 bg-light rounded">
                            {{ photo.description or '无' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">照片</h6>
                </div>
                <div class="card-body text-center">
                    <a href="{{ photo.file_path }}" target="_blank">
                        <img src="{{ photo.file_path }}" class="img-fluid rounded" alt="检查照片">
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteModalLabel">确认删除</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                确定要删除这张检查照片吗？此操作不可恢复。
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <form action="{{ url_for('daily_management.delete_inspection_photo', photo_id=photo.id) }}" method="post" novalidate novalidate>
                    {{ form.csrf_token }}
                    <button type="submit" class="btn btn-danger">确认删除</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
