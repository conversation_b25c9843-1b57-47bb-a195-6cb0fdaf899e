{% extends 'base.html' %}

{% block title %}权限配置帮助 - {{ super() }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        </div>
    <div class="col-md-4 text-end">
        <a href="{{ url_for('system.roles') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> 返回角色管理
        </a>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">权限配置基础</h5>
    </div>
    <div class="card-body">
        <p>权限配置使用JSON格式，基本结构是一个对象（字典），其中：</p>
        <ul>
            <li><strong>键（key）</strong>：表示系统中的模块名称</li>
            <li><strong>值（value）</strong>：是一个数组，包含该模块允许的操作</li>
        </ul>

        <h6>基本格式</h6>
        <pre class="bg-light p-3 rounded">
{
  "模块名1": ["操作1", "操作2", ...],
  "模块名2": ["操作1", "操作2", ...],
  ...
}</pre>

        <h6>特殊符号</h6>
        <ul>
            <li><code>"*"</code> 作为模块名：表示所有模块</li>
            <li><code>"*"</code> 作为操作：表示该模块的所有操作</li>
        </ul>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">系统中的模块和操作</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">用户管理 (user)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看用户</li>
                            <li><span class="badge bg-success">create</span> 创建用户</li>
                            <li><span class="badge bg-primary">edit</span> 编辑用户</li>
                            <li><span class="badge bg-danger">delete</span> 删除用户</li>
                            <li><span class="badge bg-warning">change_status</span> 修改用户状态</li>
                            <li><span class="badge bg-dark">reset_password</span> 重置密码</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">角色管理 (role)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看角色</li>
                            <li><span class="badge bg-success">create</span> 创建角色</li>
                            <li><span class="badge bg-primary">edit</span> 编辑角色</li>
                            <li><span class="badge bg-danger">delete</span> 删除角色</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">区域管理 (area)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看区域</li>
                            <li><span class="badge bg-success">create</span> 创建区域</li>
                            <li><span class="badge bg-primary">edit</span> 编辑区域</li>
                            <li><span class="badge bg-danger">delete</span> 删除区域</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">供应商管理 (supplier)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看供应商</li>
                            <li><span class="badge bg-success">create</span> 创建供应商</li>
                            <li><span class="badge bg-primary">edit</span> 编辑供应商</li>
                            <li><span class="badge bg-danger">delete</span> 删除供应商</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">食材管理 (ingredient)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看食材</li>
                            <li><span class="badge bg-success">create</span> 创建食材</li>
                            <li><span class="badge bg-primary">edit</span> 编辑食材</li>
                            <li><span class="badge bg-danger">delete</span> 删除食材</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-dark text-white">
                        <h6 class="mb-0">食谱管理 (menu)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看食谱</li>
                            <li><span class="badge bg-success">create</span> 创建食谱</li>
                            <li><span class="badge bg-primary">edit</span> 编辑食谱</li>
                            <li><span class="badge bg-danger">delete</span> 删除食谱</li>
                            <li><span class="badge bg-warning">approve</span> 审核食谱</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">菜单计划管理 (menu_plan)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看菜单计划</li>
                            <li><span class="badge bg-success">create</span> 创建菜单计划</li>
                            <li><span class="badge bg-primary">edit</span> 编辑菜单计划</li>
                            <li><span class="badge bg-danger">delete</span> 删除菜单计划</li>
                            <li><span class="badge bg-warning">approve</span> 审核菜单计划</li>
                            <li><span class="badge bg-dark">execute</span> 执行菜单计划</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-light text-dark">
                        <h6 class="mb-0">留样管理 (sample)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看留样</li>
                            <li><span class="badge bg-success">create</span> 创建留样</li>
                            <li><span class="badge bg-primary">edit</span> 编辑留样</li>
                            <li><span class="badge bg-danger">delete</span> 删除留样</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">系统设置 (setting)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看设置</li>
                            <li><span class="badge bg-primary">edit</span> 修改设置</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-secondary text-white">
                        <h6 class="mb-0">日志管理 (log)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看日志</li>
                            <li><span class="badge bg-secondary">export</span> 导出日志</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">报表管理 (report)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-info">view</span> 查看报表</li>
                            <li><span class="badge bg-secondary">export</span> 导出报表</li>
                            <li><span class="badge bg-dark">print</span> 打印报表</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-danger text-white">
                        <h6 class="mb-0">全局权限 (*)</h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li><span class="badge bg-danger">*</span> 所有操作</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">常见角色权限配置示例</h5>
    </div>
    <div class="card-body">
        <div class="accordion" id="permissionExamples">
            <div class="card">
                <div class="card-header" id="headingOne">
                    </div>
                <div id="collapseOne" class="collapse show" aria-labelledby="headingOne" data-parent="#permissionExamples">
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">
{
  "*": ["*"]
}</pre>
                        <p>这表示拥有系统中所有模块的所有操作权限，通常分配给超级管理员。</p>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingTwo">
                    </div>
                <div id="collapseTwo" class="collapse" aria-labelledby="headingTwo" data-parent="#permissionExamples">
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">
{
  "area": ["view"],
  "supplier": ["view"],
  "ingredient": ["view", "create", "edit"],
  "menu": ["view", "create", "edit", "delete"],
  "menu_plan": ["view", "create", "edit", "execute"],
  "sample": ["view", "create", "edit", "delete"],
  "report": ["view", "print"]
}</pre>
                        <p>这表示食堂管理员可以：</p>
                        <ul>
                            <li>查看区域和供应商信息</li>
                            <li>管理食材（查看、创建、编辑）</li>
                            <li>完全管理食谱（查看、创建、编辑、删除）</li>
                            <li>管理菜单计划（查看、创建、编辑、执行）</li>
                            <li>完全管理留样（查看、创建、编辑、删除）</li>
                            <li>查看和打印报表</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingThree">
                    </div>
                <div id="collapseThree" class="collapse" aria-labelledby="headingThree" data-parent="#permissionExamples">
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">
{
  "area": ["view"],
  "user": ["view", "create", "edit"],
  "supplier": ["view", "create", "edit"],
  "ingredient": ["view"],
  "menu": ["view", "approve"],
  "menu_plan": ["view", "approve"],
  "sample": ["view"],
  "report": ["view", "export", "print"]
}</pre>
                        <p>这表示学校管理员可以：</p>
                        <ul>
                            <li>查看区域信息</li>
                            <li>管理用户（查看、创建、编辑）</li>
                            <li>管理供应商（查看、创建、编辑）</li>
                            <li>查看食材信息</li>
                            <li>查看和审核食谱</li>
                            <li>查看和审核菜单计划</li>
                            <li>查看留样记录</li>
                            <li>查看、导出和打印报表</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingFour">
                    </div>
                <div id="collapseFour" class="collapse" aria-labelledby="headingFour" data-parent="#permissionExamples">
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">
{
  "user": ["view"],
  "role": ["view"],
  "area": ["view"],
  "supplier": ["view"],
  "ingredient": ["view"],
  "menu": ["view"],
  "menu_plan": ["view"],
  "sample": ["view"],
  "report": ["view"]
}</pre>
                        <p>这表示用户只能查看各个模块的数据，但不能创建、编辑或删除。</p>
                    </div>
                </div>
            </div>
            <div class="card">
                <div class="card-header" id="headingFive">
                    </div>
                <div id="collapseFive" class="collapse" aria-labelledby="headingFive" data-parent="#permissionExamples">
                    <div class="card-body">
                        <pre class="bg-light p-3 rounded">
{
  "area": ["view"],
  "user": ["view", "create", "edit"],
  "supplier": ["view", "create"],
  "ingredient": ["view"],
  "menu": ["view", "approve"],
  "menu_plan": ["view", "approve"],
  "sample": ["view"],
  "report": ["view", "export", "print"],
  "log": ["view"]
}</pre>
                        <p>这表示乡镇教育管理员可以：</p>
                        <ul>
                            <li>查看区域信息</li>
                            <li>管理用户（查看、创建、编辑）</li>
                            <li>管理供应商（查看、创建）</li>
                            <li>查看食材信息</li>
                            <li>查看和审核食谱</li>
                            <li>查看和审核菜单计划</li>
                            <li>查看留样记录</li>
                            <li>查看、导出和打印报表</li>
                            <li>查看日志</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">权限配置技巧</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">最小权限原则</h6>
                    </div>
                    <div class="card-body">
                        <p>只给角色分配完成其工作所需的最小权限集，这样可以减少安全风险。</p>
                        <pre class="bg-light p-2 rounded">
{
  "ingredient": ["view", "create"],
  "menu": ["view"],
  "menu_plan": ["view", "create"]
}</pre>
                        <p class="text-muted mt-2">
                            <i class="fas fa-info-circle"></i> 例如：食堂管理员只需要查看供应商信息，而不需要编辑或删除供应商的权限。
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">模块级别授权</h6>
                    </div>
                    <div class="card-body">
                        <p>如果角色需要某个模块的所有权限，可以使用 <code>*</code> 操作，简化配置。</p>
                        <pre class="bg-light p-2 rounded">
{
  "ingredient": ["*"],
  "menu": ["view", "create"]
}</pre>
                        <p class="text-muted mt-2">
                            <i class="fas fa-info-circle"></i> 例如：超级管理员可能需要食材管理模块的所有权限，但只需要查看和创建食谱的权限。
                        </p>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">使用角色模板</h6>
                    </div>
                    <div class="card-body">
                        <p>系统提供了预定义的角色模板，可以快速创建常用角色。</p>
                        <p class="text-muted mt-2">
                            <i class="fas fa-info-circle"></i> 在添加角色时，选择"使用角色模板"选项，可以查看和使用这些模板。
                        </p>
                        <a href="{{ url_for('system.role_templates') }}" class="btn btn-sm btn-info mt-2">
                            <i class="fas fa-copy"></i> 查看角色模板
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">分层权限</h6>
                    </div>
                    <div class="card-body">
                        <p>可以根据操作的敏感度分层授权</p>
                        <ul>
                            <li>基本权限：<code>["view"]</code></li>
                            <li>中级权限：<code>["view", "create", "edit"]</code></li>
                            <li>高级权限：<code>["view", "create", "edit", "delete"]</code></li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card mb-3">
                    <div class="card-header bg-warning text-dark">
                        <h6 class="mb-0">特殊操作权限</h6>
                    </div>
                    <div class="card-body">
                        <p>某些操作具有特殊意义，应谨慎授权</p>
                        <pre class="bg-light p-2 rounded">
{
  "user": ["view", "create", "edit", "reset_password"],
  "menu": ["view", "create", "edit", "approve"]
}</pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="card mb-4">
    <div class="card-header">
        <h5 class="mb-0">最佳实践</h5>
    </div>
    <div class="card-body">
        <ol>
            <li><strong>使用预设模板</strong>：从系统预设的角色模板开始，如"食堂管理员"</li>
            <li><strong>逐步调整</strong>：根据实际需求，逐步添加或移除权限</li>
            <li><strong>文档记录</strong>：记录每个角色的权限配置及其理由</li>
            <li><strong>定期审查</strong>：定期检查和更新权限配置，确保符合当前需求</li>
            <li><strong>权限分离</strong>：确保敏感操作需要多个角色协作完成，避免权限过度集中</li>
        </ol>
    </div>
</div>
{% endblock %}
