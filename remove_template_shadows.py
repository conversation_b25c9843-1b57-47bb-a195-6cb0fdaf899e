#!/usr/bin/env python3
"""
移除HTML模板文件中内联样式的阴影效果脚本
处理<style>标签内的CSS和内联style属性中的阴影效果
"""

import os
import re
from pathlib import Path

def remove_shadows_from_template(file_path):
    """移除模板文件中的阴影效果"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = 0
        
        # 1. 处理<style>标签内的CSS阴影效果
        def replace_css_shadows(match):
            nonlocal changes_made
            style_content = match.group(1)
            
            # 移除 box-shadow 属性
            box_shadow_pattern = r'(\s*)(box-shadow:\s*[^;]+;)'
            box_shadow_matches = re.findall(box_shadow_pattern, style_content)
            for indent, shadow_rule in box_shadow_matches:
                commented_rule = f"{indent}/* {shadow_rule} */ /* 移除阴影效果 */"
                style_content = style_content.replace(f"{indent}{shadow_rule}", commented_rule)
                changes_made += 1
            
            # 移除 text-shadow 属性
            text_shadow_pattern = r'(\s*)(text-shadow:\s*[^;]+;)'
            text_shadow_matches = re.findall(text_shadow_pattern, style_content)
            for indent, shadow_rule in text_shadow_matches:
                commented_rule = f"{indent}/* {shadow_rule} */ /* 移除文字阴影效果 */"
                style_content = style_content.replace(f"{indent}{shadow_rule}", commented_rule)
                changes_made += 1
            
            # 移除 backdrop-filter 属性
            backdrop_filter_pattern = r'(\s*)(backdrop-filter:\s*[^;]+;)'
            backdrop_filter_matches = re.findall(backdrop_filter_pattern, style_content)
            for indent, filter_rule in backdrop_filter_matches:
                commented_rule = f"{indent}/* {filter_rule} */ /* 移除背景模糊效果 */"
                style_content = style_content.replace(f"{indent}{filter_rule}", commented_rule)
                changes_made += 1
            
            # 移除 filter: blur() 属性
            filter_blur_pattern = r'(\s*)(filter:\s*[^;]*blur[^;]*;)'
            filter_blur_matches = re.findall(filter_blur_pattern, style_content)
            for indent, filter_rule in filter_blur_matches:
                commented_rule = f"{indent}/* {filter_rule} */ /* 移除滤镜模糊效果 */"
                style_content = style_content.replace(f"{indent}{filter_rule}", commented_rule)
                changes_made += 1
            
            return f"<style{match.group(0)[6:-8]}{style_content}</style>"
        
        # 查找并处理所有<style>标签
        style_pattern = r'<style[^>]*>(.*?)</style>'
        content = re.sub(style_pattern, replace_css_shadows, content, flags=re.DOTALL)
        
        # 2. 处理内联style属性中的阴影效果
        def replace_inline_shadows(match):
            nonlocal changes_made
            style_attr = match.group(1)
            
            # 移除各种阴影效果
            shadow_patterns = [
                (r'box-shadow:\s*[^;]+;?', '/* box-shadow removed */'),
                (r'text-shadow:\s*[^;]+;?', '/* text-shadow removed */'),
                (r'backdrop-filter:\s*[^;]+;?', '/* backdrop-filter removed */'),
                (r'filter:\s*[^;]*blur[^;]*;?', '/* filter blur removed */'),
                (r'filter:\s*[^;]*drop-shadow[^;]*;?', '/* drop-shadow removed */')
            ]
            
            for pattern, replacement in shadow_patterns:
                if re.search(pattern, style_attr):
                    style_attr = re.sub(pattern, replacement, style_attr)
                    changes_made += 1
            
            return f'style="{style_attr}"'
        
        # 查找并处理所有内联style属性
        inline_style_pattern = r'style="([^"]*)"'
        content = re.sub(inline_style_pattern, replace_inline_shadows, content)
        
        # 3. 处理CSS变量中的阴影定义
        def replace_css_variables(match):
            nonlocal changes_made
            var_content = match.group(1)
            
            # 查找包含shadow的CSS变量
            shadow_var_pattern = r'(\s*)(--[^:]*shadow[^:]*:\s*[^;]+;)'
            shadow_var_matches = re.findall(shadow_var_pattern, var_content)
            for indent, var_rule in shadow_var_matches:
                commented_rule = f"{indent}/* {var_rule} */ /* 移除阴影变量 */"
                var_content = var_content.replace(f"{indent}{var_rule}", commented_rule)
                changes_made += 1
            
            return f":root {{{var_content}}}"
        
        # 处理:root中的CSS变量
        root_pattern = r':root\s*\{([^}]+)\}'
        content = re.sub(root_pattern, replace_css_variables, content, flags=re.DOTALL)
        
        # 如果有修改，写回文件
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            return changes_made
        
        return 0
        
    except Exception as e:
        print(f"❌ 处理文件 {file_path} 时出错: {str(e)}")
        return 0

def main():
    """主函数"""
    print("🔄 移除HTML模板文件中的阴影效果")
    print("=" * 50)
    
    # 模板文件目录
    templates_dir = Path("app/templates")
    
    if not templates_dir.exists():
        print("❌ 模板目录不存在")
        return
    
    total_files = 0
    total_changes = 0
    
    # 处理所有HTML文件
    for html_file in templates_dir.rglob("*.html"):
        changes = remove_shadows_from_template(html_file)
        if changes > 0:
            total_files += 1
            total_changes += changes
            rel_path = html_file.relative_to(Path("."))
            print(f"✅ 处理文件: {rel_path} - 移除了 {changes} 个阴影效果")
    
    print("\n" + "=" * 50)
    print(f"📊 处理完成:")
    print(f"   修改文件: {total_files} 个")
    print(f"   移除阴影: {total_changes} 个")
    
    if total_changes > 0:
        print("\n✨ 所有模板文件中的阴影效果已成功移除！")
    else:
        print("\n✅ 没有发现需要移除的阴影效果")

if __name__ == "__main__":
    main()
