{% extends 'base.html' %}

{% block title %}从周菜单创建采购订单 - {{ super() }}{% endblock %}

{% block head %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/sweetalert2/css/bootstrap-4.css') }}">
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2.min.js') }}"></script>
{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 用户引导样式 */
.context-guidance {
    border-start: 4px solid #17a2b8;
    background-color: #f8f9fa;
}
.workflow-context {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}
.previous-step, .current-step, .next-step {
    flex: 1;
    padding: 0 10px;
}
.current-step {
    border-start: 1px solid #dee2e6;
    border-end: 1px solid #dee2e6;
}
.guidance-tips li {
    margin-bottom: 5px;
}
.step-guide-card {
    margin-bottom: 20px;
}
.alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}
.process-step {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
}
.process-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20px;
    width: 20px;
    height: 2px;
    background-color: #dee2e6;
}
.process-step:first-child::before {
    display: none;
}
.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #17a2b8;
    color: white;
    border-radius: 50%;
    margin-right: 10px;
}
.highlight-box {
    border-start: 4px solid #28a745;
    padding-left: 15px;
    margin-bottom: 15px;
}

/* 原有样式 */
.meal-items .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}
.recipe-info {
    cursor: pointer;
    margin-left: 3px;
}
.ingredients-preview .card {
    padding: 15px;
    margin-bottom: 15px;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.05);
}
.selected-row {
    background-color: rgba(0,123,255,.1) !important;
}
.meal-count {
    font-size: 0.85em;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.quantity-input {
    font-size: 1.2em !important;
    font-weight: bold !important;
    color: #dc3545 !important;
    text-align: center !important;
    background-color: #fff3f3 !important;
    border: 2px solid #dc3545 !important;
}
.quantity-input:focus {
    background-color: #fff !important;
    border-color: #dc3545 !important;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,.25) !important;
}
.supplier-select {
    font-size: 1.1em !important;
}
.ingredient-name {
    font-weight: bold;
    color: #495057;
}
.unit-label {
    font-weight: 500;
    color: #6c757d;
}
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}
.card-header h6 {
    color: #495057;
    font-weight: bold;
    font-size: 1.1em;
}
.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 餐次容器样式 */
.meal-container {
    width: 100%;
}

/* 无菜单容器 */
.no-menu-container {
    padding: 20px 8px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

.no-menu-container .text-muted {
    font-size: 15px;
    color: #6c757d !important;
}

/* 餐次标题区域 */
.meal-header {
    margin-bottom: 8px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    font-size: 15px;
}

/* 菜谱网格布局 - 两列显示 */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 10px;
    margin-top: 10px;
}

.recipe-item {
    padding: 4px 0;
    font-size: 15px;
    color: #495057;
    line-height: 1.4;
    word-break: break-word;
}

.recipe-item i {
    margin-right: 4px;
    font-size: 12px;
    color: #6c757d;
}

/* 表格样式 */
.table td {
    vertical-align: top;
    padding: 10px 6px;
    min-width: 180px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
    padding: 8px 6px;
}

/* 让表格更紧凑 */
.table-responsive {
    font-size: 15px;
}

/* 优化复选框样式 */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 选中餐次的背景色样式 */
.meal-container.selected {
    background-color: #e8f5e8 !important;
    border: 2px solid #28a745 !important;
    border-radius: 6px !important;
    transition: all 0.3s ease;
}

.meal-container.selected .form-check {
    background-color: #d4edda !important;
    border-color: #28a745 !important;
}

.meal-container.selected .form-check-label {
    color: #155724 !important;
    font-weight: 600 !important;
}

.meal-container.selected .recipe-item {
    color: #155724 !important;
}

.meal-container.selected .recipe-item i {
    color: #28a745 !important;
}

/* 分析按钮样式 - 保持系统风格 */
.analyze-btn {
    font-weight: 600;
}

/* 让日期列更突出 */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-width: 80px;
}

/* 星期列样式 */
.table td:nth-child(2) {
    text-align: center;
    min-width: 60px;
}

/* 响应式调整 */
@d-flex (max-width: 768px) {
    .recipes-grid {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .table td {
        min-width: 120px;
        padding: 8px 4px;
    }

    .recipe-item {
        font-size: 15px;
    }

    .form-check-label {
        font-size: 15px;
    }
}

/* 用户引导样式 */
.context-guidance {
    border-start: 4px solid #17a2b8;
    background-color: #f8f9fa;
}
.workflow-context {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}
.previous-step, .current-step, .next-step {
    flex: 1;
    padding: 0 10px;
}
.current-step {
    border-start: 1px solid #dee2e6;
    border-end: 1px solid #dee2e6;
}
.guidance-tips li {
    margin-bottom: 5px;
}
.step-guide-card {
    margin-bottom: 20px;
}
.alert-icon {
    font-size: 1.2rem;
    margin-right: 10px;
}
.process-step {
    background-color: #f8f9fa;
    border-radius: 5px;
    padding: 15px;
    margin-bottom: 20px;
    position: relative;
}
.process-step::before {
    content: '';
    position: absolute;
    top: 50%;
    left: -20px;
    width: 20px;
    height: 2px;
    background-color: #dee2e6;
}
.process-step:first-child::before {
    display: none;
}
.step-number {
    display: inline-block;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: #17a2b8;
    color: white;
    border-radius: 50%;
    margin-right: 10px;
}
.highlight-box {
    border-start: 4px solid #28a745;
    padding-left: 15px;
    margin-bottom: 15px;
}

/* 原有样式 */
.meal-items .badge {
    margin-right: 5px;
    margin-bottom: 5px;
}
.recipe-info {
    cursor: pointer;
    margin-left: 3px;
}
.ingredients-preview .card {
    padding: 15px;
    margin-bottom: 15px;
}
.table-hover tbody tr:hover {
    background-color: rgba(0,0,0,.05);
}
.selected-row {
    background-color: rgba(0,123,255,.1) !important;
}
.meal-count {
    font-size: 0.85em;
}
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255,255,255,0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}
.quantity-input {
    font-size: 1.2em !important;
    font-weight: bold !important;
    color: #dc3545 !important;
    text-align: center !important;
    background-color: #fff3f3 !important;
    border: 2px solid #dc3545 !important;
}
.quantity-input:focus {
    background-color: #fff !important;
    border-color: #dc3545 !important;
    /* box-shadow: 0 0 0 0.2rem rgba(220,53,69,.25) !important; */ /* 移除阴影效果 */
}
.supplier-select {
    font-size: 1.1em !important;
}
.ingredient-name {
    font-weight: bold;
    color: #495057;
}
.unit-label {
    font-weight: 500;
    color: #6c757d;
}
.card-header {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
}
.card-header h6 {
    color: #495057;
    font-weight: bold;
    font-size: 1.1em;
}
.table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
}

/* 餐次容器样式 */
.meal-container {
    width: 100%;
}

/* 无菜单容器 */
.no-menu-container {
    padding: 20px 8px;
    text-align: center;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #dee2e6;
}

.no-menu-container .text-muted {
    font-size: 15px;
    color: #6c757d !important;
}

/* 餐次标题区域 */
.meal-header {
    margin-bottom: 8px;
}

.form-check {
    background-color: #f8f9fa;
    padding: 6px 8px;
    border-radius: 4px;
    border: 1px solid #dee2e6;
    margin-bottom: 0;
}

.form-check-label {
    font-weight: 500;
    color: #495057;
    font-size: 15px;
}

/* 菜谱网格布局 - 两列显示 */
.recipes-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 6px 10px;
    margin-top: 10px;
}

.recipe-item {
    padding: 4px 0;
    font-size: 15px;
    color: #495057;
    line-height: 1.4;
    word-break: break-word;
}

.recipe-item i {
    margin-right: 4px;
    font-size: 12px;
    color: #6c757d;
}

/* 表格样式 */
.table td {
    vertical-align: top;
    padding: 10px 6px;
    min-width: 180px;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
    text-align: center;
    padding: 8px 6px;
}

/* 让表格更紧凑 */
.table-responsive {
    font-size: 15px;
}

/* 优化复选框样式 */
.form-check-input:checked {
    background-color: #28a745;
    border-color: #28a745;
}

/* 选中餐次的背景色样式 */
.meal-container.selected {
    background-color: #e8f5e8 !important;
    border: 2px solid #28a745 !important;
    border-radius: 6px !important;
    transition: all 0.3s ease;
}

.meal-container.selected .form-check {
    background-color: #d4edda !important;
    border-color: #28a745 !important;
}

.meal-container.selected .form-check-label {
    color: #155724 !important;
    font-weight: 600 !important;
}

.meal-container.selected .recipe-item {
    color: #155724 !important;
}

.meal-container.selected .recipe-item i {
    color: #28a745 !important;
}

/* 分析按钮样式 - 保持系统风格 */
.analyze-btn {
    font-weight: 600;
}

/* 让日期列更突出 */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    text-align: center;
    min-width: 80px;
}

/* 星期列样式 */
.table td:nth-child(2) {
    text-align: center;
    min-width: 60px;
}

/* 响应式调整 */
@d-flex (max-width: 768px) {
    .recipes-grid {
        grid-template-columns: 1fr;
        gap: 4px;
    }

    .table td {
        min-width: 120px;
        padding: 8px 4px;
    }

    .recipe-item {
        font-size: 15px;
    }

    .form-check-label {
        font-size: 15px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 采购计划流程引导 -->
    <div class="context-guidance card mb-4 border-primary">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0"><i class="fas fa-shopping-cart"></i> 采购计划创建 - 流程指引</h5>
        </div>
        <div class="card-body">
            <div class="alert alert-info">
                <i class="fas fa-info-circle alert-icon"></i> <strong>提示：</strong> 采购计划是食堂管理的重要环节，合理的采购计划可以确保食材供应充足，同时避免浪费。
            </div>

            <div class="workflow-context mt-3">
                <div class="previous-step">
                    <small class="text-muted">上一步</small>
                    <p><i class="fas fa-calendar-alt"></i> 周菜单计划</p>
                    <small>已完成周菜单的制定</small>
                    <div class="mt-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-arrow-left"></i> 返回菜单计划
                        </a>
                    </div>
                </div>
                <div class="current-step bg-light p-2 border rounded">
                    <small class="text-muted">当前步骤</small>
                    <p class="fw-bold"><i class="fas fa-shopping-cart"></i> 创建采购计划</p>
                    <small>根据菜单生成采购清单</small>
                </div>
                <div class="next-step">
                    <small class="text-muted">下一步</small>
                    <p><i class="fas fa-list-alt"></i> 采购计划列表</p>
                    <small>查看和管理采购订单</small>
                    <div class="mt-2">
                        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-arrow-right"></i> 前往采购计划列表
                        </a>
                    </div>
                </div>
            </div>

            <!-- 操作流程指南 -->
            <div class="card mt-4">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-tasks"></i> 采购计划创建流程</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">1</span>
                                <strong>选择日期</strong>
                                <p class="small text-muted mt-2 mb-0">选择需要采购食材的日期，可以选择多天一起采购</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">2</span>
                                <strong>生成食材清单</strong>
                                <p class="small text-muted mt-2 mb-0">系统会根据菜单自动计算所需食材的种类和数量</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="process-step">
                                <span class="step-number">3</span>
                                <strong>确认采购信息</strong>
                                <p class="small text-muted mt-2 mb-0">调整采购数量，选择供应商，确认后生成采购订单</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作提示 -->
            <div class="alert alert-light border mt-3">
                <h6 class="alert-heading"><i class="fas fa-lightbulb text-warning"></i> 操作提示</h6>
                <ul class="mb-0">
                    <li>勾选需要采购的日期（可以选择多天一起采购，减少采购频次）</li>
                    <li>点击"生成食材清单"按钮，系统会自动计算所需食材</li>
                    <li>在弹出的窗口中，可以调整采购数量和选择供应商</li>
                    <li>确认无误后，点击"确认采购"按钮生成采购订单</li>
                    <li>生成的采购订单可以打印或发送给供应商</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h2>{{ area.name }}采购订单创建</h2>
            <p class="text-muted">{{ week_start }} 至 {{ week_end }}</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="{{ url_for('purchase_order.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
    </div>

    <!-- 周次选择 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-calendar-week"></i> 选择周次
            </h5>
        </div>
        <div class="card-body">
            <div class="btn-group btn-group-lg" role="group">
                {% for option in week_options %}
                <a href="{{ url_for('purchase_order.create_from_menu', area_id=area.id, week_offset=option.offset) }}"
                   class="btn {% if option.selected %}btn-primary{% else %}btn-outline-primary{% endif %}{% if not option.has_menu % %} disabled{% endif %}">
                    <i class="fas fa-calendar-week"></i> {{ option.name }}
                    <br>
                    <small>{{ option.start_str }} ~ {{ option.end_str }}</small>
                    {% if not option.has_menu %}
                    <br><small class="text-muted">(无菜单)</small>
                    {% endif %}
                </a>
                {% endfor %}
            </div>
            {% if not weekly_menu %}
            <div class="alert alert-warning mt-3">
                <i class="fas fa-exclamation-triangle"></i>
                当前选择的周次还没有菜单，请先创建周菜单。
                <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-sm btn-warning ms-2">
                    <i class="fas fa-plus"></i> 创建周菜单
                </a>
            </div>
            {% endif %}
        </div>
    </div>

    <!-- 周菜单选择卡片 -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-utensils"></i> 选择要创建采购计划的日期和餐次
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered">
                    <thead class="thead-light">
                        <tr>
                            <th style="width: 100px;">日期</th>
                            <th style="width: 80px;">星期</th>
                            <th>早餐</th>
                            <th>午餐</th>
                            <th>晚餐</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for date_str, day_data in week_data.items() %}
                        <tr>
                            <td>
                                <strong>{{ date_str[5:] }}</strong>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ day_data.weekday }}</span>
                            </td>

                            <!-- 早餐 -->
                            <td>
                                {% if day_data.meals['早餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_早餐"
                                                   id="meal_{{ date_str }}_breakfast">
                                            <label class="form-check-label" for="meal_{{ date_str }}_breakfast">
                                                <strong>创建早餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['早餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>

                            <!-- 午餐 -->
                            <td>
                                {% if day_data.meals['午餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_午餐"
                                                   id="meal_{{ date_str }}_lunch">
                                            <label class="form-check-label" for="meal_{{ date_str }}_lunch">
                                                <strong>创建午餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['午餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>

                            <!-- 晚餐 -->
                            <td>
                                {% if day_data.meals['晚餐'] %}
                                <div class="meal-container">
                                    <div class="meal-header">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   name="selected_meals"
                                                   value="{{ date_str }}_晚餐"
                                                   id="meal_{{ date_str }}_dinner">
                                            <label class="form-check-label" for="meal_{{ date_str }}_dinner">
                                                <strong>创建晚餐采购</strong>
                                            </label>
                                        </div>
                                    </div>
                                    <div class="recipes-grid">
                                        {% for recipe in day_data.meals['晚餐'] %}
                                        <div class="recipe-item">
                                            <i class="fas fa-utensils text-muted"></i> {{ recipe.name }}
                                        </div>
                                        {% endfor %}
                                    </div>
                                </div>
                                {% else %}
                                <div class="no-menu-container">
                                    <span class="text-muted">
                                        <i class="fas fa-minus-circle"></i> 无菜单安排
                                    </span>
                                </div>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            <!-- 批量选择按钮 -->
            <div class="mt-3">
                <button type="button" class="btn btn-outline-primary btn-sm" id="selectAllBtn">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary btn-sm ms-2" id="selectNoneBtn">
                    <i class="fas fa-square"></i> 全不选
                </button>
                <button type="button" class="btn btn-outline-info btn-sm ms-2" id="selectLunchOnlyBtn">
                    <i class="fas fa-sun"></i> 只选午餐
                </button>
            </div>

            <!-- 分析按钮 -->
            <div class="mt-3 text-center">
                <button type="button" class="btn btn-primary btn-lg" id="analyzeSelectedBtn">
                    <i class="fas fa-chart-line"></i> 分析选中餐次
                </button>
            </div>

            <!-- 分析结果显示区域 -->
            <div id="analysis-result" class="mt-3" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-chart-line"></i> 选中餐次分析结果
                        </h5>
                    </div>
                    <div class="card-body" id="analysis-content">
                        <!-- 动态内容将在这里显示 -->
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 加载中遮罩 -->
    <div class="loading-overlay">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">加载中...</span>
    </div>
  </div>
</div>

<!-- 食材预览模态框 -->
<div class="modal fade" id="ingredientsPreviewModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title"><i class="fas fa-list-alt"></i> 食材采购清单</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <!-- 操作指引 -->
            <div class="alert alert-light border-info m-3">
                <div class="d-flex">
                    <div class="me-3">
                        <i class="fas fa-info-circle text-info fa-2x"></i>
                    </div>
                    <div>
                        <h6 class="alert-heading">食材采购清单操作指引</h6>
                        <p>在此页面您可以：</p>
                        <ol>
                            <li><strong>调整采购数量</strong> - 根据实际需求和库存情况调整每种食材的采购数量</li>
                            <li><strong>选择计量单位</strong> - 选择适合的计量单位（公斤、箱、包等）</li>
                            <li><strong>选择供应商</strong> - 为每种食材选择合适的供应商或设置为自购</li>
                        </ol>
                        <div class="alert alert-warning py-2">
                            <small><i class="fas fa-exclamation-triangle"></i> <strong>注意：</strong> 所有食材必须设置大于0的采购数量，并选择供应商才能提交。</small>
                        </div>
                    </div>
                </div>
            </div>

            <div class="modal-body">
                <!-- 这里将通过JavaScript动态填充内容 -->
            </div>

            <!-- 采购提示 -->
            <div class="alert alert-light border-success mx-3 mb-3">
                <h6 class="text-success"><i class="fas fa-lightbulb"></i> 采购建议</h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>蔬菜类食材建议少量多次采购，保证新鲜度</li>
                            <li>肉类和冷冻食品可以适当增加采购量，减少采购频次</li>
                            <li>干货类食材可以批量采购，降低采购成本</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="small mb-0">
                            <li>采购前先检查库存，避免重复采购</li>
                            <li>考虑食材的保质期，避免过期浪费</li>
                            <li>根据季节性调整采购策略，选择当季食材</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-arrow-left"></i> 返回修改
                </button>
                <button type="button" class="btn btn-primary btn-lg" id="confirmPurchaseBtn">
                    <i class="fas fa-shopping-cart"></i> 确认采购
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
// 全局变量
let weeklyMenuId = null;
let areaId = null;

// 页面加载完成后执行
$(document).ready(function() {
    // 从URL或页面数据中获取周菜单ID和区域ID
    const urlParams = new URLSearchParams(window.location.search);
    weeklyMenuId = urlParams.get('weekly_menu_id') || {{ weekly_menu.id if weekly_menu else 'null' }};
    areaId = {{ area.id if area else 'null' }};

    console.log('周菜单ID:', weeklyMenuId);
    console.log('区域ID:', areaId);

    // 绑定餐次复选框变化事件
    bindMealCheckboxEvents();

    // 绑定批量选择按钮事件
    bindBatchSelectEvents();
});

// 绑定餐次复选框变化事件
function bindMealCheckboxEvents() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const mealContainer = this.closest('.meal-container');
            if (mealContainer) {
                if (this.checked) {
                    mealContainer.classList.add('selected');
                } else {
                    mealContainer.classList.remove('selected');
                }
            }
        });
    });
}

// 绑定批量选择按钮事件
function bindBatchSelectEvents() {
    const selectAllBtn = document.getElementById('selectAllBtn');
    const selectNoneBtn = document.getElementById('selectNoneBtn');
    const selectLunchOnlyBtn = document.getElementById('selectLunchOnlyBtn');
    const analyzeSelectedBtn = document.getElementById('analyzeSelectedBtn');

    if (selectAllBtn) {
        selectAllBtn.addEventListener('click', selectAll);
    }
    if (selectNoneBtn) {
        selectNoneBtn.addEventListener('click', selectNone);
    }
    if (selectLunchOnlyBtn) {
        selectLunchOnlyBtn.addEventListener('click', selectLunchOnly);
    }
    if (analyzeSelectedBtn) {
        analyzeSelectedBtn.addEventListener('click', analyzeSelected);
    }
}

// 选择功能
function selectAll() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = true;
        // 触发change事件以更新样式
        checkbox.dispatchEvent(new Event('change'));
    });
}

function selectNone() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = false;
        // 触发change事件以更新样式
        checkbox.dispatchEvent(new Event('change'));
    });
}

function selectLunchOnly() {
    document.querySelectorAll('input[name="selected_meals"]').forEach(function(checkbox) {
        checkbox.checked = checkbox.value.includes('_午餐');
        // 触发change事件以更新样式
        checkbox.dispatchEvent(new Event('change'));
    });
}

// 分析选中餐次
function analyzeSelected() {
    // 获取选中的餐次
    const selectedMeals = [];
    document.querySelectorAll('input[name="selected_meals"]:checked').forEach(function(checkbox) {
        selectedMeals.push(checkbox.value);
    });

    if (selectedMeals.length === 0) {
        alert('请先选择要分析的餐次');
        return;
    }

    // 显示加载状态
    const analysisResult = document.getElementById('analysis-result');
    const analysisContent = document.getElementById('analysis-content');

    analysisResult.style.display = 'block';
    analysisContent.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin"></i> 正在分析选中餐次...</div>';

    // 发送AJAX请求
    fetch('/purchase-order/analyze-selected-meals', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            weekly_menu_id: parseInt(weeklyMenuId),  // 确保是整数
            area_id: parseInt(areaId),               // 确保是整数
            selected_meals: selectedMeals
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayAnalysisResult(data.data);
            // 分析完成后，平滑滚动到生成采购订单按钮
            setTimeout(() => {
                scrollToGenerateButton();
            }, 500);
        } else {
            analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> ' + data.message + '</div>';
        }
    })
    .catch(error => {
        console.error('分析失败:', error);
        analysisContent.innerHTML = '<div class="alert alert-danger"><i class="fas fa-exclamation-triangle"></i> 分析失败，请重试</div>';
    });
}

// 显示分析结果
function displayAnalysisResult(data) {
    const analysisContent = document.getElementById('analysis-content');
    let html = '';

    // 整体食材分析
    html += '<div class="mt-3">';
    html += '<h6>整体食材分析（仅主材，不含调味料）</h6>';

    // 统计所有餐次的食材分类
    const allCategories = {};
    const allIngredients = new Set();

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];
        if (meal.has_recipes && meal.ingredient_by_category) {
            Object.keys(meal.ingredient_by_category).forEach(function(category) {
                if (!allCategories[category]) {
                    allCategories[category] = new Set();
                }
                meal.ingredient_by_category[category].forEach(function(ing) {
                    allCategories[category].add(ing.name);
                    allIngredients.add(ing.name);
                });
            });
        }
    });

    if (Object.keys(allCategories).length > 0) {
        html += '<div class="row mb-3">';
        html += '<div class="col-12">';
        html += '<div class="alert alert-info">';
        html += '<h6 class="mb-2"><i class="fas fa-chart-pie"></i> 食材分类统计</h6>';
        html += '<div class="row">';

        Object.keys(allCategories).forEach(function(category) {
            const count = allCategories[category].size;
            html += '<div class="col-md-3 mb-2">';
            html += '<span class="badge bg-primary me-1">' + category + '</span>';
            html += '<small class="text-muted">' + count + '种</small>';
            html += '</div>';
        });

        html += '</div>';
        html += '<div class="mt-2">';
        html += '<small class="text-muted"><strong>总计:</strong> ' + allIngredients.size + '种不同的主材食材</small>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
        html += '</div>';
    }
    html += '</div>';

    // 餐次详情
    html += '<div class="mt-3">';
    html += '<h6>餐次详情</h6>';
    html += '<div class="row">';

    Object.keys(data.meal_analysis).forEach(function(mealKey) {
        const meal = data.meal_analysis[mealKey];

        html += '<div class="col-md-6 mb-3">';
        html += '<div class="card">';
        html += '<div class="card-header">';
        html += '<h5 class="mb-0">' + mealKey.replace('_', ' ') + '</h5>';
        html += '</div>';
        html += '<div class="card-body">';

        if (meal.has_recipes) {
            // 基本统计信息
            html += '<div class="mb-2">';
            html += '<small class="text-muted"><strong>食谱:</strong> ' + meal.recipes_count + '个</small><br>';
            html += '<small class="text-muted"><strong>主材:</strong> ' + meal.main_ingredients_count + '种（不含调味料）</small><br>';
            html += '<small class="text-muted"><strong>分类:</strong> ' + meal.category_stats.total_categories + '类</small>';
            html += '</div>';

            // 食材分类详情
            if (meal.ingredient_by_category && Object.keys(meal.ingredient_by_category).length > 0) {
                html += '<div class="ingredient-categories mb-2">';
                html += '<small class="text-muted"><strong>食材分类详情:</strong></small>';
                html += '<div class="mt-1">';

                Object.keys(meal.ingredient_by_category).forEach(function(category) {
                    const ingredients = meal.ingredient_by_category[category];
                    const uniqueIngredients = [];
                    const seenIngredients = new Set();

                    // 去重，只显示唯一的食材名称
                    ingredients.forEach(function(ing) {
                        if (!seenIngredients.has(ing.name)) {
                            seenIngredients.add(ing.name);
                            uniqueIngredients.push(ing);
                        }
                    });

                    html += '<div class="category-group">';
                    html += '<span class="badge bg-light bg-sm me-1">' + category + ' (' + uniqueIngredients.length + ')</span>';
                    html += '<small class="text-muted">';
                    uniqueIngredients.slice(0, 3).forEach(function(ing, index) {
                        html += ing.name;
                        if (index < Math.min(2, uniqueIngredients.length - 1)) html += ', ';
                    });
                    if (uniqueIngredients.length > 3) {
                        html += '等' + uniqueIngredients.length + '种';
                    }
                    html += '</small><br>';
                    html += '</div>';
                });
                html += '</div>';
                html += '</div>';
            }

            // 智能建议
            if (meal.smart_suggestions) {
                const suggestion = meal.smart_suggestions;
                let badgeClass = 'bg-secondary';
                let iconClass = 'fas fa-info-circle';

                if (suggestion.action === 'skip') {
                    badgeClass = 'bg-danger';
                    iconClass = 'fas fa-times-circle';
                } else if (suggestion.action === 'review') {
                    badgeClass = 'bg-warning';
                    iconClass = 'fas fa-exclamation-triangle';
                } else if (suggestion.action === 'create_new') {
                    badgeClass = 'bg-success';
                    iconClass = 'fas fa-plus-circle';
                }

                html += '<div class="mt-2">';
                html += '<span class="badge ' + badgeClass bg-sm"><i class="' + iconClass"></i> ' + suggestion.action.replace('_', ' ').toUpperCase() + '</span>';
                html += '<div class="mt-1"><small class="text-muted">' + suggestion.message + '</small></div>';
                html += '</div>';
            }

            // 相似度分析
            if (meal.similarity_analysis.has_similar) {
                html += '<div class="mt-2">';
                html += '<span class="badge bg-warning bg-sm">相似度 ' + meal.similarity_analysis.max_similarity_percent + '%</span>';
                html += '</div>';
            }

            // 已存在的采购订单
            if (meal.existing_orders && meal.existing_orders.length > 0) {
                html += '<div class="mt-2">';
                html += '<span class="badge bg-info bg-sm">已有 ' + meal.existing_orders.length + ' 个订单</span>';
                html += '</div>';
            }
        } else {
            html += '<small class="text-muted">' + meal.message + '</small>';
        }

        html += '</div>';
        html += '</div>';
        html += '</div>';
    });

    html += '</div>';
    html += '</div>';

    // 操作按钮
    html += '<div class="mt-3 text-center">';
    html += '<button type="button" class="btn btn-primary btn-lg" id="generatePurchaseOrderBtn">';
    html += '<i class="fas fa-shopping-cart"></i> 生成采购订单';
    html += '</button>';
    html += '</div>';

    analysisContent.innerHTML = html;

    // 绑定生成采购订单按钮的点击事件
    const generateBtn = document.getElementById('generatePurchaseOrderBtn');
    if (generateBtn) {
        generateBtn.addEventListener('click', generatePurchaseOrder);
    }
}

// 平滑滚动到生成采购订单按钮
function scrollToGenerateButton() {
    // 查找生成采购订单按钮
    const generateButton = document.getElementById('generatePurchaseOrderBtn');
    if (generateButton) {
        // 计算按钮位置，并向上偏移一些像素以确保按钮完全可见
        const buttonRect = generateButton.getBoundingClientRect();
        const offsetTop = window.pageYOffset + buttonRect.top - 100; // 向上偏移100px

        // 平滑滚动到按钮位置
        window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
        });
    }
}

// 生成采购订单
function generatePurchaseOrder() {
    // 获取选中的餐次
    const selectedMeals = [];
    document.querySelectorAll('input[name="selected_meals"]:checked').forEach(function(checkbox) {
        selectedMeals.push(checkbox.value);
    });

    if (selectedMeals.length === 0) {
        alert('请先选择要采购的餐次');
        return;
    }

    // 发送请求生成食材清单
    fetch('/purchase-order/get-ingredients', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            area_id: parseInt(areaId),               // 确保是整数
            weekly_menu_id: parseInt(weeklyMenuId),  // 确保是整数
            selected_meals: selectedMeals
        })
    })
    .then(response => response.json())
    .then(data => {
        console.log('生成食材清单响应:', data);
        if (data.success) {
            console.log('食材数据:', data.data);
            // 显示食材清单模态框
            showIngredientsModal(data.data);
        } else {
            alert('生成食材清单失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('生成食材清单失败:', error);
        alert('生成食材清单失败，请重试');
    });
}

// 显示食材清单模态框
function showIngredientsModal(data) {
    console.log('显示食材清单模态框，数据:', data);
    console.log('食材数据:', data.ingredients);

    // 填充模态框内容
    renderIngredientsPreview(data.ingredients);
    $('#ingredientsPreviewModal').modal('show');
}

// 渲染食材预览
function renderIngredientsPreview(groupedIngredients) {
    console.log('渲染食材预览，数据:', groupedIngredients);

    const modalBody = document.querySelector('#ingredientsPreviewModal .modal-body');
    if (!modalBody) {
        console.error('找不到模态框body元素');
        return;
    }

    let html = '';

    // 检查数据是否为空
    if (!groupedIngredients || Object.keys(groupedIngredients).length === 0) {
        html = '<div class="alert alert-warning">没有找到需要采购的食材</div>';
        modalBody.innerHTML = html;
        return;
    }

    // 遍历食材类别
    for (const [category, ingredients] of Object.entries(groupedIngredients)) {
        console.log(`处理分类: ${category}, 食材数量: ${ingredients.length}`);
        html += `
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-utensils me-2"></i>${category} (${ingredients.length}种)</h5>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-bordered table-hover mb-0">
                            <thead>
                                <tr>
                                    <th style="width: 25%; color: #495057; font-weight: bold;">食材名称</th>
                                    <th style="width: 20%; color: #495057; font-weight: bold;">采购数量</th>
                                    <th style="width: 15%; color: #495057; font-weight: bold;">单位</th>
                                    <th style="width: 35%; color: #495057; font-weight: bold;">采购方式</th>
                                    <th style="width: 5%; color: #495057; font-weight: bold;">操作</th>
                                </tr>
                            </thead>
                            <tbody>`;

        // 定义常用单位
        const commonUnits = ['公斤', 'KG', '盒', '箱', '包', '袋', '个', '件', '斤', '克'];

        // 遍历该类别下的食材
        for (const ingredient of ingredients) {
            const suppliers = ingredient.suppliers || [];
            const hasSuppliers = suppliers.length > 0;

            html += `
                <tr>
                    <td>
                        <span class="ingredient-name">${ingredient.name}</span>
                    </td>
                    <td>
                        <input type="number" class="form-control quantity-input"
                               data-ingredient-id="${ingredient.id}"
                               value="${ingredient.total_quantity || 1}"
                               min="0.1" step="0.1">
                    </td>
                    <td>
                        <select class="form-control unit-select">
                            ${commonUnits.map((unit, index) => `
                                <option value="${unit}" ${unit === '公斤' ? 'selected' : ''}>${unit}</option>
                            `).join('')}
                        </select>
                    </td>
                    <td>
                        <select class="form-control supplier-select"
                                data-ingredient-id="${ingredient.id}">
                            ${hasSuppliers ? `
                                ${suppliers.map((s, index) => `
                                    <option value="${s.product_id}"
                                            data-supplier-id="${s.id}"
                                            ${index === 0 ? 'selected' : ''}>
                                        ${s.name}
                                        ${s.specification ? ` (${s.specification})` : ''}
                                    </option>
                                `).join('')}
                                <option value="self">自购</option>
                            ` : `
                                <option value="self" selected>自购</option>
                            `}
                        </select>
                    </td>
                    <td class="text-center">
                        <button type="button" class="btn btn-sm btn-danger delete-ingredient"
                                data-ingredient-id="${ingredient.id}">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>`;
        }

        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>`;
    }

    modalBody.innerHTML = html;

    // 为数量输入框添加提示
    document.querySelectorAll('.quantity-input').forEach(input => {
        input.title = '请输入采购数量';
    });

    console.log('食材清单渲染完成');
}

// 页面加载完成后绑定事件
$(document).ready(function() {
    // 删除食材按钮点击事件
    $(document).on('click', '.delete-ingredient', function() {
        const $btn = $(this);
        const ingredientId = $btn.data('ingredient-id');
        const $row = $btn.closest('tr');

        // 确认删除
        if (confirm('确定要从采购清单中移除此食材吗？')) {
            $row.fadeOut(300, function() {
                $(this).remove();
            });
        }
    });

    // 确认采购按钮点击事件
    $(document).on('click', '#confirmPurchaseBtn', function() {
        const $btn = $(this);

        // 检查是否有未选择供应商的食材
        let hasEmptySupplier = false;
        let hasEmptyQuantity = false;

        $('#ingredientsPreviewModal tbody tr:visible').each(function() {
            const $row = $(this);
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            const supplierValue = $row.find('.supplier-select').val();

            if (quantity > 0 && !supplierValue) {
                hasEmptySupplier = true;
            }
            if (quantity <= 0) {
                hasEmptyQuantity = true;
                $row.find('.quantity-input').addClass('is-invalid');
            } else {
                $row.find('.quantity-input').removeClass('is-invalid');
            }
        });

        if (hasEmptySupplier) {
            alert('请为所有食材选择供应商或设置为自购');
            return;
        }

        if (hasEmptyQuantity) {
            alert('请确保所有食材的采购数量大于0');
            return;
        }

        // 收集采购数据
        const purchaseData = [];
        $('#ingredientsPreviewModal tbody tr:visible').each(function() {
            const $row = $(this);
            const ingredientId = $row.find('.quantity-input').data('ingredient-id');
            const quantity = parseFloat($row.find('.quantity-input').val()) || 0;
            const unit = $row.find('.unit-select').val();
            const $supplierSelect = $row.find('.supplier-select');
            const productId = $supplierSelect.val();

            if (quantity > 0) {
                purchaseData.push({
                    id: ingredientId,
                    purchase_quantity: quantity,
                    unit: unit,
                    product_id: productId === 'self' ? null : productId
                });
            }
        });

        console.log('采购数据:', purchaseData);

        // 显示加载状态
        $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin"></i> 创建中...');

        // 发送创建订单请求
        fetch('/purchase-order/create-order', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                area_id: parseInt(areaId),
                ingredients: purchaseData
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('创建订单响应:', data);
            if (data.success) {
                alert('采购订单创建成功！');
                window.location.href = data.redirect_url;
            } else {
                alert('创建订单失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('创建订单失败:', error);
            alert('创建订单失败，请重试');
        })
        .finally(() => {
            $btn.prop('disabled', false).html('<i class="fas fa-shopping-cart"></i> 确认采购');
        });
    });
});
</script>
{% endblock %}
