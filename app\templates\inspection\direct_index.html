{% extends 'base.html' %}

{% block title %}{{ title }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .card-header-custom {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    .order-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
    .status-pending {
        background-color: #ffeeba;
        color: #856404;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .order-row {
        cursor: pointer;
    }
    .order-row:hover {
        background-color: #f8f9fa;
    }
    .order-row.selected {
        background-color: #e2f0ff;
    }

    .card-header-custom {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }
    .order-status {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875rem;
    }
    .status-pending {
        background-color: #ffeeba;
        color: #856404;
    }
    .status-approved {
        background-color: #d4edda;
        color: #155724;
    }
    .status-rejected {
        background-color: #f8d7da;
        color: #721c24;
    }
    .status-completed {
        background-color: #d1ecf1;
        color: #0c5460;
    }
    .order-row {
        cursor: pointer;
    }
    .order-row:hover {
        background-color: #f8f9fa;
    }
    .order-row.selected {
        background-color: #e2f0ff;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">入库检查</h1>
    </div>

    <div class="row">
        <!-- 订单列表卡片 -->
        <div class="col-md-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 fw-bold text-primary">待检查订单列表</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="ordersTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>订单编号</th>
                                    <th>供应商</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for order in orders %}
                                <tr class="order-row" data-order-id="{{ order.id }}" data-onclick="selectOrder({{ order.id }})">
                                    <td>{{ order.order_number }}</td>
                                    <td>{{ order.supplier.name if order.supplier else '未知供应商' }}</td>
                                    <td>
                                        <span class="order-status {% if order.status == '待审核' %}status-pending elif '已审核' %}status-approved '已拒绝' %}status-rejected '已入库' %}status-completed endif %}">
                                            {{ order.status }}
                                        </span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 食材列表和检查表单卡片 -->
        <div class="col-md-7">
            <div id="itemsCard" class="card shadow mb-4" style="display:none;">
                <div class="card-header py-3 d-flex justify-content-between align-items-center">
                    <h6 class="m-0 fw-bold text-primary">订单食材列表</h6>
                    <div>
                        <span id="orderInfo" class="me-3"></span>
                        <button id="passBtn" class="btn btn-success btn-sm" data-onclick="passInspection()">通过检查</button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="itemsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>食材名称</th>
                                    <th>数量</th>
                                    <th>单位</th>
                                    <th>单价</th>
                                    <th>金额</th>
                                </tr>
                            </thead>
                            <tbody id="itemsTableBody">
                                <!-- 食材列表将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    // 当前选中的订单ID
    let currentOrderId = null;

    // 选择订单
    function selectOrder(orderId) {
        // 移除之前选中的行的高亮
        document.querySelectorAll('.order-row').forEach(row => {
            row.classList.remove('selected');
        });

        // 高亮当前选中的行
        document.querySelector(`.order-row[data-order-id="${orderId}"]`).classList.add('selected');

        // 保存当前选中的订单ID
        currentOrderId = orderId;

        // 加载订单食材
        loadOrderItems(orderId);
    }

    // 加载订单食材
    function loadOrderItems(orderId) {
        // 显示食材卡片
        document.getElementById('itemsCard').style.display = 'block';

        // 发送AJAX请求获取订单信息和食材列表
        fetch(`/food-trace-api/purchase-orders/${orderId}`)
            .then(response => response.json())
            .then(data => {
                // 显示订单信息
                document.getElementById('orderInfo').textContent = `${data.order_number} - ${data.supplier_name}`;

                // 显示食材列表
                const tbody = document.getElementById('itemsTableBody');
                tbody.innerHTML = '';

                data.items.forEach(item => {
                    const row = document.createElement('tr');
                    const amount = parseFloat(item.quantity) * parseFloat(item.unit_price || 0);
                    row.innerHTML = `
                        <td>${item.ingredient_name || item.name}</td>
                        <td>${item.quantity}</td>
                        <td>${item.unit}</td>
                        <td>¥${parseFloat(item.unit_price || 0).toFixed(2)}</td>
                        <td>¥${amount.toFixed(2)}</td>
                    `;
                    tbody.appendChild(row);
                });
            })
            .catch(error => {
                console.error('获取订单信息失败:', error);
                alert('获取订单信息失败，请重试');
            });
    }

    // 通过检查
    function passInspection() {
        if (!currentOrderId) {
            alert('请先选择一个订单');
            return;
        }

        if (!confirm('确认该订单检查通过吗？')) {
            return;
        }

        // 发送AJAX请求保存检查结果
        fetch('/inspection/quick-pass', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                purchase_order_id: currentOrderId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('检查通过，订单已标记为已入库');
                // 刷新页面
                window.location.reload();
            } else {
                alert('操作失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('保存检查结果失败:', error);
            alert('保存检查结果失败，请重试');
        });
    }

    // 页面加载完成后，如果只有一个订单，自动选中
    document.addEventListener('DOMContentLoaded', function() {
        const orderRows = document.querySelectorAll('.order-row');
        if (orderRows.length === 1) {
            const orderId = orderRows[0].getAttribute('data-order-id');
            selectOrder(parseInt(orderId));
        }
    });
</script>
{% endblock %}

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>