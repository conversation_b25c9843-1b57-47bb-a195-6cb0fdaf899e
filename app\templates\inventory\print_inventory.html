<!DOCTYPE html>
<html>
<head>
    <title>库存报表</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style nonce="{{ csp_nonce }}">
        /* 打印样式 */
        @page {
            size: 297mm 210mm; /* A4横向精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 297mm; /* A4横向宽度 */
            height: 210mm; /* A4横向高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }

        .container {
            width: 267mm; /* A4横向宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .header h2 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .header p {
            font-size: 12pt;
            color: #666;
            margin: 4px 0;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #2c3e50;
            padding: 8px;
            vertical-align: top;
            font-size: 10pt;
        }

        .info-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            text-align: center;
            width: 15%;
            border: 1px solid #000;
        }

        /* 明细表格 */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #2c3e50;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            font-size: 10pt;
        }

        .items-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            border: 1px solid #000;
        }

        .items-table td {
            background-color: white;
        }

        .ingredient-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .ingredient-category {
            font-size: 9px;
            color: #666;
            display: block;
            margin-top: 2px;
        }

        /* 签名区域 */
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            page-break-inside: avoid;
        }

        .signature {
            width: 45%;
            text-align: left;
        }

        .signature p {
            margin: 8px 0;
            font-size: 10pt;
            line-height: 1.8;
        }

        @d-flex print {
            html, body {
                width: 297mm;
                height: 210mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                box-shadow: none;
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
            .info-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
        }
    
        /* 打印样式 */
        @page {
            size: 297mm 210mm; /* A4横向精确尺寸 */
            margin: 1.5cm;
        }
        /* 确保打印时背景色显示 */
        * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
        }
        html, body {
            width: 297mm; /* A4横向宽度 */
            height: 210mm; /* A4横向高度 */
            margin: 0 auto;
            padding: 0;
            font-family: "Microsoft YaHei", "微软雅黑", SimSun, "宋体", sans-serif;
            font-size: 11pt;
            line-height: 1.6;
            background-color: white;
            color: #333;
        }

        .container {
            width: 267mm; /* A4横向宽度减去页边距 */
            margin: 0 auto;
            padding: 15px 0;
            box-sizing: border-box;
        }

        /* 页眉 */
        .header {
            text-align: center;
            margin-bottom: 20px;
            page-break-inside: avoid;
        }

        .header h2 {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 8px;
            color: #2c3e50;
        }

        .header p {
            font-size: 12pt;
            color: #666;
            margin: 4px 0;
        }

        /* 信息表格 */
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .info-table th,
        .info-table td {
            border: 1px solid #2c3e50;
            padding: 8px;
            vertical-align: top;
            font-size: 10pt;
        }

        .info-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            text-align: center;
            width: 15%;
            border: 1px solid #000;
        }

        /* 明细表格 */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
            border: 2px solid #2c3e50;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #2c3e50;
            padding: 6px;
            text-align: center;
            vertical-align: middle;
            font-size: 10pt;
        }

        .items-table th {
            background-color: white;
            color: #000;
            font-weight: bold;
            border: 1px solid #000;
        }

        .items-table td {
            background-color: white;
        }

        .ingredient-name {
            font-weight: bold;
            color: #2c3e50;
        }

        .ingredient-category {
            font-size: 9px;
            color: #666;
            display: block;
            margin-top: 2px;
        }

        /* 签名区域 */
        .footer {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            page-break-inside: avoid;
        }

        .signature {
            width: 45%;
            text-align: left;
        }

        .signature p {
            margin: 8px 0;
            font-size: 10pt;
            line-height: 1.8;
        }

        @d-flex print {
            html, body {
                width: 297mm;
                height: 210mm;
            }
            .no-print {
                display: none !important;
            }
            .container {
                width: 100%;
                padding: 0;
                /* box-shadow: none; */ /* 移除阴影效果 */
            }
            /* 允许表格跨页 */
            table { page-break-inside: auto; }
            tr { page-break-inside: avoid; }
            /* 避免页面元素被分割 */
            .header, .footer { page-break-inside: avoid; }

            /* 表头在每页重复显示 */
            thead { display: table-header-group; }
            tfoot { display: table-footer-group; }

            /* 分页后保持表头样式 */
            .items-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
            .info-table thead tr th {
                background-color: white !important;
                color: #000 !important;
                border: 1px solid #000 !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>库存报表</h2>
            <p>打印时间：{{ print_date.strftime('%Y-%m-%d %H:%M:%S') }}</p>
        </div>

        <table class="info-table">
            <tr>
                <th>仓库</th>
                <td>{{ filter_info.warehouse }}</td>
                <th>食材</th>
                <td>{{ filter_info.ingredient }}</td>
                <th>状态</th>
                <td>{{ filter_info.status }}</td>
            </tr>
            <tr>
                <th>存储位置</th>
                <td>{{ filter_info.storage_location }}</td>
                <th>记录数量</th>
                <td>{{ inventories|length }} 条</td>
                <th>食材种类</th>
                <td>{{ inventories|map(attribute='ingredient.name')|unique|list|length }} 种</td>
            </tr>
            <tr>
                <th>正常库存</th>
                <td>{{ inventories|selectattr('status', 'equalto', '正常')|list|length }} 条</td>
                <th>仓库数量</th>
                <td>{{ inventories|map(attribute='warehouse.name')|unique|list|length }} 个</td>
                <th>打印人</th>
                <td>{{ current_user.real_name or current_user.username if current_user else '-' }}</td>
            </tr>
        </table>

    <table class="items-table">
        <thead>
            <tr>
                <th>序号</th>
                <th>食材名称</th>
                <th>仓库</th>
                <th>存储位置</th>
                <th>批次号</th>
                <th>数量</th>
                <th>单位</th>
                <th>生产日期</th>
                <th>过期日期</th>
                <th>状态</th>
                <th>备注</th>
            </tr>
        </thead>
        <tbody>
            {% for inventory in inventories %}
            <tr>
                <td>{{ loop.index }}</td>
                <td>
                    <div class="ingredient-name">{{ inventory.ingredient.name }}</div>
                    {% if inventory.ingredient.category %}
                    <div class="ingredient-category">{{ inventory.ingredient.category.name }}</div>
                    {% endif %}
                </td>
                <td>{{ inventory.warehouse.name }}</td>
                <td>
                    {{ inventory.storage_location.name }}
                    <br>({{ inventory.storage_location.location_code }})
                </td>
                <td>{{ inventory.batch_number }}</td>
                <td>{{ inventory.quantity }}</td>
                <td>{{ inventory.unit }}</td>
                <td>{{ inventory.production_date.strftime('%Y-%m-%d') if inventory.production_date else '-' }}</td>
                <td>{{ inventory.expiry_date.strftime('%Y-%m-%d') if inventory.expiry_date else '-' }}</td>
                <td>{{ inventory.status }}</td>
                <td>{{ inventory.notes or '-' }}</td>
            </tr>
            {% else %}
            <tr>
                <td colspan="11" style="text-align: center; padding: 20px;">
                    暂无库存数据
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>

        <div class="footer">
            <div class="signature">
                <p>库存管理员：{{ current_user.real_name or current_user.username if current_user else '________________' }}</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>仓库负责人：________________</p>
                <p>签名：________________</p>
            </div>
        </div>

        <div class="footer">
            <div class="signature">
                <p>审核人：________________</p>
                <p>签名：________________</p>
            </div>
            <div class="signature">
                <p>打印时间：{{ print_date.strftime('%Y-%m-%d %H:%M:%S') if print_date else '' }}</p>
            </div>
        </div>

        <div class="no-print" style="text-align: center; margin-top: 20px;">
            <button class="print-button">打印</button>
            <button data-onclick="window.close()">关闭</button>
        </div>
    </div>

    <script nonce="{{ csp_nonce }}">
        // 页面加载完成后自动聚焦
        window.onload = function() {
            window.focus();
        };
    </script>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/comprehensive-event-handler.js') }}"></script>
</body>
</html>
