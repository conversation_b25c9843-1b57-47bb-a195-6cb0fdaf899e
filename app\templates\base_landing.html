<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#165DFF">
    <title>{% block title %}{{ project_name|default('校园餐智慧食堂 ') }}{% endblock %}</title>

    <!-- 动态Favicon -->
    {% if system_logo %}
    <link rel="icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ system_logo }}">
    <link rel="apple-touch-icon" href="{{ system_logo }}">
    {% else %}
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    <link rel="shortcut icon" type="image/x-icon" href="{{ url_for('static', filename='img/favicon.ico') }}">
    {% endif %}
    
    <!-- 基础样式 -->
    <link rel="stylesheet" href="{{ url_for('static', filename='bootstrap/css/bootstrap.min.css') }}?v=1.0.0">
    <link rel="stylesheet" href="{{ url_for('static', filename='vendor/fontawesome/css/all.min.css') }}?v=1.0.0">
    
    {% block extra_css %}{% endblock %}
    
    <style nonce="{{ csp_nonce }}">
        /* 基础重置样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            overflow-x: hidden;
        }
        
        /* 隐藏默认滚动条但保持功能 */
        ::-webkit-scrollbar {
            width: 8px;
        }
        
        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #165DFF;
            border-radius: 4px;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #0D47A1;
        }
        
        /* 确保容器样式 */
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }
        
        @d-flex (min-width: 768px) {
            .container {
                padding: 0 2rem;
            }
        }
        
        /* 响应式工具类 */
        .d-none { display: none !important; }
        .d-block { display: block !important; }
        .d-flex { display: flex !important; }
        .justify-content-center { justify-content: center !important; }
        .align-items-center { align-items: center !important; }
        .text-center { text-align: center !important; }
        .w-100 { width: 100% !important; }
        .h-100 { height: 100% !important; }
        
        @d-flex (min-width: 768px) {
            .d-md-block { display: block !important; }
            .d-md-flex { display: flex !important; }
            .d-md-none { display: none !important; }
        }
        
        @d-flex (min-width: 1024px) {
            .d-lg-block { display: block !important; }
            .d-lg-flex { display: flex !important; }
            .d-lg-none { display: none !important; }
        }
        
        /* 简单的网格系统 */
        .row {
            display: flex;
            flex-wrap: wrap;
            margin: 0 -0.5rem;
        }
        
        .col {
            flex: 1;
            padding: 0 0.5rem;
        }
        
        .col-12 { flex: 0 0 100%; max-width: 100%; }
        .col-6 { flex: 0 0 50%; max-width: 50%; }
        .col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
        .col-3 { flex: 0 0 25%; max-width: 25%; }
        
        @d-flex (min-width: 768px) {
            .col-md-12 { flex: 0 0 100%; max-width: 100%; }
            .col-md-6 { flex: 0 0 50%; max-width: 50%; }
            .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
            .col-md-3 { flex: 0 0 25%; max-width: 25%; }
        }
        
        @d-flex (min-width: 1024px) {
            .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
            .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
            .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
            .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
        }
        
        /* 间距工具类 */
        .m-0 { margin: 0 !important; }
        .m-1 { margin: 0.25rem !important; }
        .m-2 { margin: 0.5rem !important; }
        .m-3 { margin: 1rem !important; }
        .m-4 { margin: 1.5rem !important; }
        .m-5 { margin: 3rem !important; }
        
        .p-0 { padding: 0 !important; }
        .p-1 { padding: 0.25rem !important; }
        .p-2 { padding: 0.5rem !important; }
        .p-3 { padding: 1rem !important; }
        .p-4 { padding: 1.5rem !important; }
        .p-5 { padding: 3rem !important; }
        
        .mt-0 { margin-top: 0 !important; }
        .mt-1 { margin-top: 0.25rem !important; }
        .mt-2 { margin-top: 0.5rem !important; }
        .mt-3 { margin-top: 1rem !important; }
        .mt-4 { margin-top: 1.5rem !important; }
        .mt-5 { margin-top: 3rem !important; }
        
        .mb-0 { margin-bottom: 0 !important; }
        .mb-1 { margin-bottom: 0.25rem !important; }
        .mb-2 { margin-bottom: 0.5rem !important; }
        .mb-3 { margin-bottom: 1rem !important; }
        .mb-4 { margin-bottom: 1.5rem !important; }
        .mb-5 { margin-bottom: 3rem !important; }
        
        .pt-0 { padding-top: 0 !important; }
        .pt-1 { padding-top: 0.25rem !important; }
        .pt-2 { padding-top: 0.5rem !important; }
        .pt-3 { padding-top: 1rem !important; }
        .pt-4 { padding-top: 1.5rem !important; }
        .pt-5 { padding-top: 3rem !important; }
        
        .pb-0 { padding-bottom: 0 !important; }
        .pb-1 { padding-bottom: 0.25rem !important; }
        .pb-2 { padding-bottom: 0.5rem !important; }
        .pb-3 { padding-bottom: 1rem !important; }
        .pb-4 { padding-bottom: 1.5rem !important; }
        .pb-5 { padding-bottom: 3rem !important; }
        
        /* 颜色工具类 */
        .text-white { color: white !important; }
        .text-dark { color: #333 !important; }
        .text-muted { color: #6c757d !important; }
        .text-primary { color: #165DFF !important; }
        .text-success { color: #28a745 !important; }
        .text-warning { color: #ffc107 !important; }
        .text-danger { color: #dc3545 !important; }
        .text-info { color: #17a2b8 !important; }
        
        .bg-white { background-color: white !important; }
        .bg-dark { background-color: #333 !important; }
        .bg-primary { background-color: #165DFF !important; }
        .bg-light { background-color: #f8f9fa !important; }
        
        /* 边框工具类 */
        .border { border: 1px solid #dee2e6 !important; }
        .border-0 { border: 0 !important; }
        .rounded { border-radius: 0.375rem !important; }
        .rounded-lg { border-radius: 0.5rem !important; }
        .rounded-xl { border-radius: 0.75rem !important; }
        .rounded-2xl { border-radius: 1rem !important; }
        .rounded-full { border-radius: 9999px !important; }
        
        /* 阴影工具类 */
        .shadow { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
        .shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
        .shadow-lg { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
        
        /* 位置工具类 */
        .position-relative { position: relative !important; }
        .position-absolute { position: absolute !important; }
        .position-fixed { position: fixed !important; }
        
        /* 溢出工具类 */
        .overflow-hidden { overflow: hidden !important; }
        .overflow-auto { overflow: auto !important; }
        
        /* 字体工具类 */
        .fw-normal { font-weight: 400 !important; }
        .fw-bold { font-weight: 700 !important; }
        .font-size-sm { font-size: 0.875rem !important; }
        .font-size-lg { font-size: 1.125rem !important; }
        .font-size-xl { font-size: 1.25rem !important; }
        
        /* 过渡效果 */
        .transition { transition: all 0.3s ease !important; }
    </style>
</head>
<body>
    {% block content %}{% endblock %}
    
    <!-- 基础JavaScript -->
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>
    <script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
    
    {% block scripts %}{% endblock %}
</body>
</html>
