{% extends 'base.html' %}

{% block title %}员工管理 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
    .dashboard-card {
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        height: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dashboard-card .card-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-card .card-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .dashboard-card.bg-primary,
    .dashboard-card.bg-success,
    .dashboard-card.bg-info,
    .dashboard-card.bg-warning,
    .dashboard-card.bg-danger {
        color: white;
    }

    .chart-container {
        height: 250px;
    }

    .alert-items {
        max-height: 200px;
        overflow-y: auto;
    }

    /* 顶部按钮样式 */
    .btn-group {
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        border-radius: 4px;
        overflow: hidden;
    }

    .btn-group .btn {
        border-radius: 0;
        margin: 0;
        padding: 8px 16px;
        font-weight: 500;
        border: none;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .btn-group .btn i {
        margin-right: 5px;
    }

    .dashboard-card {
        border-radius: 10px;
        /* box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); */ /* 移除阴影效果 */
        transition: all 0.3s ease;
        height: 100%;
    }

    .dashboard-card:hover {
        transform: translateY(-5px);
        /* box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15); */ /* 移除阴影效果 */
    }

    .dashboard-card .card-body {
        padding: 1.5rem;
    }

    .dashboard-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .dashboard-card .card-value {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.5rem;
    }

    .dashboard-card .card-icon {
        font-size: 2.5rem;
        opacity: 0.8;
    }

    .dashboard-card.bg-primary,
    .dashboard-card.bg-success,
    .dashboard-card.bg-info,
    .dashboard-card.bg-warning,
    .dashboard-card.bg-danger {
        color: white;
    }

    .chart-container {
        height: 250px;
    }

    .alert-items {
        max-height: 200px;
        overflow-y: auto;
    }

    /* 顶部按钮样式 */
    .btn-group {
        /* box-shadow: 0 2px 5px rgba(0,0,0,0.1); */ /* 移除阴影效果 */
        border-radius: 4px;
        overflow: hidden;
    }

    .btn-group .btn {
        border-radius: 0;
        margin: 0;
        padding: 8px 16px;
        font-weight: 500;
        border: none;
    }

    .btn-group .btn:first-child {
        border-top-left-radius: 4px;
        border-bottom-left-radius: 4px;
    }

    .btn-group .btn:last-child {
        border-top-right-radius: 4px;
        border-bottom-right-radius: 4px;
    }

    .btn-group .btn i {
        margin-right: 5px;
    }
</style>
{% endblock %}

{% block content %}
<!-- 桌面端头部 -->
<div class="row mb-4 align-items-center desktop-only">
    <div class="col-md-6">
        <h2 class="mb-0">员工管理仪表盘</h2>
    </div>
    <div class="col-md-6">
        <div class="text-end">
            <div class="btn-group">
                <a href="{{ url_for('employee.add_employee') }}" class="btn btn-primary">
                    <i class="fas fa-plus"></i> 添加员工
                </a>
                <a href="{{ url_for('employee.health_certificates') }}" class="btn btn-info">
                    <i class="fas fa-id-card"></i> 健康证管理
                </a>
                <a href="{{ url_for('employee.daily_health_check') }}" class="btn btn-success">
                    <i class="fas fa-heartbeat"></i> 日常健康检查
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 移动端头部 -->
<div class="mobile-only mb-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-3">员工管理仪表盘</h2>
            <div class="action-buttons">
                <a href="{{ url_for('employee.add_employee') }}" class="btn btn-primary mb-2">
                    <i class="fas fa-plus"></i> 添加员工
                </a>
                <a href="{{ url_for('employee.health_certificates') }}" class="btn btn-info mb-2">
                    <i class="fas fa-id-card"></i> 健康证管理
                </a>
                <a href="{{ url_for('employee.daily_health_check') }}" class="btn btn-success mb-2">
                    <i class="fas fa-heartbeat"></i> 日常健康检查
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 仪表盘统计卡片 -->
<div class="row mb-4">
    <div class="col-lg-3 col-md-6 col-12 mb-3">
        <div class="card dashboard-card bg-primary">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">员工总数</h5>
                        <h2 class="card-value">{{ dashboard_data.total_employees }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-users card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-3">
        <div class="card dashboard-card bg-success">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">在职员工</h5>
                        <h2 class="card-value">{{ dashboard_data.active_employees }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-user-check card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-3">
        <div class="card dashboard-card bg-warning">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">休假员工</h5>
                        <h2 class="card-value">{{ dashboard_data.on_leave_employees }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-user-clock card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-12 mb-3">
        <div class="card dashboard-card bg-secondary">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">离职员工</h5>
                        <h2 class="card-value">{{ dashboard_data.inactive_employees }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-user-times card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-info">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">健康证有效</h5>
                        <h2 class="card-value">{{ dashboard_data.health_cert_valid }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-id-card card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-warning">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">即将到期</h5>
                        <h2 class="card-value">{{ dashboard_data.health_cert_expiring }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-exclamation-triangle card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-danger">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">已过期</h5>
                        <h2 class="card-value">{{ dashboard_data.health_cert_expired }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-calendar-times card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card dashboard-card bg-secondary">
            <div class="card-body">
                <div class="row">
                    <div class="col-8">
                        <h5 class="card-title">未办理</h5>
                        <h2 class="card-value">{{ dashboard_data.health_cert_none }}</h2>
                    </div>
                    <div class="col-4 text-end">
                        <i class="fas fa-file-alt card-icon"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">部门分布</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="departmentChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">职位分布</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="positionChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">区域分布</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="areaChart"></canvas>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">系统账号关联情况</h5>
            </div>
            <div class="card-body">
                <div class="chart-container">
                    <canvas id="accountChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 健康证提醒 -->
<div class="row mb-4">
    <div class="col-md-6">
        {% if expiring_certs %}
        <div class="card">
            <div class="card-header bg-warning text-white">
                <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> 健康证即将到期提醒</h5>
            </div>
            <div class="card-body alert-items">
                <ul class="mb-0">
                    {% for item in expiring_certs %}
                    <li>
                        <strong>{{ item.employee.name }}</strong> 的健康证将在 <strong>{{ item.days_left }}</strong> 天后到期
                        ({{  item.certificate.expire_date|format_datetime('%Y-%m-%d')  }})
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
    </div>
    <div class="col-md-6">
        {% if expired_certs %}
        <div class="card">
            <div class="card-header bg-danger text-white">
                <h5 class="mb-0"><i class="fas fa-calendar-times"></i> 健康证已过期提醒</h5>
            </div>
            <div class="card-body alert-items">
                <ul class="mb-0">
                    {% for item in expired_certs %}
                    <li>
                        <strong>{{ item.employee.name }}</strong> 的健康证已过期 <strong>{{ item.days_expired }}</strong> 天
                        ({{  item.certificate.expire_date|format_datetime('%Y-%m-%d')  }})
                    </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<div class="card">
    <div class="card-body">
        <!-- 桌面端表格 -->
        <div class="table-responsive desktop-only">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>姓名</th>
                        <th>性别</th>
                        <th>职位</th>
                        <th>部门</th>
                        <th>所属区域</th>
                        <th>联系电话</th>
                        <th>状态</th>
                        <th>健康证状态</th>
                        <th>系统账号</th>
                        <th>食品安全</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    {% for employee in employees.items %}
                    <tr>
                        <td>{{ employee.id }}</td>
                        <td>{{ employee.name }}</td>
                        <td>{{ employee.gender }}</td>
                        <td>{{ employee.position }}</td>
                        <td>{{ employee.department }}</td>
                        <td>
                            {% if employee.area %}
                            <span class="badge bg-info">{{ employee.area.get_level_name() }}</span>
                            {{ employee.area.name }}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </td>
                        <td>{{ employee.phone }}</td>
                        <td>
                            {% if employee.status == 1 %}
                            <span class="badge bg-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge bg-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge bg-warning">休假</span>
                            {% endif %}
                        </td>
                        <td>
                            {% set cert_status = employee.get_health_certificate_status() %}
                            {% if cert_status == "有效" %}
                            <span class="badge bg-success">{{ cert_status }}</span>
                            {% elif cert_status == "未办理" %}
                            <span class="badge bg-secondary">{{ cert_status }}</span>
                            {% elif cert_status == "已过期" %}
                            <span class="badge bg-danger">{{ cert_status }}</span>
                            {% elif "即将过期" in cert_status %}
                            <span class="badge bg-warning">{{ cert_status }}</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.user %}
                                <span class="badge bg-primary">已关联</span>
                                {% if employee.user.status == 0 %}
                                    <span class="badge bg-danger">已禁用</span>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">未关联</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if employee.responsible_areas %}
                                <span class="badge bg-info" title="负责区域">
                                    <i class="fas fa-map-marker-alt"></i>
                                </span>
                            {% endif %}

                            {% if employee.food_safety_certifications %}
                                <span class="badge bg-success" title="食品安全证书">
                                    <i class="fas fa-certificate"></i>
                                </span>
                            {% endif %}

                            {% if employee.safety_violation_count and employee.safety_violation_count > 0 %}
                                <span class="badge bg-danger" title="安全违规: {{ employee.safety_violation_count }}次">
                                    <i class="fas fa-exclamation-triangle"></i> {{ employee.safety_violation_count }}
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    {% else %}
                    <tr>
                        <td colspan="12" class="text-center">暂无员工数据</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- 移动端员工卡片 -->
        <div class="mobile-only">
            {% for employee in employees.items %}
            <div class="card mb-3 border-start-{% if employee.status == 1 %}success{% elif 2 %}warning{% else %}secondary{% endif %}">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-8">
                            <h6 class="mb-1">{{ employee.name }}</h6>
                            <small class="text-muted">ID: {{ employee.id }} | {{ employee.gender }}</small>
                        </div>
                        <div class="col-4 text-end">
                            {% if employee.status == 1 %}
                            <span class="badge bg-success">在职</span>
                            {% elif employee.status == 0 %}
                            <span class="badge bg-secondary">离职</span>
                            {% elif employee.status == 2 %}
                            <span class="badge bg-warning">休假</span>
                            {% endif %}
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">职位</small>
                            <div class="small">{{ employee.position }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">部门</small>
                            <div class="small">{{ employee.department }}</div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">联系电话</small>
                            <div class="small">{{ employee.phone }}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">所属区域</small>
                            <div class="small">
                                {% if employee.area %}
                                {{ employee.area.name }}
                                {% else %}
                                未设置
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-6">
                            <small class="text-muted">健康证状态</small>
                            <div>
                                {% set cert_status = employee.get_health_certificate_status() %}
                                {% if cert_status == "有效" %}
                                <span class="badge bg-success bg-sm">{{ cert_status }}</span>
                                {% elif cert_status == "未办理" %}
                                <span class="badge bg-secondary bg-sm">{{ cert_status }}</span>
                                {% elif cert_status == "已过期" %}
                                <span class="badge bg-danger bg-sm">{{ cert_status }}</span>
                                {% elif "即将过期" in cert_status %}
                                <span class="badge bg-warning bg-sm">{{ cert_status }}</span>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">系统账号</small>
                            <div>
                                {% if employee.user %}
                                <span class="badge bg-primary bg-sm">已关联</span>
                                {% if employee.user.status == 0 %}
                                <span class="badge bg-danger bg-sm">已禁用</span>
                                {% endif %}
                                {% else %}
                                <span class="badge bg-secondary bg-sm">未关联</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <div class="row mt-2">
                        <div class="col-12">
                            <div class="btn-group btn-group-sm w-100" role="group">
                                <a href="{{ url_for('employee.view_employee', id=employee.id) }}" class="btn btn-outline-info" title="查看详情">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="{{ url_for('employee.edit_employee', id=employee.id) }}" class="btn btn-outline-primary" title="编辑信息">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_certificate', employee_id=employee.id) }}" class="btn btn-outline-warning" title="添加健康证">
                                    <i class="fas fa-id-card"></i>
                                </a>
                                <a href="{{ url_for('employee.add_health_check', employee_id=employee.id) }}" class="btn btn-outline-success" title="健康检查">
                                    <i class="fas fa-heartbeat"></i>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% else %}
            <div class="text-center py-4">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5>暂无员工数据</h5>
                <p class="text-muted">您可以添加新的员工信息</p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% if employees.pages > 1 %}
    <div class="card-footer">
        <nav aria-label="Page navigation">
            <ul class="pagination justify-content-center mb-0">
                <li class="page-item {% if not employees.has_prev %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.prev_num) if employees.has_prev else '#' }}">
                        <i class="fas fa-chevron-left"></i> 上一页
                    </a>
                </li>
                {% for page in employees.iter_pages() %}
                    {% if page %}
                        <li class="page-item {% if page == employees.page %}active{% endif %}">
                            <a class="page-link" href="{{ url_for('employee.index', page=page) }}">{{ page }}</a>
                        </li>
                    {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                    {% endif %}
                {% endfor %}
                <li class="page-item {% if not employees.has_next %}disabled{% endif %}">
                    <a class="page-link" href="{{ url_for('employee.index', page=employees.next_num) if employees.has_next else '#' }}">
                        下一页 <i class="fas fa-chevron-right"></i>
                    </a>
                </li>
            </ul>
        </nav>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart.min.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 部门分布图表
        var departmentCtx = document.getElementById('departmentChart').getContext('2d');
        var departmentChart = new Chart(departmentCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for dept, count in dashboard_data.departments.items() %}
                    '{{ dept }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for dept, count in dashboard_data.departments.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)',
                        'rgba(255, 159, 64, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)',
                        'rgba(255, 159, 64, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 职位分布图表
        var positionCtx = document.getElementById('positionChart').getContext('2d');
        var positionChart = new Chart(positionCtx, {
            type: 'bar',
            data: {
                labels: [
                    {% for position, count in dashboard_data.positions.items() %}
                    '{{ position }}',
                    {% endfor %}
                ],
                datasets: [{
                    label: '员工数量',
                    data: [
                        {% for position, count in dashboard_data.positions.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            precision: 0
                        }
                    }
                }
            }
        });

        // 区域分布图表
        var areaCtx = document.getElementById('areaChart').getContext('2d');
        var areaChart = new Chart(areaCtx, {
            type: 'pie',
            data: {
                labels: [
                    {% for area, count in dashboard_data.areas.items() %}
                    '{{ area }}',
                    {% endfor %}
                ],
                datasets: [{
                    data: [
                        {% for area, count in dashboard_data.areas.items() %}
                        {{ count }},
                        {% endfor %}
                    ],
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.7)',
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 206, 86, 0.7)',
                        'rgba(75, 192, 192, 0.7)',
                        'rgba(153, 102, 255, 0.7)'
                    ],
                    borderColor: [
                        'rgba(255, 99, 132, 1)',
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 206, 86, 1)',
                        'rgba(75, 192, 192, 1)',
                        'rgba(153, 102, 255, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });

        // 系统账号关联情况图表
        var accountCtx = document.getElementById('accountChart').getContext('2d');
        var accountChart = new Chart(accountCtx, {
            type: 'doughnut',
            data: {
                labels: ['已关联系统账号', '未关联系统账号'],
                datasets: [{
                    data: [
                        {{ dashboard_data.with_system_account }},
                        {{ dashboard_data.without_system_account }}
                    ],
                    backgroundColor: [
                        'rgba(54, 162, 235, 0.7)',
                        'rgba(255, 99, 132, 0.7)'
                    ],
                    borderColor: [
                        'rgba(54, 162, 235, 1)',
                        'rgba(255, 99, 132, 1)'
                    ],
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        });
    });
</script>
{% endblock %}