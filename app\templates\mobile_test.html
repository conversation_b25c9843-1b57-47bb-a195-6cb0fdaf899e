{% extends "base.html" %}

{% block title %}移动端体验测试{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-header">
                <h1>📱 移动端体验测试</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ url_for('main.index') }}">首页</a></li>
                        <li class="breadcrumb-item active">移动端测试</li>
                    </ol>
                </nav>
            </div>

            <!-- 设备信息 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">📊 设备信息</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>屏幕宽度:</strong> <span id="screen-width">-</span>px</p>
                            <p><strong>屏幕高度:</strong> <span id="screen-height">-</span>px</p>
                            <p><strong>设备像素比:</strong> <span id="device-ratio">-</span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>用户代理:</strong> <span id="user-agent">-</span></p>
                            <p><strong>触摸支持:</strong> <span id="touch-support">-</span></p>
                            <p><strong>移动端检测:</strong> <span id="mobile-detected">-</span></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 导航测试 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🧭 导航测试</h5>
                </div>
                <div class="card-body">
                    <p>请测试顶部导航栏的下拉菜单是否在移动端正常工作：</p>
                    <div class="alert alert-info">
                        <strong>测试要点：</strong>
                        <ul class="mb-0">
                            <li>点击导航栏的汉堡菜单按钮</li>
                            <li>点击下拉菜单项（如主题切换、通知等）</li>
                            <li>检查菜单项是否可以正常点击和跳转</li>
                            <li>检查触摸反馈是否正常</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 按钮测试 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🔘 按钮测试</h5>
                </div>
                <div class="card-body">
                    <div class="action-buttons">
                        <button type="button" class="btn btn-primary">主要按钮</button>
                        <button type="button" class="btn btn-secondary">次要按钮</button>
                        <button type="button" class="btn btn-success">成功按钮</button>
                        <button type="button" class="btn btn-warning">警告按钮</button>
                        <button type="button" class="btn btn-danger">危险按钮</button>
                    </div>
                    
                    <div class="mt-3">
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary">按钮组1</button>
                            <button type="button" class="btn btn-outline-primary">按钮组2</button>
                            <button type="button" class="btn btn-outline-primary">按钮组3</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 表单测试 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">📝 表单测试</h5>
                </div>
                <div class="card-body">
                    <form>
                        <div class="mb-3">
                            <label for="test-input">文本输入</label>
                            <input type="text" class="form-control" id="test-input" placeholder="请输入文本">
                        </div>
                        
                        <div class="mb-3">
                            <label for="test-select">下拉选择</label>
                            <select class="form-control" id="test-select">
                                <option>选项1</option>
                                <option>选项2</option>
                                <option>选项3</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label for="test-textarea">文本域</label>
                            <textarea class="form-control" id="test-textarea" rows="3" placeholder="请输入多行文本"></textarea>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="test-checkbox">
                            <label class="form-check-label" for="test-checkbox">
                                复选框选项
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="test-radio" id="test-radio1">
                            <label class="form-check-label" for="test-radio1">
                                单选框选项1
                            </label>
                        </div>
                        
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="test-radio" id="test-radio2">
                            <label class="form-check-label" for="test-radio2">
                                单选框选项2
                            </label>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">提交表单</button>
                    </form>
                </div>
            </div>

            <!-- 表格测试 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">📊 表格测试</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>姓名</th>
                                    <th>邮箱</th>
                                    <th>电话</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>张三</td>
                                    <td><EMAIL></td>
                                    <td>13800138000</td>
                                    <td><span class="badge bg-success">活跃</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>李四</td>
                                    <td><EMAIL></td>
                                    <td>13900139000</td>
                                    <td><span class="badge bg-warning">待审核</span></td>
                                    <td>
                                        <button class="btn btn-sm btn-primary">编辑</button>
                                        <button class="btn btn-sm btn-danger">删除</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 模态框测试 -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">🪟 模态框测试</h5>
                </div>
                <div class="card-body">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#testModal">
                        打开测试模态框
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>

<!-- 测试模态框 -->
<div class="modal fade" id="testModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">测试模态框</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>这是一个测试模态框，用于验证移动端的模态框显示效果。</p>
                <p>请检查：</p>
                <ul>
                    <li>模态框是否正确居中显示</li>
                    <li>模态框大小是否适合移动端屏幕</li>
                    <li>关闭按钮是否容易点击</li>
                    <li>背景遮罩是否正常</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary">确定</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
$(document).ready(function() {
    // 显示设备信息
    $('#screen-width').text(window.screen.width);
    $('#screen-height').text(window.screen.height);
    $('#device-ratio').text(window.devicePixelRatio || 1);
    $('#user-agent').text(navigator.userAgent.substring(0, 50) + '...');
    $('#touch-support').text('ontouchstart' in window ? '是' : '否');
    $('#mobile-detected').text(window.innerWidth <= 768 ? '是' : '否');
    
    // 按钮点击测试
    $('.btn').on('click', function() {
        if (!$(this).attr('data-toggle') && !$(this).attr('data-dismiss') && $(this).attr('type') !== 'submit') {
            toastr.success('按钮点击成功！', '测试结果');
        }
    });
    
    // 表单提交测试
    $('form').on('submit', function(e) {
        e.preventDefault();
        toastr.info('表单提交测试成功！', '测试结果');
    });
});
</script>
{% endblock %}
