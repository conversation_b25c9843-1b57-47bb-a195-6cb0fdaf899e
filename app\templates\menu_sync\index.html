{% extends "base.html" %}

{% block title %}菜单同步{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <p>此工具用于将周菜单或日菜单数据同步到工作日志中。</p>
                        <p>同步后，工作日志中的早餐、午餐、晚餐菜单字段将自动填充菜单中的菜品名称。</p>
                    </div>

                    <div class="row">
                        <!-- 区域同步 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">按区域和日期范围同步</h4>
                                </div>
                                <div class="card-body">
                                    <form action="#" method="post" id="area-sync-form" novalidate novalidate>
        {{ csrf_token() }}
                                        <div class="mb-3">
                                            <label for="area_id">选择区域</label>
                                            <select class="form-control" id="area_id" name="area_id" required>
                                                {% for area in areas %}
                                                <option value="{{ area.id }}" {% if loop.first %}selected{% endif %}>{{ area.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="start_date">开始日期</label>
                                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                                        </div>
                                        <div class="mb-3">
                                            <label for="end_date">结束日期</label>
                                            <input type="date" class="form-control" id="end_date" name="end_date" required>
                                        </div>
                                        <button type="button" class="btn btn-primary" id="sync-area-btn">同步选定区域的菜单</button>
                                    </form>
                                </div>
                            </div>
                        </div>

                        <!-- 最近的周菜单 -->
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">最近的周菜单</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th>区域</th>
                                                    <th>周期</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for menu in recent_weekly_menus %}
                                                <tr>
                                                    <td>{{ menu.area_name }}</td>
                                                    <td>{{ menu.week_start }} 至 {{ menu.week_end }}</td>
                                                    <td>{{ menu.status }}</td>
                                                    <td>
                                                        <form action="{{ url_for('menu_sync.sync_weekly_menu', weekly_menu_id=menu.id) }}" method="post" novalidate novalidate>
        {{ csrf_token() }}
                                                            <button type="submit" class="btn btn-sm btn-primary" {% if menu.status != '已发布' %}disabled{% endif %}>
                                                                同步
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="4" class="text-center">没有找到周菜单</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 最近的日菜单 -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4 class="card-title">最近的日菜单</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-hover">
                                            <thead>
                                                <tr>
                                                    <th>区域</th>
                                                    <th>日期</th>
                                                    <th>餐次</th>
                                                    <th>状态</th>
                                                    <th>操作</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for plan in recent_menu_plans %}
                                                <tr>
                                                    <td>{{ plan.area_name }}</td>
                                                    <td>{{ plan.plan_date }}</td>
                                                    <td>{{ plan.meal_type }}</td>
                                                    <td>{{ plan.status }}</td>
                                                    <td>
                                                        <form action="{{ url_for('menu_sync.sync_menu_plan', menu_plan_id=plan.id) }}" method="post" novalidate novalidate>
        {{ csrf_token() }}
                                                            <button type="submit" class="btn btn-sm btn-primary" {% if plan.status %}{% if variable not in ['已发布', '已执行']   %}disabled{% endif %}{% if {% if plan.status  endif %}>
                                                                同步
                                                            </button>
                                                        </form>
                                                    </td>
                                                </tr>
                                                {% else %}
                                                <tr>
                                                    <td colspan="5" class="text-center">没有找到日菜单</td>
                                                </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 设置默认日期
        var today = new Date();
        var oneWeekAgo = new Date();
        oneWeekAgo.setDate(today.getDate() - 7);
        
        $('#start_date').val(oneWeekAgo.toISOString().split('T')[0]);
        $('#end_date').val(today.toISOString().split('T')[0]);
        
        // 区域同步按钮点击事件
        $('#sync-area-btn').click(function() {
            var areaId = $('#area_id').val();
            var startDate = $('#start_date').val();
            var endDate = $('#end_date').val();
            
            if (!areaId || !startDate || !endDate) {
                alert('请填写所有必填字段');
                return;
            }
            
            // 提交表单
            var form = $('#area-sync-form');
            form.attr('action', '/menu-sync/area/' + areaId);
            form.submit();
        });
    });
</script>
{% endblock %}
