2025-06-16 01:11:11,475 INFO: 应用启动 - PID: 16128 [in C:\StudentsCMSSP\app\__init__.py:839]
2025-06-16 01:19:43,483 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:22:23,093 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:39:07,955 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:39:10,963 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:44:22,330 INFO: 查询菜谱：日期=2025-06-16, 星期=0(0=周一), day_of_week=1, 餐次=午餐, 区域ID=22 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-16 01:44:22,334 INFO: 找到 0 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-16 01:44:22,334 INFO: 未找到 2025-06-16 午餐 的菜谱信息 [in C:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-16 01:44:22,340 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-16 01:44:48,080 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:51:21,502 INFO: 查询菜谱：日期=2025-06-16, 星期=0(0=周一), day_of_week=1, 餐次=午餐, 区域ID=42 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-16 01:51:21,502 INFO: 找到 0 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-16 01:51:21,503 INFO: 未找到 2025-06-16 午餐 的菜谱信息 [in C:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-16 01:51:21,504 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-16 01:52:05,466 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:54:02,585 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-16 01:56:04,640 INFO: 当前用户: 13974081099 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-16 01:56:04,641 INFO: 用户区域ID: 22 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-16 01:56:04,642 INFO: 用户区域名称: 城南小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-16 01:56:04,642 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-16 01:56:21,302 INFO: 当前用户: 13974081099 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-16 01:56:21,303 INFO: 用户区域ID: 22 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-16 01:56:21,305 INFO: 用户区域名称: 城南小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-16 01:56:21,305 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-16 01:56:27,994 INFO: 当前用户: 13974081099 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-16 01:56:27,994 INFO: 用户区域ID: 22 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-16 01:56:27,995 INFO: 用户区域名称: 城南小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-16 01:56:27,995 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
