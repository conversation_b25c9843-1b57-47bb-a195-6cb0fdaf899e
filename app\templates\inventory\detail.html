{% extends 'base.html' %}

{% block title %}库存详情{% endblock %}

{% block styles %}
{{ super() }}
<style nonce="{{ csp_nonce }}">
/* 库存详情页面样式 */
.compact-toolbar {
    background: #f8f9fa;
    padding: 10px 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.ingredient-highlight-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border-start: 4px solid #28a745;
}

.info-item {
    margin-bottom: 15px;
}

.info-label {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 3px;
    display: block;
}

.info-value {
    font-size: 14px;
    color: #495057;
    font-weight: 500;
}

.ingredient-name {
    font-size: 18px;
    font-weight: bold;
    color: #28a745;
}

.batch-number {
    background: #e9ecef;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
}

.inventory-status-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
}

.status-header {
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 10px;
    margin-bottom: 15px;
}

.status-item {
    margin-bottom: 12px;
}

.status-label {
    font-size: 11px;
    color: #6c757d;
    font-weight: 500;
    margin-bottom: 2px;
    display: block;
}

.status-value {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
}

.quantity-highlight {
    font-size: 20px;
    font-weight: bold;
    color: #007bff;
}

.unit {
    font-size: 14px;
    color: #6c757d;
}

/* 卡片标题样式 */
.card-header h6 {
    color: #495057;
    font-weight: 600;
}

/* 表格紧凑样式 */
.table-compact {
    font-size: 13px;
}

.table-compact th {
    padding: 8px;
    background: #f8f9fa;
    font-weight: 600;
    font-size: 12px;
}

.table-compact td {
    padding: 8px;
}

/* 徽章样式 */
.bg-sm {
    font-size: 10px;
    padding: 2px 6px;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 精简的工具栏 -->
    <div class="compact-toolbar d-flex justify-content-between align-items-center mb-3">
        <div class="d-flex align-items-center">
            <h4 class="mb-0 me-3">📦 库存详情</h4>
            <span class="badge bg-info">{{ inventory.ingredient.name }}</span>
        </div>
        <div>
            <a href="{{ url_for('inventory.index') }}" class="btn btn-primary btn-sm">
                <i class="fas fa-arrow-left"></i> 返回库存列表
            </a>
        </div>
    </div>

    <!-- 食材基本信息卡片 -->
    <div class="card mb-3">
        <div class="card-header py-2">
            <h5 class="card-title mb-0">🥬 食材基本信息</h5>
        </div>
        <div class="card-body p-3">
            <div class="row">
                <div class="col-md-8">
                    <!-- 突出显示食材信息 -->
                    <div class="ingredient-highlight-section mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label">食材名称</label>
                                    <div class="info-value ingredient-name">{{ inventory.ingredient.name }}</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="info-item">
                                    <label class="info-label">食材类别</label>
                                    <div class="info-value">
                                        <span class="badge bg-secondary">{{ inventory.ingredient.category.name if inventory.ingredient.category else '未分类' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 存储信息 -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">仓库</label>
                                <div class="info-value">{{ inventory.warehouse.name }}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">存储位置</label>
                                <div class="info-value">
                                    {{ inventory.storage_location.name }}
                                    <small class="text-muted">({{ inventory.storage_location.location_code }})</small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">批次号</label>
                                <div class="info-value">
                                    <code class="batch-number">{{ inventory.batch_number }}</code>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="info-item">
                                <label class="info-label">备注</label>
                                <div class="info-value">{{ inventory.notes or '-' }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4">
                    <!-- 库存状态卡片 -->
                    <div class="inventory-status-card">
                        <div class="status-header">
                            <h6 class="mb-2">📊 库存状态</h6>
                        </div>

                        <div class="status-item">
                            <label class="status-label">当前库存</label>
                            <div class="status-value quantity-highlight">
                                {{ inventory.quantity }} <span class="unit">{{ inventory.unit }}</span>
                            </div>
                        </div>

                        <div class="status-item">
                            <label class="status-label">生产日期</label>
                            <div class="status-value">{{ inventory.production_date|format_datetime('%Y-%m-%d') }}</div>
                        </div>

                        <div class="status-item">
                            <label class="status-label">过期日期</label>
                            <div class="status-value">
                                {{ inventory.expiry_date|format_datetime('%Y-%m-%d') }}
                                {% if inventory.status == '已过期' %}
                                    <br><span class="badge bg-danger bg-sm">已过期</span>
                                {% elif inventory.status == '临期' %}
                                    <br><span class="badge bg-warning bg-sm">临期</span>
                                {% endif %}
                            </div>
                        </div>

                        <div class="status-item">
                            <label class="status-label">状态</label>
                            <div class="status-value">
                                {% if inventory.status == '正常' %}
                                <span class="badge bg-success">正常</span>
                                {% elif inventory.status == '待检' %}
                                <span class="badge bg-warning">待检</span>
                                {% elif inventory.status == '冻结' %}
                                <span class="badge bg-info">冻结</span>
                                {% elif inventory.status == '已过期' %}
                                <span class="badge bg-danger">已过期</span>
                                {% elif inventory.status == '已用完' %}
                                <span class="badge bg-secondary">已用完</span>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 入库信息 -->
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">📥 入库信息</h6>
        </div>
                        <div class="card-body">
                            {% if stock_in_items %}
                            <div class="table-responsive">
                                <table class="table table-compact table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>入库单号</th>
                                            <th>入库日期</th>
                                            <th>入库类型</th>
                                            <th>入库数量</th>
                                            <th>操作人</th>
                                            <th>入库状态</th>
                                            <th>质量状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_in_items %}
                                        <tr>
                                            <td>
                                                {% if item.stock_in %}
                                                <a href="{{ url_for('stock_in.view', id=item.stock_in.id) }}">
                                                    {{ item.stock_in.stock_in_number }}
                                                </a>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>{{ item.stock_in.stock_in_date|format_datetime('%Y-%m-%d') if item.stock_in and item.stock_in.stock_in_date else '-' }}</td>
                                            <td>{{ item.stock_in.stock_in_type if item.stock_in else '-' }}</td>
                                            <td>{{ item.quantity }} {{ item.unit }}</td>
                                            <td>{{ item.stock_in.operator.real_name if item.stock_in and item.stock_in.operator else '-' }}</td>
                                            <td>
                                                {% if item.stock_in and item.stock_in.status == '已入库' %}
                                                <span class="badge bg-success">已入库</span>
                                                {% elif item.stock_in and item.stock_in.status == '待审核' %}
                                                <span class="badge bg-warning">待审核</span>
                                                {% elif item.stock_in and item.stock_in.status == '已审核' %}
                                                <span class="badge bg-info">已审核</span>
                                                {% elif item.stock_in and item.stock_in.status == '已取消' %}
                                                <span class="badge bg-danger">已取消</span>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if item.quality_status == '良好' %}
                                                <span class="badge bg-success">良好</span>
                                                {% elif item.quality_status == '一般' %}
                                                <span class="badge bg-warning">一般</span>
                                                {% elif item.quality_status == '较差' %}
                                                <span class="badge bg-danger">较差</span>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('stock_in.view', id=item.stock_in.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 未找到该批次号 <strong>{{ inventory.batch_number }}</strong> 的入库记录。
                            </div>
                            {% endif %}
                        </div>
                    </div>

    <!-- 供应商信息 -->
    {% if inventory.supplier %}
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">🏢 供应商信息</h6>
        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-compact table-bordered">
                                        <tr>
                                            <th class="w-30">供应商名称</th>
                                            <td>{{ inventory.supplier.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>法定代表人</th>
                                            <td>{{ inventory.supplier.legal_representative or '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">联系电话</th>
                                            <td>{{ inventory.supplier.phone if inventory.supplier.phone else '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>地址</th>
                                            <td>{{ inventory.supplier.address if inventory.supplier.address else '-' }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

    <!-- 检验检疫证明 -->
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">📋 检验检疫证明</h6>
        </div>
                        <div class="card-body">
                            {% if certificates %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>证书类型</th>
                                            <th>证书编号</th>
                                            <th>发证日期</th>
                                            <th>证书文件</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for cert in certificates %}
                                        <tr>
                                            <td>{{ cert.document_type }}</td>
                                            <td>{{ cert.certificate_number or cert.document_no or '-' }}</td>
                                            <td>{{ cert.issue_date|format_datetime('%Y-%m-%d') if cert.issue_date else '-' }}</td>
                                            <td>
                                                {% if cert.file_path %}
                                                <a href="{{ url_for('static', filename=cert.file_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-file-pdf"></i> 查看文件
                                                </a>
                                                {% elif cert.document_path %}
                                                <a href="{{ url_for('static', filename=cert.document_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-file-pdf"></i> 查看文件
                                                </a>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>{{ cert.notes or cert.remark or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 未找到该批次号 <strong>{{ inventory.batch_number }}</strong> 的检验检疫证明。
                            </div>
                            {% endif %}
                        </div>
                    </div>

    <!-- 食材溯源信息 -->
    {% if material_batch %}
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">🔍 食材溯源信息</h6>
        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">批次编号</th>
                                            <td>{{ material_batch.batch_number }}</td>
                                        </tr>
                                        <tr>
                                            <th>食材名称</th>
                                            <td>{{ material_batch.ingredient.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>供应商</th>
                                            <td>{{ material_batch.supplier.name }}</td>
                                        </tr>
                                        <tr>
                                            <th>生产日期</th>
                                            <td>{{ material_batch.production_date|format_datetime('%Y-%m-%d') }}</td>
                                        </tr>
                                        <tr>
                                            <th>过期日期</th>
                                            <td>{{ material_batch.expiry_date|format_datetime('%Y-%m-%d') }}</td>
                                        </tr>
                                    </table>
                                </div>
                                <div class="col-md-6">
                                    <table class="table table-bordered">
                                        <tr>
                                            <th class="w-30">生产批号</th>
                                            <td>{{ material_batch.production_batch_no or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>产地信息</th>
                                            <td>{{ material_batch.origin_place or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>检验编号</th>
                                            <td>{{ material_batch.inspection_no or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>合格证编号</th>
                                            <td>{{ material_batch.certificate_no or '-' }}</td>
                                        </tr>
                                        <tr>
                                            <th>状态</th>
                                            <td>{{ material_batch.status }}</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

    <!-- 溯源文档 -->
    {% if trace_documents %}
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">📄 溯源文档</h6>
        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>文档类型</th>
                                            <th>文档编号</th>
                                            <th>上传时间</th>
                                            <th>上传人</th>
                                            <th>文档</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for doc in trace_documents %}
                                        <tr>
                                            <td>{{ doc.document_type }}</td>
                                            <td>{{ doc.document_no or '-' }}</td>
                                            <td>{{ doc.upload_time|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ doc.uploader.real_name if doc.uploader else '-' }}</td>
                                            <td>
                                                {% if doc.document_path %}
                                                <a href="{{ url_for('static', filename=doc.document_path) }}" target="_blank" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-file-pdf"></i> 查看文件
                                                </a>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>{{ doc.remark or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    {% endif %}

    <!-- 出库记录 -->
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">📤 出库记录</h6>
        </div>
                        <div class="card-body">
                            {% if stock_out_items %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>出库单号</th>
                                            <th>出库日期</th>
                                            <th>出库类型</th>
                                            <th>出库数量</th>
                                            <th>领用人</th>
                                            <th>领用部门</th>
                                            <th>操作人</th>
                                            <th>出库状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for item in stock_out_items %}
                                        <tr>
                                            <td>
                                                {% if item.stock_out %}
                                                <a href="{{ url_for('stock_out.view', id=item.stock_out.id) }}">
                                                    {{ item.stock_out.stock_out_number }}
                                                </a>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>{{ item.stock_out.stock_out_date|format_datetime('%Y-%m-%d') if item.stock_out and item.stock_out.stock_out_date else '-' }}</td>
                                            <td>{{ item.stock_out.stock_out_type if item.stock_out else '-' }}</td>
                                            <td>{{ item.quantity }} {{ item.unit }}</td>
                                            <td>{{ item.stock_out.recipient if item.stock_out else '-' }}</td>
                                            <td>{{ item.stock_out.department if item.stock_out else '-' }}</td>
                                            <td>{{ item.stock_out.operator.real_name if item.stock_out and item.stock_out.operator else '-' }}</td>
                                            <td>
                                                {% if item.stock_out and item.stock_out.status == '已出库' %}
                                                <span class="badge bg-success">已出库</span>
                                                {% elif item.stock_out and item.stock_out.status == '待审核' %}
                                                <span class="badge bg-warning">待审核</span>
                                                {% elif item.stock_out and item.stock_out.status == '已审核' %}
                                                <span class="badge bg-info">已审核</span>
                                                {% elif item.stock_out and item.stock_out.status == '已取消' %}
                                                <span class="badge bg-danger">已取消</span>
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>
                                                <a href="{{ url_for('stock_out.view', id=item.stock_out.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 未找到该批次号 <strong>{{ inventory.batch_number }}</strong> 的出库记录。
                            </div>
                            {% endif %}
                        </div>
                    </div>

    <!-- 批次流水 -->
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">📊 批次流水</h6>
        </div>
                        <div class="card-body">
                            {% if batch_flows %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>流水类型</th>
                                            <th>流向</th>
                                            <th>数量</th>
                                            <th>单位</th>
                                            <th>关联单据</th>
                                            <th>操作人</th>
                                            <th>流水日期</th>
                                            <th>备注</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for flow in batch_flows %}
                                        <tr>
                                            <td>{{ flow.flow_type }}</td>
                                            <td>
                                                {% if flow.flow_direction == '增加' %}
                                                <span class="text-success">{{ flow.flow_direction }}</span>
                                                {% else %}
                                                <span class="text-danger">{{ flow.flow_direction }}</span>
                                                {% endif %}
                                            </td>
                                            <td>{{ flow.quantity }}</td>
                                            <td>{{ flow.unit }}</td>
                                            <td>
                                                {% if flow.related_id and flow.related_type %}
                                                {% if flow.related_type == '入库单' %}
                                                <a href="{{ url_for('stock_in.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                {% elif flow.related_type == '出库单' %}
                                                <a href="{{ url_for('stock_out.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                {% elif flow.related_type == '消耗计划' %}
                                                <a href="{{ url_for('consumption_plan.view', id=flow.related_id) }}">{{ flow.related_type }}#{{ flow.related_id }}</a>
                                                {% else %}
                                                {{ flow.related_type }}#{{ flow.related_id }}
                                                {% endif %}
                                                {% else %}
                                                -
                                                {% endif %}
                                            </td>
                                            <td>{{ flow.operator.real_name if flow.operator else '-' }}</td>
                                            <td>{{ flow.flow_date|format_datetime('%Y-%m-%d %H:%M') }}</td>
                                            <td>{{ flow.remark or '-' }}</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 未找到该批次号 <strong>{{ inventory.batch_number }}</strong> 的批次流水记录。
                            </div>
                            {% endif %}
                        </div>
                    </div>

    <!-- 关联消耗计划 -->
    <div class="card mb-3">
        <div class="card-header py-2 bg-light">
            <h6 class="card-title mb-0">🍽️ 关联消耗计划</h6>
                        </div>
                        <div class="card-body">
                            {% if consumption_plans %}
                            <div class="table-responsive">
                                <table class="table table-bordered table-striped">
                                    <thead>
                                        <tr>
                                            <th>消耗计划ID</th>
                                            <th>消耗日期</th>
                                            <th>餐次</th>
                                            <th>用餐人数</th>
                                            <th>区域</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for plan in consumption_plans %}
                                        <tr>
                                            <td>{{ plan.id }}</td>
                                            <td>{{ plan.consumption_date|format_datetime('%Y-%m-%d') }}</td>
                                            <td>{{ plan.meal_type }}</td>
                                            <td>{{ plan.diners_count }}</td>
                                            <td>{{ plan.menu_plan.area.name if plan.menu_plan and plan.menu_plan.area else '-' }}</td>
                                            <td>
                                                <a href="{{ url_for('consumption_plan.view', id=plan.id) }}" class="btn btn-sm btn-info">
                                                    <i class="fas fa-eye"></i> 查看
                                                </a>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            {% else %}
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> 未找到该批次号 <strong>{{ inventory.batch_number }}</strong> 的关联消耗计划。
                            </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
