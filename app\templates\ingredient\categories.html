{% extends 'base.html' %}

{% block title %}食材分类管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-sitemap text-primary me-2"></i>食材分类管理
            </h2>
            <p class="text-muted mb-0">管理食材分类层级结构</p>
        </div>
        <div>
            <a href="{{ url_for('ingredient_category.create') }}" class="btn btn-primary">
                <i class="fas fa-plus me-1"></i>添加分类
            </a>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 分类树视图 -->
        <div class="col-lg-5 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tree text-success me-2"></i>分类树结构
                    </h5>
                </div>
                <div class="card-body">
                    <div id="category-tree" style="min-height: 300px;"></div>
                </div>
            </div>
        </div>

        <!-- 分类列表 -->
        <div class="col-lg-7 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list text-info me-2"></i>分类列表
                        </h5>
                        <small class="text-muted">共 {{ categories|length }} 个分类</small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th style="width: 60px;">ID</th>
                                    <th>分类名称</th>
                                    <th>父分类</th>
                                    <th>描述</th>
                                    <th class="text-center" style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr>
                                    <td>
                                        <span class="badge bg-light">{{ category.id }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-tag text-primary me-2"></i>
                                        <strong>{{ category.name }}</strong>
                                    </td>
                                    <td>
                                        {% if category.parent %}
                                            <span class="badge bg-secondary">{{ category.parent.name }}</span>
                                        {% else %}
                                            <span class="text-muted">顶级分类</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ category.description or '暂无描述' }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('ingredient_category.edit', id=category.id) }}"
                                               class="btn btn-outline-primary"
                                               title="编辑分类">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button class="btn btn-outline-danger delete-btn"
                                                    data-id="{{ category.id }}"
                                                    title="删除分类">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                            <p class="mb-1">暂无分类数据</p>
                                            <small>点击上方"添加分类"按钮创建第一个分类</small>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>确认删除
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                <h6 class="mb-2">确定要删除这个食材分类吗？</h6>
                <p class="text-muted mb-0">此操作不可逆，请谨慎操作。</p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-danger px-4" id="confirmDelete">
                    <i class="fas fa-check me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* 基于系统框架的简单样式增强 */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

#category-tree {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

.badge {
    font-size: 0.75rem;
}

/* 响应式优化 */
@d-flex (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .col-lg-5, .col-lg-7 {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jstree/jstree.min.js') }}"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/jstree/themes/default/style.min.css') }}" />
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化分类树
        var categoryTree = {{  category_tree|safe  }};

        function transformData(data) {
            return data.map(function(item) {
                var node = {
                    id: item.id.toString(),
                    text: item.name,
                    state: { opened: true }
                };

                if (item.children && item.children.length > 0) {
                    node.children = transformData(item.children);
                }

                return node;
            });
        }

        $('#category-tree').jstree({
            'core': {
                'data': transformData(categoryTree)
            }
        });

        // 删除功能
        var deleteId = null;

        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                $.ajax({
                    url: '{{ url_for("ingredient_category.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    }
                });
            }
        });
    });
</script>
{% endblock %}
