{% extends 'base.html' %}

{% block title %}食堂管理仪表盘 - {{ super() }}{% endblock %}

{% block styles %}
{{ super() }}
<link rel="stylesheet" href="{{ url_for('static', filename='css/process_navigation.css') }}">
<style nonce="{{ csp_nonce }}">
    .dashboard-card {
        transition: all 0.3s;
        height: 100%;
    }
    .dashboard-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .menu-card {
        height: 100%;
        transition: all 0.3s ease;
    }
    .menu-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .menu-item {
        border-bottom: 1px solid #f8f9fa;
        padding: 10px 0;
        transition: background-color 0.2s ease;
    }
    .menu-item:last-child {
        border-bottom: none;
    }
    .menu-item:hover {
        background-color: rgba(0,123,255,0.05);
        border-radius: 4px;
        margin: 0 -8px;
        padding: 10px 8px;
    }

    /* 菜单卡片头部样式 */
    .menu-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        font-weight: 600;
    }

    .menu-card .card-header.breakfast-header {
        background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    }

    .menu-card .card-header.lunch-header {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    }

    .menu-card .card-header.dinner-header {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    }

    /* 菜谱项目样式 */
    .recipe-badge {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-size: 0.7rem;
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
    }
    .status-badge {
        font-size: 0.8rem;
    }
    .section-title {
        border-start: 4px solid var(--primary);
        padding-left: 10px;
        margin-bottom: 20px;
    }
    .quick-actions .btn {
        margin-bottom: 10px;
    }
    .function-card {
        transition: all 0.3s;
        cursor: pointer;
        height: 100%;
    }
    .function-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0,0,0,0.1);
    }
    .function-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }
    .companion-card {
        border-bottom: 1px solid #eee;
        padding: 10px 0;
    }
    .companion-card:last-child {
        border-bottom: none;
    }
    .qr-code-container {
        text-align: center;
        margin: 15px 0;
    }
    .qr-code-img {
        max-width: 150px;
        margin: 0 auto;
    }
    /* 加载状态样式 */
    .loading-overlay {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 1000;
        border-radius: 0.25rem;
    }
    .loading-spinner {
        width: 3rem;
        height: 3rem;
    }
    .card {
        position: relative;
    }
    .error-message {
        color: #dc3545;
        text-align: center;
        padding: 10px;
    }
    .retry-button {
        display: block;
        margin: 10px auto;
    }

    /* 通知消息和待办事项样式 */
    .border-start-primary {
        border-start: 0.25rem solid #4e73df !important;
    }
    .border-start-success {
        border-start: 0.25rem solid #1cc88a !important;
    }
    .border-start-danger {
        border-start: 0.25rem solid #e74a3b !important;
    }
    .border-start-info {
        border-start: 0.25rem solid #36b9cc !important;
    }

    /* 增大通知消息字体 */
    .notification-item {
        font-size: 1.1rem;
        line-height: 1.5;
        font-weight: 500;
    }

    /* 增大待办事项字体 */
    .todo-item {
        font-size: 1rem;
        line-height: 1.4;
        font-weight: 500;
    }

    /* 增大标题字体 */
    .text-xs {
        font-size: 0.9rem !important;
        font-weight: 600;
    }

    /* 增大供应链状态卡片内容字体 */
    .supply-chain-content {
        font-size: 1rem;
        font-weight: 500;
    }

    .supply-chain-content .small {
        font-size: 0.95rem !important;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-gray-300 {
        color: #dddfeb !important;
    }

    .shadow {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
    }

    .py-2 {
        padding-top: 0.5rem !important;
        padding-bottom: 0.5rem !important;
    }

    .g-0 {
        margin-right: 0;
        margin-left: 0;
    }

    .g-0 > .col,
    .g-0 > [class*="col-"] {
        padding-right: 0;
        padding-left: 0;
    }

    /* 供应链状态卡片动画 */
    .card:hover {
        transform: translateY(-2px);
        transition: all 0.3s ease;
    }

    /* 进度条样式 */
    .progress {
        background-color: #f8f9fc;
    }

    .bg-warning {
        background-color: #f6c23e !important;
    }
</style>
{% endblock %}

{% block content %}
<!-- 导入流程导航组件 -->
{% from 'components/process_navigation.html' import process_navigation %}

<!-- 新用户欢迎信息 -->
{% if is_new_user %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-success alert-dismissible fade show" style="background: linear-gradient(135deg, #28a745, #20c997); border: none; color: white;">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                <div class="d-flex align-items-center">
                    <div class="me-3">
                        <i class="fas fa-graduation-cap fa-3x"></i>
                    </div>
                    <div>
                        <h4 class="alert-heading mb-2">🎉 欢迎加入校园餐智慧食堂平台！</h4>
                        <p class="mb-2">恭喜您成功创建 <strong>{{ current_user.area.name }}</strong> 的管理账号！</p>
                        <p class="mb-2">您现在拥有完整的食堂管理权限，可以开始使用以下功能：</p>
                        <div class="row mt-3">
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle me-2"></i>日常工作日志管理</li>
                                    <li><i class="fas fa-check-circle me-2"></i>食品安全检查记录</li>
                                    <li><i class="fas fa-check-circle me-2"></i>陪餐记录管理</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle me-2"></i>菜单计划制定</li>
                                    <li><i class="fas fa-check-circle me-2"></i>食品溯源管理</li>
                                    <li><i class="fas fa-check-circle me-2"></i>留样记录管理</li>
                                </ul>
                            </div>
                            <div class="col-md-4">
                                <ul class="list-unstyled mb-0">
                                    <li><i class="fas fa-check-circle me-2"></i>仓库库存管理</li>
                                    <li><i class="fas fa-check-circle me-2"></i>采购订单管理</li>
                                    <li><i class="fas fa-check-circle me-2"></i>供应商管理</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mt-3">
                            <button class="btn btn-light btn-sm me-2" data-onclick="startUserGuide()">
                                <i class="fas fa-graduation-cap me-1"></i>开始系统引导
                            </button>
                            <a href="{{ url_for('daily_management.logs') }}" class="btn btn-outline-light btn-sm me-2">
                                <i class="fas fa-play me-1"></i>创建工作日志
                            </a>
                            <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-outline-light btn-sm me-2">
                                <i class="fas fa-calendar-alt me-1"></i>制定菜单计划
                            </a>
                            <a href="{{ url_for('warehouse.index') }}" class="btn btn-outline-light btn-sm">
                                <i class="fas fa-warehouse me-1"></i>查看仓库管理
                            </a>
                        </div>
                        <div class="mt-2">
                            <small class="text-light">
                                <i class="fas fa-info-circle me-1"></i>
                                系统已为您自动创建了默认仓库和三个储存位置（储存室、冷藏区、冷冻区），您可以立即开始使用库存管理功能！
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
{% endif %}

<!-- 通知消息和今日待办 -->
<div class="row mb-4">
    <div class="col-md-8">
        <!-- 通知消息区域 -->
        <div class="card border-start-primary shadow h-100 py-2">
            <div class="card-body">
                <div class="row g-0 align-items-center">
                    <div class="col me-2">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">
                            <i class="fas fa-bell me-1"></i>通知消息
                        </div>
                        <div class="h5 mb-0 fw-bold text-gray-800">
                            <div class="notification-item mb-2">
                                <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                <span class="text-warning">库存预警：</span>大米库存不足，请及时补充
                            </div>
                            <div class="notification-item mb-2">
                                <i class="fas fa-truck text-info me-2"></i>
                                <span class="text-info">采购订单：</span>今日有3个订单待确认收货
                            </div>
                            <div class="notification-item">
                                <i class="fas fa-clipboard-check text-success me-2"></i>
                                <span class="text-success">检查提醒：</span>晨检记录已完成，午检待进行
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-bell fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <!-- 今日待办区域 -->
        <div class="card border-start-success shadow h-100 py-2">
            <div class="card-body">
                <div class="row g-0 align-items-center">
                    <div class="col me-2">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">
                            <i class="fas fa-tasks me-1"></i>今日待办
                        </div>
                        <div class="h6 mb-0 fw-bold text-gray-800" id="todo-container">
                            <div class="text-center text-muted">
                                <i class="fas fa-spinner fa-spin"></i> 加载待办事项...
                            </div>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tasks fa-2x text-gray-300"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 供应链状态概览 -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>供应链状态概览
                </h5>
                <div>
                    <button id="refreshDashboard" class="btn btn-info btn-sm">
                        <i class="fas fa-sync-alt"></i> 刷新数据
                    </button>
                    <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-clipboard-list"></i> 日常管理
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card border-start-danger shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-danger text-uppercase mb-1">采购订单</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">
                                            <div class="supply-chain-content">
                                                <div>待确认: <span class="text-warning" id="purchase-pending">加载中...</span></div>
                                                <div>已完成: <span class="text-success" id="purchase-completed">加载中...</span></div>
                                                <div>总金额: <span class="text-primary" id="purchase-total">加载中...</span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-start-success shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-success text-uppercase mb-1">食材入库</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">
                                            <div class="supply-chain-content">
                                                <div>今日入库: <span class="text-success" id="stock-in-today">加载中...</span></div>
                                                <div>待检验: <span class="text-warning" id="stock-in-pending">加载中...</span></div>
                                                <div>合格率: <span class="text-success" id="stock-in-quality">加载中...</span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-start-primary shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">消耗计划</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">
                                            <div class="supply-chain-content">
                                                <div>今日计划: <span class="text-primary" id="consumption-plan">加载中...</span></div>
                                                <div>执行率: <span class="text-success" id="consumption-rate">加载中...</span></div>
                                                <div>预计消耗: <span class="text-info" id="consumption-cost">加载中...</span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-calculator fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card border-start-info shadow h-100 py-2">
                            <div class="card-body">
                                <div class="row g-0 align-items-center">
                                    <div class="col me-2">
                                        <div class="text-xs fw-bold text-info text-uppercase mb-1">食材出库</div>
                                        <div class="h5 mb-0 fw-bold text-gray-800">
                                            <div class="supply-chain-content">
                                                <div>今日出库: <span class="text-info" id="stock-out-today">加载中...</span></div>
                                                <div>待出库: <span class="text-warning" id="stock-out-pending">加载中...</span></div>
                                                <div>库存周转: <span class="text-success" id="stock-out-turnover">加载中...</span></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-auto">
                                        <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 食堂管理流程导航 -->
<div class="row mb-4">
    <div class="col-12">
        {{ process_navigation(
            menu_plan=menu_plan,
            purchase_plan=purchase_plan,
            storage_in=storage_in,
            consumption_plan=consumption_plan,
            storage_out=storage_out,
            inventory=inventory,
            samples=samples,
            tracing=tracing,
            progress_percentage=progress_percentage,
            today_tasks=today_tasks,
            available_routes=[]
        ) }}
    </div>
</div>

<!-- 日常管理六大功能 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">日常管理功能</h4>
    </div>
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="row" id="daily-management-functions">
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="{{ url_for('daily_management.logs') }}" class="text-decoration-none">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-clipboard-list"></i></div>
                                    <h5>工作日志</h5>
                                    <p class="text-muted small mt-2">记录日常工作</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="text-decoration-none">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-clipboard-check"></i></div>
                                    <h5>检查记录</h5>
                                    <p class="text-muted small mt-2">食品安全检查</p>
                                    <span class="badge bg-info mt-2">
                                        <i class="fas fa-qrcode"></i> 支持扫码上传
                                    </span>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none companion-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-utensils"></i></div>
                                    <h5>陪餐记录</h5>
                                    <p class="text-muted small mt-2">领导陪餐记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none training-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-chalkboard-teacher"></i></div>
                                    <h5>培训记录</h5>
                                    <p class="text-muted small mt-2">员工培训记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none event-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-calendar-day"></i></div>
                                    <h5>特殊事件</h5>
                                    <p class="text-muted small mt-2">特殊事件记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 col-sm-4 mb-4">
                        <a href="#" class="text-decoration-none issue-entry-link">
                            <div class="card function-card text-center">
                                <div class="card-body">
                                    <div class="function-icon"><i class="fas fa-exclamation-triangle"></i></div>
                                    <h5>问题记录</h5>
                                    <p class="text-muted small mt-2">问题跟踪记录</p>
                                </div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 今日菜单 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">今日菜单</h4>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header breakfast-header">
                <h5 class="mb-0">
                    <i class="fas fa-sun me-2"></i>早餐
                </h5>
            </div>
            <div class="card-body" id="breakfast-menu">
                {% if today_menu and today_menu['早餐'] %}
                    <div class="mb-2">
                        <span class="badge badge-{% if today_menu['早餐'].status == '已执行' %}success{% elif unified_status == '已发布' %}primary{% else %}secondary{% endif %} status-badge">{{ today_menu['早餐'].status }}</span>
                        {% if today_menu['早餐'].expected_diners %}
                            <span class="ms-2">预计就餐人数: {{ today_menu['早餐'].expected_diners }}</span>
                        {% endif %}
                    </div>
                    {% if today_menu['早餐'].recipes %}
                        <div class="mt-3">
                            {% for recipe in today_menu['早餐'].recipes %}
                                <div class="menu-item">
                                    <div class="d-flex justify-content-between">
                                        <div>{{ recipe.name }}</div>
                                        {% if recipe.quantity %}
                                            <div>{{ recipe.quantity }} 份</div>
                                        {% endif %}
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% else %}
                        <div class="text-center py-2"><p class="text-muted">暂无菜品</p></div>
                    {% endif %}
                {% else %}
                    <div class="text-center py-4">
                        <p class="text-muted">暂无早餐菜单</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header lunch-header">
                <h5 class="mb-0">
                    <i class="fas fa-utensils me-2"></i>午餐
                </h5>
            </div>
            <div class="card-body" id="lunch-menu">
                <div class="text-center py-4">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2 text-muted">加载中...</span>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card menu-card mb-4">
            <div class="card-header dinner-header">
                <h5 class="mb-0">
                    <i class="fas fa-moon me-2"></i>晚餐
                </h5>
            </div>
            <div class="card-body" id="dinner-menu">
                <div class="text-center py-4">
                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <span class="ms-2 text-muted">加载中...</span>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 陪餐记录和二维码 -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">最近陪餐记录</h5>
                <a href="{{ url_for('daily_management.index') }}" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body p-0" id="recent-companions">
                <div class="table-responsive">
                    <table class="table table-sm table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th class="w-15">陪餐人</th>
                                <th style="width: 10%;">角色</th>
                                <th style="width: 10%;">餐次</th>
                                <th style="width: 20%;">陪餐时间</th>
                                <th class="w-15">口味评分</th>
                                <th class="w-15">卫生评分</th>
                                <th class="w-15">服务评分</th>
                            </tr>
                        </thead>
                        <tbody id="companions-table-body">
                            <tr>
                                <td colspan="7" class="text-center py-3">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">加载中...</span>
                                    </div>
                                    <span class="ms-2 text-muted">加载中...</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="card-footer text-center">
                <a href="#" class="btn btn-info" id="add-companion-btn">
                    <i class="fas fa-plus"></i> 添加陪餐记录
                </a>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">陪餐记录二维码</h5>
            </div>
            <div class="card-body text-center">
                <p>扫描下方二维码快速添加陪餐记录</p>
                <div class="qr-code-container">
                    <img src="{{ url_for('static', filename='img/qr-code-placeholder.png') }}" alt="陪餐记录二维码" class="qr-code-img" id="companion-qr-code">
                </div>
                <button class="btn btn-outline-primary mt-3" id="generate-qr-code-btn">
                    <i class="fas fa-qrcode"></i> 生成二维码
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 溯源和留样 -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header">
                <h5 class="mb-0">食品溯源</h5>
            </div>
            <div class="card-body">
                <p>通过食品溯源系统，可以追踪食材从采购到加工的全过程。</p>
                <div class="text-center py-3">
                    <i class="fas fa-search-location fa-4x text-primary mb-3"></i>
                    <p>扫描食品包装上的二维码，即可查看食品详细信息</p>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('food_trace.index') }}" class="btn btn-primary">
                        <i class="fas fa-qrcode"></i> 扫码溯源
                    </a>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card h-100">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">食品留样</h5>
                <span class="badge bg-primary" id="food-samples-count">-</span>
            </div>
            <div class="card-body">
                <p>今日食品留样情况统计</p>
                <div class="text-center py-3">
                    <i class="fas fa-vial fa-4x text-success mb-3"></i>
                    <p>一键完成食品留样记录，确保食品安全</p>
                </div>
                <div class="text-center mt-3">
                    <a href="{{ url_for('food_trace.one_click_sample') }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> 一键留样
                    </a>
                    <a href="{{ url_for('food_sample.index') }}" class="btn btn-outline-secondary ms-2">
                        <i class="fas fa-list"></i> 查看记录
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 快速操作 -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="section-title">快速操作</h4>
    </div>
    <div class="col-md-12">
        <div class="card">
            <div class="card-body quick-actions">
                <div class="row">
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-primary w-100" id="create-daily-log-btn">
                            <i class="fas fa-clipboard"></i> 创建今日工作日志
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-success w-100" id="add-inspection-btn">
                            <i class="fas fa-clipboard-check"></i> 添加检查记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('daily_management.scan_upload_entry') }}" class="btn btn-info w-100" style="background: linear-gradient(135deg, #36b9cc, #1cc88a); position: relative; overflow: hidden;">
                            <i class="fas fa-qrcode"></i> 扫码上传
                            <span class="badge bg-light" style="position: absolute; top: -8px; right: -8px; border-radius: 50%; padding: 0.25rem; font-size: 0.6rem; box-shadow: 0 2px 5px rgba(0,0,0,0.2);">
                                <i class="fas fa-star"></i>
                            </span>
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-info w-100" id="add-companion-btn2">
                            <i class="fas fa-utensils"></i> 添加陪餐记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-warning w-100" id="add-issue-btn">
                            <i class="fas fa-exclamation-triangle"></i> 添加问题记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-secondary w-100" id="add-training-btn">
                            <i class="fas fa-chalkboard-teacher"></i> 添加培训记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="#" class="btn btn-danger w-100" id="add-event-btn">
                            <i class="fas fa-calendar-day"></i> 添加特殊事件
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('food_trace.one_click_sample') }}" class="btn btn-dark w-100">
                            <i class="fas fa-vial"></i> 添加留样记录
                        </a>
                    </div>
                    <div class="col-md-3 mb-2">
                        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-light w-100">
                            <i class="fas fa-calendar-alt"></i> 菜单计划管理
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

<!-- 引导模态框 -->
{% include 'guide/step_modal.html' %}

<!-- 场景选择模态框 -->
{% include 'guide/scenario_selection.html' %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/process_navigation.js') }}"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/user_guide.js') }}"></script>
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 绑定二维码生成按钮
        $('#generate-qr-code-btn').click(function() {
            generateCompanionQRCode();
        });

        // 绑定快速操作按钮
        bindQuickActionButtons();

        // 加载陪餐记录数据
        loadRecentCompanions();

        // 加载今日菜单数据
        loadTodayMenu();

        // 绑定功能图标点击事件
        bindFunctionCardClicks();

        // 绑定刷新按钮
        $('#refreshDashboard').click(function() {
            loadRecentCompanions();
            loadTodayMenu();
            loadSupplyChainStatus();
            loadNotifications();
            loadTodoItems();
        });

        // 加载供应链状态和通知
        loadSupplyChainStatus();
        loadNotifications();
        loadTodoItems();
    });

    // 启动用户引导
    function startUserGuide() {
        if (typeof userGuide !== 'undefined') {
            // 显示场景选择模态框
            $('#scenarioSelectionModal').modal('show');
        } else {
            console.error('用户引导系统未初始化');
        }
    }

    // 直接启动引导（跳过场景选择）
    function startGuideDirectly() {
        if (typeof userGuide !== 'undefined') {
            userGuide.showStep('welcome');
        } else {
            console.error('用户引导系统未初始化');
        }
    }

    // 生成陪餐记录二维码
    function generateCompanionQRCode() {
        try {
            // 显示加载状态
            var qrCodeImg = $('#companion-qr-code');
            var originalSrc = qrCodeImg.attr('src');
            qrCodeImg.attr('src', "{{ url_for('static', filename='img/loading.gif') }}");

            // 获取当前日期
            var today = new Date().toISOString().split('T')[0];

            // 使用外部服务生成二维码
            var qrData = encodeURIComponent(window.location.origin + "{{ url_for('daily_management.index') }}" + '?date=' + today);
            var qrServiceUrl = 'https://api.qrserver.com/v1/create-qr-code/?size=150x150&data=' + qrData;

            // 模拟API调用
            setTimeout(function() {
                qrCodeImg.attr('src', qrServiceUrl);
                alert('二维码生成成功');
            }, 500);
        } catch (e) {
            console.error('生成二维码失败:', e);
            $('#companion-qr-code').attr('src', originalSrc || "{{ url_for('static', filename='img/qr-code-placeholder.png') }}");
            alert('二维码生成失败');
        }
    }

    // 绑定快速操作按钮
    function bindQuickActionButtons() {
        // 创建今日工作日志
        $('#create-daily-log-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.logs') }}";
        });

        // 添加检查记录
        $('#add-inspection-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 检查记录二维码 - 已经改为直接链接到扫码上传入口
        // $('#inspection-qrcode-btn').click(function(e) {
        //     e.preventDefault();
        //     // 获取今天的日期
        //     var today = new Date().toISOString().split('T')[0];
        //     // 先跳转到日志页面，然后再跳转到检查记录页面
        //     window.location.href = "{{ url_for('daily_management.inspections_by_date', date_str='') }}" + today;
        // });

        // 添加陪餐记录
        $('#add-companion-btn, #add-companion-btn2').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加培训记录
        $('#add-training-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加特殊事件
        $('#add-event-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 添加问题记录
        $('#add-issue-btn').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });
    }

    // 加载最近陪餐记录
    function loadRecentCompanions() {
        $.ajax({
            url: '/api/v2/dining-companions/recent',
            type: 'GET',
            data: { limit: 5 },
            success: function(data) {
                var tbody = $('#companions-table-body');
                tbody.empty();

                if (data && data.length > 0) {
                    data.forEach(function(companion) {
                        var row = '<tr>';
                        row += '<td>' + (companion.name || '-') + '</td>';
                        row += '<td><span class="badge bg-secondary">' + (companion.role || '-') + '</span></td>';
                        row += '<td><span class="badge badge-' + getMealTypeBadgeClass(companion.meal_type) '">' + getMealTypeText(companion.meal_type) + '</span></td>';
                        row += '<td>' + (companion.date || '-') + ' ' + (companion.time || '-') + '</td>';
                        row += '<td>' + generateStarRating(companion.taste_rating) + '</td>';
                        row += '<td>' + generateStarRating(companion.hygiene_rating) + '</td>';
                        row += '<td>' + generateStarRating(companion.service_rating) + '</td>';
                        row += '</tr>';
                        tbody.append(row);
                    });
                } else {
                    tbody.append('<tr><td colspan="7" class="text-center py-3 text-muted">暂无陪餐记录</td></tr>');
                }
            },
            error: function(xhr, status, error) {
                var tbody = $('#companions-table-body');
                tbody.empty();
                var errorMessage = '加载失败，请稍后重试';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                tbody.append('<tr><td colspan="7" class="text-center py-3 text-danger">' + errorMessage + '</td></tr>');
                console.error('加载陪餐记录失败:', error);
            }
        });
    }

    // 生成星级评分HTML
    function generateStarRating(rating) {
        if (!rating || rating === 0) {
            return '<span class="text-muted">-</span>';
        }

        var stars = '';
        for (var i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars += '<i class="fas fa-star text-warning"></i>';
            } else {
                stars += '<i class="far fa-star text-muted"></i>';
            }
        }
        return stars;
    }

    // 获取餐次徽章样式
    function getMealTypeBadgeClass(mealType) {
        switch(mealType) {
            case 'breakfast': return 'info';
            case 'lunch': return 'success';
            case 'dinner': return 'warning';
            default: return 'secondary';
        }
    }

    // 获取餐次文本
    function getMealTypeText(mealType) {
        switch(mealType) {
            case 'breakfast': return '早餐';
            case 'lunch': return '午餐';
            case 'dinner': return '晚餐';
            default: return mealType || '-';
        }
    }

    // 加载今日菜单
    function loadTodayMenu() {
        // 显示加载状态
        showMenuLoading();

        $.ajax({
            url: '/api/v2/dashboard/today-menu',
            type: 'GET',
            success: function(response) {
                console.log('今日菜单API响应:', response);
                if (response.success && response.data) {
                    updateMenuDisplay(response.data);
                } else {
                    showMenuError('获取菜单失败: ' + (response.message || '未知错误'));
                }
            },
            error: function(xhr, status, error) {
                console.error('加载今日菜单失败:', status, error);
                showMenuError('网络错误，无法获取菜单');
            }
        });
    }

    // 显示菜单加载状态
    function showMenuLoading() {
        var loadingHtml = `
            <div class="text-center py-4">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <span class="ms-2 text-muted">加载中...</span>
            </div>
        `;
        $('#breakfast-menu, #lunch-menu, #dinner-menu').html(loadingHtml);
    }

    // 更新菜单显示
    function updateMenuDisplay(menuData) {
        // 更新早餐
        updateMealDisplay('breakfast', menuData['早餐']);
        // 更新午餐
        updateMealDisplay('lunch', menuData['午餐']);
        // 更新晚餐
        updateMealDisplay('dinner', menuData['晚餐']);
    }

    // 更新单个餐次显示
    function updateMealDisplay(mealId, mealData) {
        var containerId = mealId === 'breakfast' ? '#breakfast-menu' :
                         mealId === 'lunch' ? '#lunch-menu' : '#dinner-menu';
        var container = $(containerId);

        console.log('更新餐次显示:', mealId, mealData);

        if (!mealData || !mealData.recipes || mealData.recipes.length === 0) {
            var emptyHtml = `
                <div class="text-center py-4">
                    <i class="fas fa-utensils fa-2x text-muted mb-2"></i>
                    <p class="text-muted mb-1">暂无${getMealTypeName(mealId)}菜单</p>
                    <small class="text-muted">请在周菜单管理中安排菜谱</small>
                </div>
            `;
            container.html(emptyHtml);
            return;
        }

        var html = '<div class="mb-2">';

        // 显示状态徽章
        var status = mealData.status || '计划中';
        html += '<span class="badge badge-' + getStatusBadgeClass(status) ' status-badge">' + status + '</span>';

        // 显示预计就餐人数
        if (mealData.expected_diners) {
            html += '<span class="ms-2 small text-muted">预计就餐: ' + mealData.expected_diners + '人</span>';
        }
        html += '</div>';

        // 显示菜谱列表
        if (mealData.recipes.length > 0) {
            html += '<div class="mt-3">';
            mealData.recipes.forEach(function(recipe, index) {
                html += '<div class="menu-item">';
                html += '<div class="d-flex justify-content-between align-items-center">';
                html += '<div class="d-flex align-items-center">';
                html += '<span class="recipe-badge me-2">' + (index + 1) + '</span>';
                html += '<span class="font-weight-medium">' + recipe.name + '</span>';
                html += '</div>';
                if (recipe.quantity) {
                    html += '<div class="text-muted small">' + recipe.quantity + ' 份</div>';
                }
                html += '</div></div>';
            });
            html += '</div>';

            // 添加菜谱统计信息
            html += '<div class="mt-2 pt-2 border-top">';
            html += '<small class="text-muted">';
            html += '<i class="fas fa-list me-1"></i>共 ' + mealData.recipes.length + ' 道菜品';
            if (mealData.expected_diners) {
                html += ' | <i class="fas fa-users me-1"></i>' + mealData.expected_diners + ' 人用餐';
            }
            html += '</small>';
            html += '</div>';
        }

        container.html(html);
    }

    // 获取餐次中文名称
    function getMealTypeName(mealId) {
        switch(mealId) {
            case 'breakfast': return '早餐';
            case 'lunch': return '午餐';
            case 'dinner': return '晚餐';
            default: return '餐次';
        }
    }

    // 获取状态徽章样式
    function getStatusBadgeClass(status) {
        switch(status) {
            case '已执行': return 'success';
            case '已发布': return 'primary';
            case '计划中': return 'info';
            default: return 'secondary';
        }
    }

    // 显示菜单错误
    function showMenuError(message) {
        ['#breakfast-menu', '#lunch-menu', '#dinner-menu'].forEach(function(selector) {
            $(selector).html('<div class="text-center py-4"><p class="text-danger">' + message + '</p></div>');
        });
    }

    // 绑定功能图标点击事件
    function bindFunctionCardClicks() {
        // 陪餐记录入口
        $('.companion-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 培训记录入口
        $('.training-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 特殊事件入口
        $('.event-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });

        // 问题记录入口
        $('.issue-entry-link').click(function(e) {
            e.preventDefault();
            window.location.href = "{{ url_for('daily_management.index') }}";
        });
    }

    // 测试API函数
    function testAPIs() {
        console.log('开始测试API...');

        // 测试陪餐记录数据
        $.ajax({
            url: '/api/v2/test/companions',
            type: 'GET',
            success: function(data) {
                console.log('陪餐记录测试结果:', data);
                alert('陪餐记录测试成功！总数: ' + data.total_count + ' 条');
            },
            error: function(xhr, status, error) {
                console.error('陪餐记录测试失败:', status, error);
                alert('陪餐记录测试失败: ' + error);
            }
        });

        // 测试菜单数据
        $.ajax({
            url: '/api/v2/test/menu',
            type: 'GET',
            success: function(data) {
                console.log('菜单测试结果:', data);
                alert('菜单测试成功！总数: ' + data.total_count + ' 条');
            },
            error: function(xhr, status, error) {
                console.error('菜单测试失败:', status, error);
                alert('菜单测试失败: ' + error);
            }
        });
    }

    // 加载供应链状态
    function loadSupplyChainStatus() {
        $.ajax({
            url: '/api/v2/dashboard/supply-chain-status',
            type: 'GET',
            success: function(response) {
                console.log('供应链状态API响应:', response);
                if (response.success && response.data) {
                    updateSupplyChainDisplay(response.data);
                } else {
                    console.error('获取供应链状态失败:', response.message);
                    // 使用默认数据
                    updateSupplyChainDisplay(getDefaultSupplyChainData());
                }
            },
            error: function(xhr, status, error) {
                console.error('加载供应链状态失败:', status, error);
                // 使用默认数据
                updateSupplyChainDisplay(getDefaultSupplyChainData());
            }
        });
    }

    // 更新供应链状态显示
    function updateSupplyChainDisplay(supplyChainData) {
        // 更新采购订单数据
        $('#purchase-pending').text(supplyChainData.purchase_orders.pending + '单');
        $('#purchase-completed').text(supplyChainData.purchase_orders.completed + '单');
        $('#purchase-total').text('¥' + supplyChainData.purchase_orders.total_amount.toFixed(0));

        // 更新入库数据
        $('#stock-in-today').text(supplyChainData.stock_in.today_batches + '批次');
        $('#stock-in-pending').text(supplyChainData.stock_in.pending_inspection + '批次');
        $('#stock-in-quality').text(supplyChainData.stock_in.quality_rate + '%');

        // 更新消耗计划数据
        $('#consumption-plan').text(supplyChainData.consumption_plan.today_plans > 0 ? '已制定' : '未制定');
        $('#consumption-rate').text(supplyChainData.consumption_plan.execution_rate + '%');
        $('#consumption-cost').text('¥' + supplyChainData.consumption_plan.estimated_cost.toFixed(0));

        // 更新出库数据
        $('#stock-out-today').text(supplyChainData.stock_out.today_batches + '批次');
        $('#stock-out-pending').text(supplyChainData.stock_out.pending_batches + '批次');
        $('#stock-out-turnover').text(supplyChainData.stock_out.turnover_status);
    }

    // 获取默认供应链数据（当API失败时使用）
    function getDefaultSupplyChainData() {
        return {
            purchase_orders: {
                pending: 0,
                completed: 0,
                total_amount: 0
            },
            stock_in: {
                today_batches: 0,
                pending_inspection: 0,
                quality_rate: 0
            },
            consumption_plan: {
                today_plans: 0,
                execution_rate: 0,
                estimated_cost: 0
            },
            stock_out: {
                today_batches: 0,
                pending_batches: 0,
                turnover_status: '正常'
            }
        };
    }

    // 加载通知消息
    function loadNotifications() {
        $.ajax({
            url: '/api/v2/dashboard/notifications',
            type: 'GET',
            success: function(response) {
                console.log('通知消息API响应:', response);
                if (response.success && response.data) {
                    updateNotificationsDisplay(response.data);
                } else {
                    console.error('获取通知消息失败:', response.message);
                    // 使用默认通知
                    updateNotificationsDisplay(getDefaultNotifications());
                }
            },
            error: function(xhr, status, error) {
                console.error('加载通知消息失败:', status, error);
                // 使用默认通知
                updateNotificationsDisplay(getDefaultNotifications());
            }
        });
    }

    // 更新通知消息显示
    function updateNotificationsDisplay(notifications) {
        // 更新通知消息显示
        var notificationContainer = $('.notification-item').parent();
        notificationContainer.empty();

        notifications.forEach(function(notification, index) {
            var notificationHtml = `
                <div class="notification-item ${index < notifications.length - 1 ? 'mb-2' : ''}">
                    <i class="${notification.icon} text-${notification.color} me-2"></i>
                    <span class="text-${notification.color}">${notification.type}</span>${notification.message}
                </div>
            `;
            notificationContainer.append(notificationHtml);
        });

        // 更新待办事项进度（这里可以根据实际业务逻辑调整）
        updateTodoProgress();
    }

    // 加载待办事项
    function loadTodoItems() {
        $.ajax({
            url: '/api/v2/dashboard/todo-items',
            type: 'GET',
            success: function(response) {
                console.log('待办事项API响应:', response);
                if (response.success && response.data) {
                    updateTodoDisplay(response.data);
                } else {
                    console.error('获取待办事项失败:', response.message);
                    updateTodoDisplay(getDefaultTodoItems());
                }
            },
            error: function(xhr, status, error) {
                console.error('加载待办事项失败:', status, error);
                updateTodoDisplay(getDefaultTodoItems());
            }
        });
    }

    // 更新待办事项显示
    function updateTodoDisplay(todoItems) {
        var container = $('#todo-container');
        container.empty();

        if (!todoItems || todoItems.length === 0) {
            container.html('<div class="text-center text-muted">暂无待办事项</div>');
            return;
        }

        todoItems.forEach(function(item, index) {
            var itemHtml = '';

            if (item.status === 'progress') {
                // 有进度的任务
                itemHtml = `
                    <div class="todo-item ${index < todoItems.length - 1 ? 'mb-2' : ''}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>${item.title}</span>
                            <span class="badge ${item.badge_class}">${item.completed}/${item.total}</span>
                        </div>
                        <div class="progress mt-1" style="height: 4px;">
                            <div class="progress-bar bg-warning" role="progressbar" style="width: ${item.progress}%"></div>
                        </div>
                    </div>
                `;
            } else {
                // 普通状态任务
                itemHtml = `
                    <div class="todo-item ${index < todoItems.length - 1 ? 'mb-2' : ''}">
                        <div class="d-flex justify-content-between align-items-center">
                            <span>${item.title}</span>
                            <span class="badge ${item.badge_class}">${item.message}</span>
                        </div>
                    </div>
                `;
            }

            container.append(itemHtml);
        });
    }

    // 获取默认待办事项（当API失败时使用）
    function getDefaultTodoItems() {
        return [
            {
                title: '完成食材入库',
                status: 'none',
                message: '暂无任务',
                badge_class: 'bg-secondary'
            },
            {
                title: '准备出库计划',
                status: 'none',
                message: '暂无计划',
                badge_class: 'bg-secondary'
            },
            {
                title: '制定明日菜单',
                status: 'none',
                message: '暂无安排',
                badge_class: 'bg-secondary'
            }
        ];
    }

    // 更新待办事项进度（保留兼容性）
    function updateTodoProgress() {
        // 这个函数现在由 updateTodoDisplay 替代
        // 保留是为了兼容性，实际不再使用
    }

    // 获取默认通知消息（当API失败时使用）
    function getDefaultNotifications() {
        return [
            {
                icon: 'fas fa-info-circle',
                color: 'info',
                type: '系统状态：',
                message: '正在加载最新信息...'
            }
        ];
    }


</script>
{% endblock %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>